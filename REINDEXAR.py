import os
import sys
import traceback
import ctypes
import win32api
import psutil
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QListWidget, QScrollArea, QCheckBox, QWidget,
    QSizePolicy, QGraphicsDropShadowEffect
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QPropertyAnimation, QPoint
)
from PyQt6.QtGui import QColor
from APARIENCIA import (
    ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY,
    ACCENT_ENABLE_FLUENT, DWMWA_WINDOW_CORNER_PREFERENCE,
    DWMWCP_ROUND, is_windows_11_or_greater
)

class ReindexProgressDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        layout = QVBoxLayout(self)
        self.frame = QFrame()
        self.frame.setStyleSheet("""
            QFrame {
                background-color: rgba(45, 45, 44, 220);
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 30);
            }
        """)
        frame_layout = QVBoxLayout(self.frame)
        self.status_label = QLabel("Indexando archivos de video y música...")
        self.status_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(self.status_label)
        self.progress_list = QListWidget()
        self.progress_list.setStyleSheet("""
            QListWidget {
                background-color: rgba(0, 0, 0, 50);
                border: none;
                color: white;
                border-radius: 5px;
            }
        """)
        self.progress_list.setMinimumHeight(150)
        frame_layout.addWidget(self.progress_list)
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 5px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ef5350;
            }
        """)
        frame_layout.addWidget(self.cancel_button)
        layout.addWidget(self.frame)
        self.setFixedSize(400, 300)

    def add_progress_message(self, message):
        self.progress_list.addItem(message)
        self.progress_list.scrollToBottom()

class ReindexThread(QThread):
    def __init__(self, volumes, callback=None, progress_callback=None, incremental=True):
        super().__init__()
        self.volumes = volumes
        self.callback = callback
        self.progress_callback = progress_callback
        self.incremental = incremental
        self.running = True
        self.total_drives = len(volumes)
        self.current_drive_index = 0

    def run(self):
        try:
            # Reducir prioridad del proceso para no interferir con otras operaciones
            current_process = psutil.Process()
            current_process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            
            # Carpetas a excluir para acelerar el proceso
            excluded_folders = {
                '$recycle.bin', 'system volume information', 'pagefile.sys', 'hiberfil.sys',
                'swapfile.sys', 'windows', 'program files', 'program files (x86)', 
                'programdata', 'users\\default', 'users\\all users', 'users\\public',
                'temp', 'tmp', 'cache', '.git', '.svn', 'node_modules', '__pycache__',
                'recovery', 'system32', 'syswow64', 'winsxs'
            }
            
            # Buffer para escritura por lotes (más eficiente)
            buffer_size = 10000
            
            if self.incremental:
                # INDEXADO INCREMENTAL: Solo actualizar discos seleccionados
                print("Iniciando indexado incremental...")
                self._incremental_reindex()
            else:
                # INDEXADO COMPLETO: Reescribir todo el archivo
                print("Iniciando indexado completo...")
                self._full_reindex(excluded_folders, buffer_size)

        except Exception as e:
            print(f"Error fatal en indexación: {e}")
        finally:
            if self.progress_callback:
                self.progress_callback(1.0)
            if self.callback:
                self.callback()
    
    def _incremental_reindex(self):
        """Indexado incremental inteligente: detecta y actualiza solo los cambios reales"""
        try:
            # Leer el archivo existente y preservar datos de discos no seleccionados
            existing_data = {}
            existing_files_by_drive = {}  # Para comparar cambios
            drives_to_update = {drive_letter for _, drive_letter in self.volumes}
            
            if os.path.exists("INDEXADO.txt"):
                print("Leyendo archivo INDEXADO.txt existente para análisis de cambios...")
                with open("INDEXADO.txt", "r", encoding='utf-8') as f:
                    current_disk = None
                    current_data = []
                    current_files = set()
                    
                    for line in f:
                        line = line.strip()
                        if line.startswith("Disco:"):
                            # Guardar datos del disco anterior
                            if current_disk:
                                if current_disk not in drives_to_update:
                                    existing_data[current_disk] = current_data.copy()
                                else:
                                    existing_files_by_drive[current_disk] = current_files.copy()
                            
                            # Extraer letra del disco
                            current_disk = line.split('(')[-1].strip(')')
                            current_data = [line]  # Incluir la línea del disco
                            current_files = set()
                        elif current_disk and line and not line.startswith('='):
                            current_data.append(line)
                            if line:  # Solo archivos, no líneas vacías
                                current_files.add(line)
                    
                    # Guardar el último disco
                    if current_disk:
                        if current_disk not in drives_to_update:
                            existing_data[current_disk] = current_data.copy()
                        else:
                            existing_files_by_drive[current_disk] = current_files.copy()
            
            # Crear archivo temporal para escribir los datos actualizados
            temp_file = "INDEXADO_temp.txt"
            with open(temp_file, "w", encoding='utf-8', buffering=8192) as f:
                # Escribir primero los datos preservados (discos no seleccionados)
                for drive_letter, data in existing_data.items():
                    for line in data:
                        f.write(line + '\n')
                    f.write('\n')
                
                # Procesar discos seleccionados con detección de cambios
                for i, (drive_info, drive_letter) in enumerate(self.volumes):
                    if not self.running:
                        print("Proceso de indexación interrumpido por el usuario")
                        break
                    
                    print(f"Analizando cambios en {drive_info}...")
                    
                    # Obtener archivos existentes en el índice para este disco
                    existing_files = existing_files_by_drive.get(drive_letter, set())
                    
                    # Escanear archivos actuales del disco
                    current_files = self._scan_drive_files(drive_letter)
                    
                    # Detectar cambios
                    changes = self._detect_changes(existing_files, current_files, drive_letter)
                    
                    if changes['has_changes']:
                        print(f"Cambios detectados en {drive_info}:")
                        print(f"  - Archivos nuevos: {len(changes['added'])}")
                        print(f"  - Archivos eliminados: {len(changes['removed'])}")
                        print(f"  - Archivos sin cambios: {len(changes['unchanged'])}")
                        print(f"  - Total archivos actuales: {len(current_files)}")
                        
                        # Mostrar algunos ejemplos de archivos nuevos y eliminados para verificación
                        if changes['added']:
                            print("  - Ejemplos de archivos nuevos:")
                            for file in list(changes['added'])[:3]:  # Mostrar hasta 3 ejemplos
                                print(f"    + {file}")
                        
                        if changes['removed']:
                            print("  - Ejemplos de archivos eliminados:")
                            for file in list(changes['removed'])[:3]:  # Mostrar hasta 3 ejemplos
                                print(f"    - {file}")
                        
                        # Escribir el índice actualizado para este disco
                        self._write_updated_drive_index(f, drive_info, current_files)
                    else:
                        print(f"Sin cambios en {drive_info}, preservando índice existente")
                        # Escribir los datos existentes sin cambios
                        f.write(f"{drive_info}\n{'=' * 30}\n\n")
                        for file_path in sorted(existing_files):
                            f.write(f"{file_path}\n")
                        f.write("\n\n")
                    
                    # Actualizar progreso
                    progress = (i + 1) / self.total_drives
                    if self.progress_callback:
                        self.progress_callback(progress)
            
            # Reemplazar el archivo original con el temporal
            if os.path.exists("INDEXADO.txt"):
                os.remove("INDEXADO.txt")
            os.rename(temp_file, "INDEXADO.txt")
            
            print("Indexado incremental inteligente completado exitosamente")
            
        except Exception as e:
            print(f"Error en indexado incremental: {e}")
            traceback.print_exc()  # Imprimir el traceback completo para mejor diagnóstico
            # Limpiar archivo temporal si existe
            if os.path.exists("INDEXADO_temp.txt"):
                os.remove("INDEXADO_temp.txt")
            raise
    
    def _scan_drive_files(self, drive_letter):
        """Escanea los archivos de video y música de un disco"""
        current_files = set()
        excluded_folders = {
            '$recycle.bin', 'system volume information', 'pagefile.sys', 'hiberfil.sys',
            'swapfile.sys', 'windows', 'program files', 'program files (x86)', 
            'programdata', 'users\\default', 'users\\all users', 'users\\public',
            'temp', 'tmp', 'cache', '.git', '.svn', 'node_modules', '__pycache__',
            'recovery', 'system32', 'syswow64', 'winsxs'
        }
        
        # Extensiones de archivos de video
        video_extensions = {
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg',
            '.m4v', '.3gp', '.3g2', '.f4v', '.f4p', '.f4a', '.f4b', '.vob', '.ogv', 
            '.ogg', '.drc', '.gifv', '.mng', '.mts', '.m2ts', '.ts', '.mov', '.qt', 
            '.yuv', '.rm', '.rmvb', '.viv', '.asf', '.amv', '.m4p', '.m4v', '.mp2', 
            '.mpe', '.mpv', '.m2v', '.svi', '.divx', '.xvid', '.ssw', '.evo', '.ogm',
            '.dvr-ms', '.wtv', '.iso', '.m1v', '.m2v', '.mod', '.tod', '.tp', '.trp',
            '.bik', '.smk', '.swf', '.ifo', '.bup', '.mkv', '.mk3d', '.mka', '.mks'
        }
        
        # Extensiones de archivos de música
        music_extensions = {
            '.mp3', '.wav', '.ogg', '.flac', '.aac', '.wma', '.m4a', '.opus', '.alac',
            '.aiff', '.ape', '.au', '.mid', '.midi', '.pcm', '.ac3', '.amr', '.voc',
            '.mpc', '.tta', '.dts', '.gsm', '.wv', '.wvc', '.spx', '.ra', '.rm', '.oga',
            '.mp2', '.mp1', '.dsf', '.dff', '.mka', '.m3u', '.m3u8', '.pls', '.cue'
        }
        
        try:
            for root, dirs, files in os.walk(drive_letter + "\\"):
                if not self.running:
                    break
                
                # Filtrar directorios para no entrar en carpetas excluidas
                dirs[:] = [d for d in dirs if d.lower() not in excluded_folders]
                
                # Saltar carpetas del sistema y temporales
                root_lower = root.lower()
                if any(excluded in root_lower for excluded in excluded_folders):
                    continue
                
                for file in files:
                    if not self.running:
                        break
                    
                    try:
                        # Obtener la extensión del archivo
                        _, extension = os.path.splitext(file.lower())
                        
                        # Solo incluir archivos de video y música
                        if extension in video_extensions or extension in music_extensions:
                            full_path = os.path.join(root, file)
                            relative_path = os.path.relpath(full_path, drive_letter + "\\")
                            current_files.add(relative_path)
                        
                    except (OSError, PermissionError, UnicodeDecodeError):
                        continue
        
        except Exception as e:
            print(f"Error escaneando disco {drive_letter}: {e}")
        
        return current_files
    
    def _detect_changes(self, existing_files, current_files, drive_letter):
        """Detecta qué archivos se agregaron, eliminaron o modificaron"""
        added = current_files - existing_files      # Archivos nuevos
        removed = existing_files - current_files    # Archivos eliminados
        unchanged = existing_files & current_files  # Archivos sin cambios
        
        # Determinar si hay cambios significativos
        # Consideramos cambios significativos si hay más de 0 archivos añadidos o eliminados
        has_changes = len(added) > 0 or len(removed) > 0
        
        # Calcular porcentajes para análisis
        total_existing = len(existing_files)
        total_current = len(current_files)
        
        percent_added = 0
        percent_removed = 0
        
        if total_existing > 0:
            percent_removed = (len(removed) / total_existing) * 100
            
        if total_current > 0:
            percent_added = (len(added) / total_current) * 100
        
        # Registrar estadísticas detalladas
        print(f"Análisis de cambios en disco {drive_letter}:")
        print(f"  - Archivos en índice anterior: {total_existing}")
        print(f"  - Archivos actuales en disco: {total_current}")
        print(f"  - Archivos nuevos: {len(added)} ({percent_added:.1f}%)")
        print(f"  - Archivos eliminados: {len(removed)} ({percent_removed:.1f}%)")
        print(f"  - Archivos sin cambios: {len(unchanged)}")
        
        return {
            'has_changes': has_changes,
            'added': added,
            'removed': removed,
            'unchanged': unchanged,
            'total_current': total_current,
            'total_existing': total_existing,
            'percent_added': percent_added,
            'percent_removed': percent_removed
        }
    
    def _write_updated_drive_index(self, file_handle, drive_info, current_files):
        """Escribe el índice actualizado de un disco con todos sus archivos actuales"""
        file_handle.write(f"{drive_info}\n{'=' * 30}\n\n")
        
        # Escribir archivos ordenados para consistencia
        for file_path in sorted(current_files):
            file_handle.write(f"{file_path}\n")
        
        file_handle.write("\n\n")
    
    def _full_reindex(self, excluded_folders, buffer_size):
        """Indexado completo: reescribe todo el archivo"""
        with open("INDEXADO.txt", "w", encoding='utf-8', buffering=8192) as f:
            for i, (drive_info, drive_letter) in enumerate(self.volumes):
                if not self.running:
                    print("Proceso de indexación interrumpido por el usuario")
                    break
                
                print(f"Indexando {drive_info}...")
                self._index_single_drive(f, drive_info, drive_letter)
                
                # Actualizar progreso
                progress = (i + 1) / self.total_drives
                if self.progress_callback:
                    self.progress_callback(progress)
        
        print("Indexado completo exitosamente")
    
    def _index_single_drive(self, file_handle, drive_info, drive_letter):
        """Indexa un solo disco y escribe los datos al archivo"""
        excluded_folders = {
            '$recycle.bin', 'system volume information', 'pagefile.sys', 'hiberfil.sys',
            'swapfile.sys', 'windows', 'program files', 'program files (x86)', 
            'programdata', 'users\\default', 'users\\all users', 'users\\public',
            'temp', 'tmp', 'cache', '.git', '.svn', 'node_modules', '__pycache__',
            'recovery', 'system32', 'syswow64', 'winsxs'
        }
        
        # Extensiones de archivos de video
        video_extensions = {
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg',
            '.m4v', '.3gp', '.3g2', '.f4v', '.f4p', '.f4a', '.f4b', '.vob', '.ogv', 
            '.ogg', '.drc', '.gifv', '.mng', '.mts', '.m2ts', '.ts', '.mov', '.qt', 
            '.yuv', '.rm', '.rmvb', '.viv', '.asf', '.amv', '.m4p', '.m4v', '.mp2', 
            '.mpe', '.mpv', '.m2v', '.svi', '.divx', '.xvid', '.ssw', '.evo', '.ogm',
            '.dvr-ms', '.wtv', '.iso', '.m1v', '.m2v', '.mod', '.tod', '.tp', '.trp',
            '.bik', '.smk', '.swf', '.ifo', '.bup', '.mkv', '.mk3d', '.mka', '.mks'
        }
        
        # Extensiones de archivos de música
        music_extensions = {
            '.mp3', '.wav', '.ogg', '.flac', '.aac', '.wma', '.m4a', '.opus', '.alac',
            '.aiff', '.ape', '.au', '.mid', '.midi', '.pcm', '.ac3', '.amr', '.voc',
            '.mpc', '.tta', '.dts', '.gsm', '.wv', '.wvc', '.spx', '.ra', '.rm', '.oga',
            '.mp2', '.mp1', '.dsf', '.dff', '.mka', '.m3u', '.m3u8', '.pls', '.cue'
        }
        
        buffer_size = 10000
        
        try:
            # Escribir información del disco
            file_handle.write(f"{drive_info}\n{'=' * 30}\n\n")
            
            file_buffer = []
            files_processed = 0
            video_files_count = 0
            music_files_count = 0
            
            # Indexar con optimizaciones
            for root, dirs, files in os.walk(drive_letter + "\\"):
                if not self.running:
                    break
                
                # Filtrar directorios para no entrar en carpetas excluidas
                dirs[:] = [d for d in dirs if d.lower() not in excluded_folders]
                
                # Saltar carpetas del sistema y temporales
                root_lower = root.lower()
                if any(excluded in root_lower for excluded in excluded_folders):
                    continue
                
                for file in files:
                    if not self.running:
                        break
                        
                    try:
                        # Obtener la extensión del archivo
                        _, extension = os.path.splitext(file.lower())
                        
                        # Solo incluir archivos de video y música
                        if extension in video_extensions or extension in music_extensions:
                            full_path = os.path.join(root, file)
                            relative_path = os.path.relpath(full_path, drive_letter + "\\")
                            file_buffer.append(relative_path)
                            files_processed += 1
                            
                            # Contar por tipo
                            if extension in video_extensions:
                                video_files_count += 1
                            elif extension in music_extensions:
                                music_files_count += 1
                            
                            # Escribir en lotes para mejor rendimiento
                            if len(file_buffer) >= buffer_size:
                                file_handle.write('\n'.join(file_buffer) + '\n')
                                file_buffer.clear()
                                file_handle.flush()  # Forzar escritura al disco
                                
                                # Pequeña pausa para no saturar el I/O
                                self.msleep(1)
                        
                    except (OSError, PermissionError, UnicodeDecodeError):
                        # Saltar archivos problemáticos sin detener el proceso
                        continue
            
            # Escribir archivos restantes del buffer
            if file_buffer:
                file_handle.write('\n'.join(file_buffer) + '\n')
                file_buffer.clear()
            
            file_handle.write("\n\n")
            print(f"Disco {drive_info} indexado: {files_processed} archivos ({video_files_count} videos, {music_files_count} música)")
            
        except Exception as drive_error:
            print(f"Error procesando disco {drive_info}: {drive_error}")
            raise

    def stop(self):
        print("Solicitando detención del proceso de indexación...")
        self.running = False

class ReindexSelectionDialog(QDialog):
    def __init__(self, indexed_drives, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # Aplicar efecto acrílico según la versión de Windows
        hwnd = int(self.winId())
        accent = ACCENT_POLICY()
        if is_windows_11_or_greater():
            accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
            accent.GradientColor = 0x01000000  # Color oscuro para Windows 11
        else:
            accent.AccentState = ACCENT_ENABLE_FLUENT
            accent.GradientColor = 0xCC000000  # Color oscuro con 80% opacidad para Windows 10
        accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
        
        data = WINDOWCOMPOSITIONATTRIBDATA()
        data.Attribute = WCA_ACCENT_POLICY
        data.SizeOfData = ctypes.sizeof(accent)
        data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
        ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))

        # Aplicar bordes redondeados
        ctypes.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            DWMWA_WINDOW_CORNER_PREFERENCE,
            ctypes.byref(ctypes.c_int(DWMWCP_ROUND)),
            ctypes.sizeof(ctypes.c_int)
        )
        layout = QVBoxLayout(self)
        self.frame = QFrame()
        self.frame.setStyleSheet("""
            QFrame {
                background-color: rgba(128, 128, 128, 50);
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 30);
            }
        """)
        frame_layout = QVBoxLayout(self.frame)
        title_label = QLabel("Indexar Archivos de Video y Música")
        title_label.setStyleSheet("""
            QLabel {
                color: white; 
                font-size: 13px;
                font-weight: bold;
                padding: 10px;
                background-color: transparent;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(title_label)
        
        # Añadir etiqueta informativa sobre el modo de indexación inteligente
        smart_index_label = QLabel("Modo Inteligente: Solo se actualizarán los cambios")
        smart_index_label.setStyleSheet("""
            QLabel {
                color: #4CAF50; 
                font-size: 12px;
                font-weight: bold;
                padding: 0px 10px;
                background-color: transparent;
            }
        """)
        smart_index_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(smart_index_label)
        
        # Añadir etiqueta informativa sobre el tipo de archivos que se indexarán
        info_label = QLabel("Solo se indexarán archivos de video y música")
        info_label.setStyleSheet("""
            QLabel {
                color: #4CAF50; 
                font-size: 11px;
                font-style: italic;
                padding: 0px 10px;
                background-color: transparent;
            }
        """)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(info_label)
        
        # Añadir lista de extensiones soportadas
        extensions_label = QLabel("Formatos soportados: MP4, AVI, MKV, MP3, WAV, FLAC, etc.")
        extensions_label.setStyleSheet("""
            QLabel {
                color: #BBBBBB; 
                font-size: 10px;
                padding: 0px 10px 10px 10px;
                background-color: transparent;
            }
        """)
        extensions_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(extensions_label)
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar {
                background-color: rgba(0, 0, 0, 50);
                width: 10px;
                border-radius: 5px;
            }
            QScrollBar::handle {
                background-color: rgba(255, 255, 255, 30);
                border-radius: 5px;
            }
        """)
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        self.disk_checkboxes = {}
        already_indexed = set()
        try:
            with open("INDEXADO.txt", "r", encoding='utf-8') as f:
                content = f.read()
                for line in content.split('\n'):
                    if line.startswith("Disco:"):
                        drive_letter = line.split('(')[-1].strip(')')
                        already_indexed.add(drive_letter)
        except FileNotFoundError:
            pass
        for volume_info, drive_letter in indexed_drives:
            disk_widget = QWidget()
            disk_layout = QHBoxLayout(disk_widget)
            checkbox = QCheckBox()
            checkbox.setStyleSheet("""
                QCheckBox {
                    spacing: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid white;
                    background-color: transparent;
                    border-radius: 4px;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #4CAF50;
                    background-color: #4CAF50;
                    border-radius: 4px;
                }
            """)
            checkbox.setChecked(drive_letter in already_indexed)
            disk_layout.addWidget(checkbox)
            label_text = volume_info.replace("Disco: ", "")
            if drive_letter in already_indexed:
                label_text += " (Actualizar indexación)"
            else:
                label_text += " (No indexado)"
            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 11px;
                    padding-left: 5px;
                }
            """)
            disk_layout.addWidget(label)
            disk_layout.addStretch()
            scroll_layout.addWidget(disk_widget)
            self.disk_checkboxes[drive_letter] = checkbox
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        frame_layout.addWidget(scroll_area)
        button_layout = QHBoxLayout()
        self.accept_button = QPushButton("Aceptar")
        self.accept_button.setFixedHeight(35)  # Altura fija
        self.accept_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.accept_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 17px;
                padding: 0 25px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.setFixedHeight(35)  # Altura fija
        self.cancel_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                border: none;
                border-radius: 17px;
                padding: 0 25px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ef5350;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
        """)
        button_layout.addWidget(self.accept_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.setSpacing(15)  # Más espacio entre botones
        button_layout.setContentsMargins(0, 15, 0, 0)  # Más margen superior
        button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)  # Centrar botones
        frame_layout.addLayout(button_layout)
        layout.addWidget(self.frame)
        self.accept_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)
        self.setFixedSize(450, 500)
    
    def get_selected_drives(self):
        return [drive for drive, checkbox in self.disk_checkboxes.items() if checkbox.isChecked()]