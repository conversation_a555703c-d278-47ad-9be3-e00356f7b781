#!/usr/bin/env python3
"""
Script simple para probar el mapeo de puertos USB
"""

import subprocess
import tempfile
import os
import re

def test_usbdeview_direct():
    """Probar USBDeview directamente"""
    print("=== PRUEBA DIRECTA DE USBDEVIEW ===")
    
    # Crear archivo temporal
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        temp_csv = f.name
    
    try:
        # Ejecutar USBDeview
        cmd = [
            "USBDeview.exe",
            "/DisplayDisconnected", "0",
            "/scomma", temp_csv
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and os.path.exists(temp_csv):
            print("✓ USBDeview ejecutado correctamente")
            
            # Leer y analizar el CSV
            with open(temp_csv, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            print(f"Total de líneas en CSV: {len(lines)}")
            
            # Buscar dispositivos de almacenamiento con letras de unidad
            storage_devices = []
            for line in lines:
                if not line.strip():
                    continue
                    
                parts = line.split(',')
                if len(parts) > 7:
                    device_name = parts[0].strip('"')
                    device_type = parts[2].strip('"')
                    drive_letters = parts[7].strip('"')
                    
                    if device_type == "Mass Storage" and drive_letters:
                        storage_devices.append({
                            'name': device_name,
                            'drives': drive_letters,
                            'type': device_type
                        })
            
            print(f"\nDispositivos de almacenamiento encontrados: {len(storage_devices)}")
            for device in storage_devices:
                print(f"  - {device['name']}: {device['drives']}")
                
                # Buscar información de puerto en el nombre
                port_match = re.search(r'Port_#(\d+)\.Hub_#(\d+)', device['name'])
                if port_match:
                    port_info = f"Port_#{port_match.group(1).zfill(4)}.Hub_#{port_match.group(2).zfill(4)}"
                    hub_info = f"Hub {port_match.group(2)}"
                    print(f"    Puerto: {port_info}, Hub: {hub_info}")
                else:
                    print(f"    No se encontró información de puerto en el nombre")
            
            # Buscar hubs y puertos
            print(f"\nHubs y puertos encontrados:")
            for line in lines:
                if not line.strip():
                    continue
                    
                parts = line.split(',')
                if len(parts) > 0:
                    device_name = parts[0].strip('"')
                    if 'Port_#' in device_name and 'Hub_#' in device_name:
                        print(f"  - {device_name}")
        else:
            print(f"✗ Error ejecutando USBDeview: {result.stderr}")
            
    finally:
        # Limpiar archivo temporal
        try:
            os.unlink(temp_csv)
        except:
            pass

if __name__ == "__main__":
    test_usbdeview_direct()
