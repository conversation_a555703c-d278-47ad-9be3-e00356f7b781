# Hook personalizado para PyInstaller para incluir todos los módulos de pywin32

from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# Recopilar todos los submódulos de pywin32
hiddenimports = []

# Módulos principales de pywin32
pywin32_modules = [
    'win32api', 'win32con', 'win32event', 'win32file', 'win32gui',
    'win32process', 'win32security', 'win32service', 'win32serviceutil',
    'win32clipboard', 'win32pipe', 'win32job', 'win32pdh', 'win32evtlog',
    'win32net', 'win32netcon', 'win32wnet', 'win32inet', 'win32inetcon',
    'win32ras', 'win32rasutil', 'win32ts', 'win32profile', 'win32print',
    'win32timezone', 'win32crypt', 'pywintypes', 'pythoncom'
]

for module in pywin32_modules:
    try:
        hiddenimports.extend(collect_submodules(module))
    except:
        hiddenimports.append(module)

# Agregar módulos específicos que suelen faltar
additional_modules = [
    'win32com.client', 'win32com.server', 'win32com.shell',
    'win32com.propsys', 'win32com.taskscheduler'
]

hiddenimports.extend(additional_modules)

# Recopilar archivos de datos si es necesario
try:
    datas = collect_data_files('pywin32')
except:
    datas = []