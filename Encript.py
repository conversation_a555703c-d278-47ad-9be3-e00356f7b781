from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
from PyQt6.QtWidgets import (
    QApplication, QInputDialog, QMessageBox, QLineEdit, 
    QDialog, QVBoxLayout, QGroupBox, QRadioButton, 
    QDialogButtonBox, QCheckBox
)
from PyQt6.QtCore import Qt
import sys
import os
import traceback  # Añadido explícitamente
# PARA ENCRIPTAR y DESENCRIPTAR EL ARCHIVO ZETACOPY.py ------> python Encript.py
class FileEncryptor:
    def __init__(self):
        # La contraseña se solicita al usuario, no se almacena aquí por seguridad
        self.salt = b"ZetaSalt2024"
        self.files_to_encrypt = [
            'MONITOREO.py',
            'ZETACOPY.py',
            'AJUSTES.py',
            'DRAGGABLE_LIST_WIDGET.py',
            'EXPLORADOR.py', # <PERSON><PERSON><PERSON><PERSON>.py a la lista
            'ZFind.py', # Añadido ZFind.py a la lista
            'BOTONES.py' # Añadido BOTONES.py a la lista
        ]
        
    def _generate_key(self, password):
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=480000,
        )
        # Asegurarse de que la contraseña sea bytes
        password_bytes = password.encode() if isinstance(password, str) else password
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key

    def encrypt_file(self):
        try:
            for file_name in self.files_to_encrypt:
                if not os.path.exists(file_name):
                    raise FileNotFoundError(f"{file_name} no encontrado")

                # Crear backup del archivo original
                with open(file_name, 'r', encoding='utf-8') as f:
                    original_content = f.read()
            
                # Guardar backup
                backup_name = f"{file_name.split('.')[0]}_backup.py"
                with open(backup_name, 'w', encoding='utf-8') as f:
                    f.write(original_content)

                # Generar clave usando el mismo método que en decrypt_and_run
                def _x(a, b):
                    return bytes([(x + b) % 256 for x in a])

                def _k():
                    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
                    _b = [_x(_a(i), 42 + i) for i in range(6)]
                    _c = [bytes([
                        (x - 42 + 256) % 256 for x in p
                    ]) for p in _b]
                    return b''.join([
                        _c[0][2:6],
                        _c[1][1:5],
                        _c[2][0:4],
                        _c[3][3:7],
                        _c[4][2:6],
                        _c[5][1:3]
                    ])

                def _s():
                    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=_s(),
                    iterations=480000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(_k()))
                f = Fernet(key)
                encrypted = f.encrypt(original_content.encode('utf-8'))

                # Modificar el loader_code según el archivo
                if file_name == 'MONITOREO.py':
                    loader_code = f'''import base64, sys, os, json, traceback
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import wmi  # Importaciones necesarias para MONITOREO.py

# Definir __file__ para que funcione correctamente con rutas relativas
__file__ = os.path.abspath(__file__)

def _x(a, b):
    return bytes([(x + b) % 256 for x in a])

def _k():
    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
    _b = [_x(_a(i), 42 + i) for i in range(6)]
    _c = [bytes([
        (x - 42 + 256) % 256 for x in p
    ]) for p in _b]
    return b''.join([
        _c[0][2:6],
        _c[1][1:5],
        _c[2][0:4],
        _c[3][3:7],
        _c[4][2:6],
        _c[5][1:3]
    ])

def _s():
    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

def decrypt_and_get_functions():
    try:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=_s(),
            iterations=480000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(_k()))
        
        encrypted_code = "{encrypted.decode()}"
        
        f = Fernet(key)
        decrypted = f.decrypt(encrypted_code.encode())
        
        if not isinstance(decrypted, bytes):
            sys.exit(0)
            
        # Crear namespace local y ejecutar el código
        namespace = {{}}
        namespace['__file__'] = __file__
        exec(decrypted.decode('utf-8'), namespace)
        
        # Exponer todas las funciones y clases al espacio global
        current_module = sys.modules[__name__]
        for name, value in namespace.items():
            if not name.startswith('__'):
                setattr(current_module, name, value)
        
        return namespace

    except Exception as e:
        print(f"Error en decrypt_and_get_functions: {{str(e)}}")
        traceback.print_exc()
        sys.exit(1)

# Ejecutar y exponer las funciones
decrypt_and_get_functions()
'''
                elif file_name == 'AJUSTES.py':  # Código especial para AJUSTES.py
                    loader_code = f'''from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import sys, os, base64, json, wmi, uuid, ctypes, hashlib, qrcode, segno
import win32file, win32api, pywintypes
import traceback
from datetime import datetime, timedelta
from io import BytesIO
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from APARIENCIA import ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY, apply_acrylic_effect, apply_acrylic_and_rounded
from MONITOREO import generate_unique_code, get_motherboard_info, get_system_uuid, get_disk_serial_number, get_original_info
from ESTILO_DIALOGO import MacLikeDialog
from PRESENTACION import IconShadowEffect, ModernZWidget
from CREAR import create_usb_icon, create_external_hdd_icon, create_internal_hdd_icon
from ACTIVATE import show_license_activation_dialog

# Definir __file__ para que funcione correctamente con rutas relativas
__file__ = os.path.abspath(__file__)

def _x(a, b):
    return bytes([(x + b) % 256 for x in a])

def _k():
    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
    _b = [_x(_a(i), 42 + i) for i in range(6)]
    _c = [bytes([
        (x - 42 + 256) % 256 for x in p
    ]) for p in _b]
    return b''.join([
        _c[0][2:6],
        _c[1][1:5],
        _c[2][0:4],
        _c[3][3:7],
        _c[4][2:6],
        _c[5][1:3]
    ])

def _s():
    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

# Desencriptar y exponer las clases/funciones necesarias
def decrypt_and_get_namespace():
    try:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=_s(),
            iterations=480000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(_k()))
        
        encrypted_code = "{encrypted.decode()}"
        
        f = Fernet(key)
        decrypted = f.decrypt(encrypted_code.encode())
        
        if not isinstance(decrypted, bytes):
            sys.exit(0)
            
        # Crear namespace local y ejecutar el código
        namespace = {{}}
        namespace['__file__'] = __file__
        exec(decrypted.decode('utf-8'), namespace)
        
        # Exponer todas las clases y funciones al módulo actual usando setattr
        current_module = sys.modules[__name__]
        for name, value in namespace.items():
            if not name.startswith('__'):
                setattr(current_module, name, value)
        
        return namespace

    except Exception as e:
        print(f"Error en decrypt_and_get_namespace: {{str(e)}}")
        traceback.print_exc()
        sys.exit(1)

# Ejecutar y exponer las clases/funciones
decrypt_and_get_namespace()
'''
                elif file_name == 'DRAGGABLE_LIST_WIDGET.py':
                    loader_code = f'''import base64, sys, os, traceback
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

__file__ = os.path.abspath(__file__)

def _x(a, b):
    return bytes([(x + b) % 256 for x in a])

def _k():
    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
    _b = [_x(_a(i), 42 + i) for i in range(6)]
    _c = [bytes([
        (x - 42 + 256) % 256 for x in p
    ]) for p in _b]
    return b''.join([
        _c[0][2:6],
        _c[1][1:5],
        _c[2][0:4],
        _c[3][3:7],
        _c[4][2:6],
        _c[5][1:3]
    ])

def _s():
    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

def decrypt_and_run():
    try:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=_s(),
            iterations=480000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(_k()))
        encrypted_code = "{encrypted.decode()}"
        f = Fernet(key)
        decrypted = f.decrypt(encrypted_code.encode())
        if not isinstance(decrypted, bytes):
            sys.exit(0)
        
        # Crear namespace local para ejecutar el código
        namespace = {{}}
        namespace['__file__'] = __file__
        
        # Ejecutar el código desencriptado
        exec(decrypted.decode('utf-8'), namespace)
        
        # Exponer todas las clases y funciones al módulo actual usando setattr
        current_module = sys.modules[__name__]
        for name, value in namespace.items():
            if not name.startswith('__'):
                setattr(current_module, name, value)
                globals()[name] = value
                
    except Exception as e:
        print(f"Error en decrypt_and_run: {{str(e)}}")
        traceback.print_exc()
        sys.exit(1)

decrypt_and_run()
'''
                elif file_name == 'EXPLORADOR.py': # Nuevo bloque para EXPLORADOR.py
                    loader_code = f'''import base64, sys, os, traceback
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

__file__ = os.path.abspath(__file__)

def _x(a, b):
    return bytes([(x + b) % 256 for x in a])

def _k():
    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
    _b = [_x(_a(i), 42 + i) for i in range(6)]
    _c = [bytes([
        (x - 42 + 256) % 256 for x in p
    ]) for p in _b]
    return b''.join([
        _c[0][2:6],
        _c[1][1:5],
        _c[2][0:4],
        _c[3][3:7],
        _c[4][2:6],
        _c[5][1:3]
    ])

def _s():
    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

def decrypt_and_run():
    try:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=_s(),
            iterations=480000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(_k()))
        encrypted_code = "{encrypted.decode()}"
        f = Fernet(key)
        decrypted = f.decrypt(encrypted_code.encode())
        if not isinstance(decrypted, bytes):
            sys.exit(0)
        
        # Crear namespace local para ejecutar el código
        namespace = {{}}
        namespace['__file__'] = __file__
        
        # Ejecutar el código desencriptado
        exec(decrypted.decode('utf-8'), namespace)
        
        # Exponer todas las clases y funciones al módulo actual usando setattr
        current_module = sys.modules[__name__]
        for name, value in namespace.items():
            if not name.startswith('__'):
                setattr(current_module, name, value)
                globals()[name] = value
                
    except Exception as e:
        print(f"Error en decrypt_and_run: {{str(e)}}")
        traceback.print_exc()
        sys.exit(1)

decrypt_and_run()
'''
                elif file_name == 'ZFind.py': # Nuevo bloque para ZFind.py
                    loader_code = f'''import base64, sys, os, traceback, subprocess, ctypes, time
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from ctypes import wintypes

# Importaciones específicas para ZFind
from APARIENCIA import apply_acrylic_and_rounded
from CREAR import CustomCloseButton, CustomMaximizeButton, CustomMinimizeButton, icono_carpeta, Icono_BUSCADOR
from ICONOS_EXPLORER import FileTypeIconManager
from REDIMENSIONAR import WindowResizer
from Barras_Seleccion import ZfindRoundedRectDelegate

__file__ = os.path.abspath(__file__)

def _x(a, b):
    return bytes([(x + b) % 256 for x in a])

def _k():
    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
    _b = [_x(_a(i), 42 + i) for i in range(6)]
    _c = [bytes([
        (x - 42 + 256) % 256 for x in p
    ]) for p in _b]
    return b''.join([
        _c[0][2:6],
        _c[1][1:5],
        _c[2][0:4],
        _c[3][3:7],
        _c[4][2:6],
        _c[5][1:3]
    ])

def _s():
    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

def decrypt_and_run():
    try:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=_s(),
            iterations=480000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(_k()))
        encrypted_code = "{encrypted.decode()}"
        f = Fernet(key)
        decrypted = f.decrypt(encrypted_code.encode())
        if not isinstance(decrypted, bytes):
            sys.exit(0)
        
        # Crear namespace local para ejecutar el código
        namespace = {{}}
        namespace['__file__'] = __file__
        
        # Ejecutar el código desencriptado
        exec(decrypted.decode('utf-8'), namespace)
        
        # Exponer todas las clases y funciones al módulo actual usando setattr
        current_module = sys.modules[__name__]
        for name, value in namespace.items():
            if not name.startswith('__'):
                setattr(current_module, name, value)
                globals()[name] = value
                
    except Exception as e:
        print(f"Error en decrypt_and_run: {{str(e)}}")
        traceback.print_exc()
        sys.exit(1)

decrypt_and_run()
'''
                elif file_name == 'BOTONES.py': # Nuevo bloque para BOTONES.py
                    loader_code = f'''import base64, sys, os, traceback
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# Importaciones específicas para BOTONES
from APARIENCIA import apply_acrylic_and_rounded

__file__ = os.path.abspath(__file__)

def _x(a, b):
    return bytes([(x + b) % 256 for x in a])

def _k():
    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
    _b = [_x(_a(i), 42 + i) for i in range(6)]
    _c = [bytes([
        (x - 42 + 256) % 256 for x in p
    ]) for p in _b]
    return b''.join([
        _c[0][2:6],
        _c[1][1:5],
        _c[2][0:4],
        _c[3][3:7],
        _c[4][2:6],
        _c[5][1:3]
    ])

def _s():
    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

def decrypt_and_run():
    try:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=_s(),
            iterations=480000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(_k()))
        encrypted_code = "{encrypted.decode()}"
        f = Fernet(key)
        decrypted = f.decrypt(encrypted_code.encode())
        if not isinstance(decrypted, bytes):
            sys.exit(0)
        
        # Crear namespace local para ejecutar el código
        namespace = {{}}
        namespace['__file__'] = __file__
        
        # Ejecutar el código desencriptado
        exec(decrypted.decode('utf-8'), namespace)
        
        # Exponer todas las clases y funciones al módulo actual usando setattr
        current_module = sys.modules[__name__]
        for name, value in namespace.items():
            if not name.startswith('__'):
                setattr(current_module, name, value)
                globals()[name] = value
                
    except Exception as e:
        print(f"Error en decrypt_and_run: {{str(e)}}")
        traceback.print_exc()
        sys.exit(1)

decrypt_and_run()
'''
                else:  # Para ZETACOPY.py
                    loader_code = f'''import base64, sys, os, json, traceback, shutil, win32api, win32event, winerror
from datetime import datetime
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Importar PyQt6 completo para tener acceso a QtCore, QtWidgets, etc.
import PyQt6
import PyQt6.QtCore as QtCore
import PyQt6.QtWidgets as QtWidgets
import PyQt6.QtGui as QtGui

# Definir __file__ para que funcione correctamente con rutas relativas
__file__ = os.path.abspath(__file__)

def _x(a, b):
    return bytes([(x + b) % 256 for x in a])

def _k():
    _a = lambda x: [i ^ x for i in range(16, 0, -1)]
    _b = [_x(_a(i), 42 + i) for i in range(6)]
    _c = [bytes([
        (x - 42 + 256) % 256 for x in p
    ]) for p in _b]
    return b''.join([
        _c[0][2:6],
        _c[1][1:5],
        _c[2][0:4],
        _c[3][3:7],
        _c[4][2:6],
        _c[5][1:3]
    ])

def _s():
    return bytes([(x ^ 42) for x in [132, 143, 158, 139, 125, 153, 150, 158, 92, 90, 92, 94]])

def decrypt_and_run():
    try:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=_s(),
            iterations=480000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(_k()))
        
        encrypted_code = "{encrypted.decode()}"
        
        f = Fernet(key)
        decrypted = f.decrypt(encrypted_code.encode())
        
        if not isinstance(decrypted, bytes):
            sys.exit(0)
            
        # Crear namespace local para ejecutar el código
        namespace = {{}}
        namespace['__file__'] = __file__
        namespace['QtCore'] = QtCore
        namespace['QtWidgets'] = QtWidgets
        namespace['QtGui'] = QtGui
        
        # IMPORTANTE: Simular que estamos ejecutando como script principal
        original_name = __name__
        namespace['__name__'] = '__main__' if original_name == '__main__' else original_name
        
        # Ejecutar el código desencriptado
        exec(decrypted.decode('utf-8'), namespace)
        
        # Exponer todas las clases y funciones al módulo actual usando setattr
        current_module = sys.modules[original_name]
        for name, value in namespace.items():
            if not name.startswith('__'):
                setattr(current_module, name, value)
                globals()[name] = value

    except Exception as e:
        print(f"Error en decrypt_and_run: {{str(e)}}")
        traceback.print_exc()
        sys.exit(1)

# Ejecutar el código desencriptado
decrypt_and_run()
'''

                # Escribir el nuevo archivo con el loader y el contenido encriptado
                with open(file_name, 'w', encoding='utf-8') as f:
                    f.write(loader_code)

                print(f"{file_name} ha sido encriptado exitosamente")

            return True

        except Exception as e:
            print(f"Error durante la encriptación: {e}")
            traceback.print_exc()
            return False

    def decrypt_file(self):
        try:
            for file_name in self.files_to_encrypt:
                backup_name = f"{file_name.split('.')[0]}_backup.py"
                if not os.path.exists(backup_name):
                    QMessageBox.warning(None, "Error", f"No se encontró el archivo de backup para {file_name}")
                    return False

                # Restaurar desde el backup
                with open(backup_name, 'r', encoding='utf-8') as f:
                    original_content = f.read()

                # Restaurar el archivo original
                with open(file_name, 'w', encoding='utf-8') as f:
                    f.write(original_content)

                print(f"Archivo {file_name} restaurado exitosamente")

            return True

        except Exception as e:
            print(f"Error al desencriptar: {str(e)}")
            return False

def main():
    app = QApplication(sys.argv)
    encryptor = FileEncryptor()
    
    password, ok = QInputDialog.getText(
        None, 
        'Verificación', 
        'Introduce la contraseña maestra:',
        QLineEdit.EchoMode.Password
    )
    
    # Verificar contraseña (debes cambiar esta línea por tu método de verificación seguro)
    if ok and password == "Prado12@@Delgado12@@":  # CAMBIAR ESTO POR UN MÉTODO MÁS SEGURO
        # Crear un diálogo personalizado para selección
        dialog = QDialog()
        dialog.setWindowTitle('Seleccionar Acción y Archivos')
        layout = QVBoxLayout()

        # Grupo de botones para la acción
        action_group = QGroupBox("¿Qué deseas hacer?")
        action_layout = QVBoxLayout()
        encrypt_radio = QRadioButton("Encriptar")
        decrypt_radio = QRadioButton("Desencriptar")
        encrypt_radio.setChecked(True)  # Por defecto seleccionado
        action_layout.addWidget(encrypt_radio)
        action_layout.addWidget(decrypt_radio)
        action_group.setLayout(action_layout)

        # Grupo de checkboxes para los archivos
        files_group = QGroupBox("Selecciona los archivos:")
        files_layout = QVBoxLayout()
        file_checks = []
        
        # Checkbox "Seleccionar todos"
        select_all = QCheckBox("Seleccionar todos")
        files_layout.addWidget(select_all)
        
        # Checkboxes individuales
        for file_name in encryptor.files_to_encrypt:
            check = QCheckBox(file_name)
            files_layout.addWidget(check)
            file_checks.append(check)
        
        # Conectar "Seleccionar todos" con los checkboxes individuales
        def toggle_all(state):
            for check in file_checks:
                check.setChecked(state)
        select_all.stateChanged.connect(toggle_all)
        
        # Actualizar "Seleccionar todos" cuando se cambian los individuales
        def update_select_all():
            all_checked = all(check.isChecked() for check in file_checks)
            any_checked = any(check.isChecked() for check in file_checks)
            select_all.setTristate(False)
            if all_checked:
                select_all.setChecked(True)
            elif any_checked:
                select_all.setTristate(True)
                select_all.setCheckState(Qt.CheckState.PartiallyChecked)
            else:
                select_all.setChecked(False)
        
        for check in file_checks:
            check.stateChanged.connect(update_select_all)
        
        files_group.setLayout(files_layout)

        # Botones OK y Cancel
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)

        # Añadir todo al layout principal
        layout.addWidget(action_group)
        layout.addWidget(files_group)
        layout.addWidget(buttons)
        dialog.setLayout(layout)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                # Obtener la acción seleccionada
                action = "Encriptar" if encrypt_radio.isChecked() else "Desencriptar"
                
                # Obtener los archivos seleccionados
                selected_files = []
                for check, file_name in zip(file_checks, encryptor.files_to_encrypt):
                    if check.isChecked():
                        selected_files.append(file_name)

                if selected_files:
                    # Guardar el archivo actual
                    current_files = encryptor.files_to_encrypt
                    # Modificar temporalmente la lista de archivos
                    encryptor.files_to_encrypt = selected_files

                    if action == "Encriptar":
                        if encryptor.encrypt_file():
                            QMessageBox.information(None, "Éxito", 
                                f"Archivos encriptados correctamente:\n{chr(10).join(selected_files)}")
                        else:
                            QMessageBox.critical(None, "Error", 
                                f"Error al encriptar los archivos:\n{chr(10).join(selected_files)}")
                    else:
                        if encryptor.decrypt_file():
                            QMessageBox.information(None, "Éxito", 
                                f"Archivos restaurados correctamente:\n{chr(10).join(selected_files)}")
                        else:
                            QMessageBox.critical(None, "Error", 
                                f"Error al restaurar los archivos:\n{chr(10).join(selected_files)}")

                    # Restaurar la lista original de archivos
                    encryptor.files_to_encrypt = current_files
                else:
                    QMessageBox.critical(None, "Error", "No se seleccionó ningún archivo")

            except Exception as e:
                QMessageBox.critical(None, "Error", f"Error: {str(e)}")
    else:
        QMessageBox.critical(None, "Error", "Contraseña incorrecta")

if __name__ == '__main__':
    main()
