import base64
import ctypes
import hashlib
import json
import os
import subprocess
import threading
import time
from ctypes import windll, create_unicode_buffer, wintypes, byref, Structure, c_byte as BYTE
from datetime import datetime

import pythoncom
import win32api
import win32file
import wmi
from PyQt6.QtCore import QThread, pyqtSignal

# Caché global para información de puertos USB
_USB_PORT_CACHE = {}
_USB_PORT_CACHE_LOCK = threading.Lock()
_PRELOAD_IN_PROGRESS = False
_PRELOAD_LOCK = threading.Lock()

# Caché global para modelos de discos
_DISK_MODELS_CACHE = {}  # Mapeo número de disco -> modelo
_DRIVE_TO_MODEL_CACHE = {}  # Mapeo letra de unidad -> modelo (directo)
_DISK_MODELS_CACHE_LOCK = threading.Lock()
_MODELS_LOADED = False





class STORAGE_PROPERTY_QUERY(Structure):
    _fields_ = [
        ('PropertyId', wintypes.DWORD),
        ('QueryType', wintypes.DWORD),
        ('AdditionalParameters', BYTE * 1)
    ]

def calculate_hash(data):
    return hashlib.sha256(data.encode()).hexdigest()

class Worker(QThread):
    update_volumes = pyqtSignal(list)  # type: pyqtSignal
    disk_status_changed = pyqtSignal(str, bool)  # type: pyqtSignal
    update_signal = pyqtSignal(dict)  # type: pyqtSignal
    finished_signal = pyqtSignal()  # type: pyqtSignal

    def __init__(self):
        super().__init__()
        self._running = True
        self._properties_cache = {}
        self._cache_timeout = 5  # segundos
        self._last_cache_cleanup = time.time()
        
        # Inicializar gestor de dispositivos móviles
        self.mobile_manager = None
        self._mobile_devices = []
    def run(self):
        old_volumes = self.get_connected_volumes()
        self.update_volumes.emit(old_volumes)  # Primera carga completa
        
        # Precarga desactivada: no realizar operaciones de precarga para evitar ralentizar
        
        while self._running:
            new_volumes = self.get_connected_volumes()
            if new_volumes != old_volumes:
                old_letters = {vol[2] for vol in old_volumes}
                new_letters = {vol[2] for vol in new_volumes}
                for old_volume in old_volumes:
                    drive_letter = old_volume[2]
                    if drive_letter not in new_letters:
                        self.disk_status_changed.emit(drive_letter, False)
                        # Limpiezas de caché desactivadas
                for new_volume in new_volumes:
                    drive_letter = new_volume[2]
                    if drive_letter not in old_letters:
                        self.disk_status_changed.emit(drive_letter, True)
                        # Sin precarga de modelos ni puertos
                        self.disk_status_changed.emit(drive_letter, True)
                
                # Actualizar la lista de volúmenes
                self.update_volumes.emit(new_volumes)
                old_volumes = new_volumes
            self.sleep(2)

    def get_connected_volumes(self):
        volumes = []
        buflen = ctypes.windll.kernel32.GetLogicalDriveStringsW(0, None)
        buf = create_unicode_buffer(buflen)
        ctypes.windll.kernel32.GetLogicalDriveStringsW(buflen, buf)
        for drive in buf[:buflen].split('\x00'):
            if drive:
                volume_name_buf = create_unicode_buffer(1024)
                file_system_name_buf = create_unicode_buffer(1024)
                serial_number = ctypes.c_uint32()
                max_component_length = ctypes.c_uint32()
                file_system_flags = ctypes.c_uint32()
                ctypes.windll.kernel32.GetVolumeInformationW(
                    ctypes.c_wchar_p(drive),
                    volume_name_buf,
                    ctypes.sizeof(volume_name_buf),
                    ctypes.byref(serial_number),
                    ctypes.byref(max_component_length),
                    ctypes.byref(file_system_flags),
                    file_system_name_buf,
                    ctypes.sizeof(file_system_name_buf)
                )
                volume_name = volume_name_buf.value if volume_name_buf.value else "Sin nombre"
                drive_type = self.get_drive_type(drive)
                volumes.append((volume_name, drive_type, drive.strip('\\')))
        return volumes

    def get_drive_type(self, drive):
        drive_type = ctypes.windll.kernel32.GetDriveTypeW(drive)
        if drive_type == 2:  # DRIVE_REMOVABLE
            return "Unidad extraíble (USB)"
        elif drive_type == 3:  # DRIVE_FIXED
            return self.get_disk_type(drive)
        elif drive_type == 4:  # DRIVE_REMOTE
            return "Unidad de red"  # Cambiado para que use el icono de red
        elif drive_type == 5:
            return "Unidad de CD/DVD"
        else:
            return "Desconocido"

    def get_disk_type(self, drive):
        drive_letter = drive.strip('\\')
        handle = windll.kernel32.CreateFileW(
            f"\\\\.\\{drive_letter}",
            0,
            0,
            None,
            3,
            0,
            None
        )
        if handle == -1:
            return "Disco duro (Desconocido)"
        class STORAGE_DEVICE_DESCRIPTOR(ctypes.Structure):
            _fields_ = [
                ("Version", wintypes.DWORD),
                ("Size", wintypes.DWORD),
                ("DeviceType", wintypes.BYTE),
                ("DeviceTypeModifier", wintypes.BYTE),
                ("RemovableMedia", wintypes.BOOLEAN),
                ("CommandQueueing", wintypes.BOOLEAN),
                ("VendorIdOffset", wintypes.DWORD),
                ("ProductIdOffset", wintypes.DWORD),
                ("ProductRevisionOffset", wintypes.DWORD),
                ("SerialNumberOffset", wintypes.DWORD),
                ("BusType", wintypes.BYTE),
                ("RawPropertiesLength", wintypes.DWORD),
                ("RawDeviceProperties", wintypes.BYTE * 1)
            ]
        IOCTL_STORAGE_QUERY_PROPERTY = 0x2D1400
        property_query = (wintypes.DWORD * 3)(0, 0, 0)
        descriptor = STORAGE_DEVICE_DESCRIPTOR()
        descriptor_size = wintypes.DWORD(ctypes.sizeof(descriptor))
        result = windll.kernel32.DeviceIoControl(
            handle,
            IOCTL_STORAGE_QUERY_PROPERTY,
            byref(property_query),
            ctypes.sizeof(property_query),
            byref(descriptor),
            descriptor_size,
            byref(descriptor_size),
            None
        )
        windll.kernel32.CloseHandle(handle)
        if result:
            if descriptor.BusType == 0x0B:
                return "Disco duro interno"
            else:
                return "Disco duro externo"
        else:
            return "Disco duro (Desconocido)"

    def get_disk_properties(self, drive_letter, force_refresh=False):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.get_disk_properties(drive_letter, force_refresh)

    def get_basic_properties(self, drive_letter):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.get_basic_properties(drive_letter)

    def add_source_disk_to_properties(self, properties_html, source_disk_info):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.add_source_disk_to_properties(properties_html, source_disk_info)

    def get_wmi_disk_info(self, drive_letter):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.get_wmi_disk_info(drive_letter)

    def format_size(self, size):
        """Método de compatibilidad - Ahora delega en Propiedades_Disco.py"""
        from Propiedades_Disco import DiscPropertyManager
        manager = DiscPropertyManager()
        return manager.format_size(size)

    def get_serial_number_model_and_uid(self, drive_letter):
        """Obtiene el número de serie, modelo e ID del disco usando PowerShell"""
        try:
            drive_letter_clean = drive_letter.replace(":", "")
            cmd = f'powershell -NoProfile -Command "(Get-Partition -DriveLetter {drive_letter_clean} -ErrorAction SilentlyContinue).DiskNumber"'
            disk_num_process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=2
            )
            if disk_num_process.returncode == 0 and disk_num_process.stdout.strip():
                disk_number = disk_num_process.stdout.strip()
                cmd = f'powershell -NoProfile -Command "Get-Disk -Number {disk_number} | Select-Object SerialNumber, Model, Path | ConvertTo-Json"'
                disk_info_process = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    shell=True,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    timeout=3
                )
                if disk_info_process.returncode == 0 and disk_info_process.stdout.strip():
                    try:
                        disk_info = json.loads(disk_info_process.stdout.strip())
                        serial = disk_info.get("SerialNumber", "").strip()
                        model = disk_info.get("Model", "").strip()
                        device_id = disk_info.get("Path", "").strip()
                        if not serial:
                            serial = "Número de serie no encontrado"
                        if not model:
                            model = "Modelo no encontrado"
                        if not device_id:
                            device_id = "UID no encontrado"
                        return serial, model, device_id
                    except json.JSONDecodeError:
                        pass
        except Exception as e:
            print(f"Error obteniendo información del disco {drive_letter}: {e}")
        return "Número de serie no encontrado", "Modelo no encontrado", "UID no encontrado"

    def get_volume_name(self, drive_letter):
        """Obtiene el nombre del volumen usando PowerShell"""
        try:
            cmd = f'powershell -NoProfile -Command "Get-Volume -DriveLetter {drive_letter.replace(":", "")} | Select-Object -ExpandProperty FileSystemLabel"'
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=2
            )
            if process.returncode == 0 and process.stdout.strip():
                return process.stdout.strip()
            cmd = f'powershell -NoProfile -Command "(Get-WmiObject -Class Win32_Volume -Filter \"DriveLetter = \'{drive_letter}\'\" | Select-Object -ExpandProperty Label)"'
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=2
            )
            if process.returncode == 0 and process.stdout.strip():
                return process.stdout.strip()
        except Exception as e:
            print(f"Error obteniendo nombre del volumen: {e}")
        return "Nombre del volumen no encontrado"

    def stop(self):
        self._running = False
        
    def get_usb_port_info(self, drive_letter):
        """
        Obtiene la información del puerto USB usando la nueva función optimizada.
        """
        return get_usb_port_info_fast(drive_letter)

        # Si todo falla, devolver valores por defecto
        return "", ""

    def _preload_disk_model(self, drive_letter):
        """Precarga el modelo de un disco recién conectado en la caché global"""
        try:
            if not os.path.exists(f"{drive_letter}\\"):
                return
            drive_type = win32file.GetDriveType(f"{drive_letter}\\")
            if drive_type == win32file.DRIVE_REMOVABLE:
                base_model = "Dispositivo extraíble"
            elif drive_type == win32file.DRIVE_FIXED:
                base_model = "Disco duro"
            elif drive_type == win32file.DRIVE_REMOTE:
                base_model = "Unidad de red"
            elif drive_type == win32file.DRIVE_CDROM:
                base_model = "Unidad óptica"
            else:
                base_model = "Dispositivo de almacenamiento"
            _DISK_MODELS_CACHE[drive_letter] = base_model

            # Solo precargar puerto USB si es dispositivo extraíble
            if drive_type == win32file.DRIVE_REMOVABLE:
                # Pequeño delay para que el dispositivo se estabilice
                time.sleep(0.5)
                threading.Thread(
                    target=self._preload_usb_port_info,
                    args=(drive_letter,),
                    daemon=True
                ).start()

            # Mejorar modelo en segundo plano
            if _mejorar_modelo_individual(drive_letter):
                return
                
            # Resto del código WMI...
        except Exception as e:
            print(f"Error precargando modelo para {drive_letter}: {e}")

    def _preload_usb_port_info(self, drive_letter):
        # Precarga desactivada
        return

    def _preload_existing_usb_devices(self, volumes):
        # Precarga desactivada
        return

    def _is_usb_device(self, drive_letter):
        """Verifica si un dispositivo es realmente USB usando PowerShell"""
        try:
            drive_letter_clean = drive_letter.replace(":", "")
            powershell_script = f'''
            try {{
                $partition = Get-Partition -DriveLetter {drive_letter_clean} -ErrorAction SilentlyContinue
                if ($partition) {{
                    $disk = Get-Disk -Number $partition.DiskNumber -ErrorAction SilentlyContinue
                    if ($disk -and $disk.BusType -eq "USB") {{
                        Write-Output "USB"
                    }} else {{
                        Write-Output "NO_USB"
                    }}
                }} else {{
                    Write-Output "NO_PARTITION"
                }}
            }} catch {{
                Write-Output "ERROR"
            }}
            '''
            
            # Configurar para ocultar la consola
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            result = subprocess.run(
                ["powershell", "-Command", powershell_script],
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=3,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0 and result.stdout.strip() == "USB":
                return True
            
        except Exception as e:
            print(f"Error verificando si {drive_letter} es USB: {e}")
        
        return False

def get_cached_usb_port_info(drive_letter):
    """Obtiene información de puerto USB desde la caché si está disponible"""
    # Caché desactivada: siempre devolver None
    return None, None

def clear_usb_port_cache_for_drive(drive_letter):
    """Limpia la entrada de caché para una unidad específica"""
    # Caché desactivada: no hacer nada
    return

def get_usb_port_info_with_cache(drive_letter):
    """Función optimizada que usa caché primero, luego consulta si es necesario"""
    # Caché desactivada: consultar directamente
    try:
        from MAPEO_PUERTO import get_port_info_for_drive
        port_id, hub_info = get_port_info_for_drive(drive_letter)
        return port_id, hub_info
    except Exception:
        return "No disponible", "No disponible"

def get_motherboard_info():
    """Obtiene información de la placa base usando PowerShell"""
    try:
        cmd = 'powershell -NoProfile -Command "Get-CimInstance -ClassName Win32_BaseBoard | Select-Object SerialNumber, Product, Manufacturer | ConvertTo-Json"'
        try:
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10
            )
            if process.returncode == 0 and process.stdout.strip():
                try:
                    board_info = json.loads(process.stdout.strip())
                    serial_number = board_info.get("SerialNumber", "").strip() or "Desconocido"
                    model = board_info.get("Product", "").strip() or "Desconocido"
                    manufacturer = board_info.get("Manufacturer", "").strip() or "Desconocido"
                    return serial_number, model, manufacturer
                except json.JSONDecodeError as e:
                    print(f"Error al decodificar JSON de placa base: {e}")
            else:
                print(f"Error al ejecutar comando de placa base: {process.stderr}")
        except subprocess.TimeoutExpired:
            print("Tiempo de espera agotado al obtener información de placa base")
        except Exception as e:
            print(f"Error ejecutando comando para placa base: {e}")
        try:
            print("Intentando método alternativo con WMI para placa base...")
            wmi_obj = wmi.WMI()
            for board in wmi_obj.Win32_BaseBoard():
                serial = board.SerialNumber.strip() if board.SerialNumber else "Desconocido"
                model = board.Product.strip() if board.Product else "Desconocido"
                manufacturer = board.Manufacturer.strip() if board.Manufacturer else "Desconocido"
                print("Método alternativo exitoso para placa base")
                return serial, model, manufacturer
        except Exception as e:
            print(f"Error en método alternativo para placa base: {e}")
    except Exception as e:
        print(f"Error general obteniendo información de placa base: {e}")
    return "Desconocido", "Desconocido", "Desconocido"

def get_system_uuid():
    """Obtiene el UUID del sistema usando PowerShell"""
    try:
        cmd = 'powershell -NoProfile -Command "Get-CimInstance -ClassName Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"'
        try:
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10  # Aumentar timeout a 10 segundos
            )
            if process.returncode == 0 and process.stdout.strip():
                return process.stdout.strip()
            else:
                print(f"Error al obtener UUID: {process.stderr}")
        except subprocess.TimeoutExpired:
            print("Tiempo de espera agotado al obtener UUID del sistema")
        except Exception as e:
            print(f"Error ejecutando comando para UUID: {e}")
        try:
            print("Intentando método alternativo con WMI para UUID...")
            wmi_obj = wmi.WMI()
            for system in wmi_obj.Win32_ComputerSystemProduct():
                if system.UUID:
                    print("Método alternativo exitoso para UUID")
                    return system.UUID.strip()
        except Exception as e:
            print(f"Error en método alternativo para UUID: {e}")
    except Exception as e:
        print(f"Error general obteniendo UUID del sistema: {e}")
    return None

def get_disk_serial_number(drive_letter):
    """Obtiene el número de serie del disco usando PowerShell"""
    try:
        drive_letter_clean = drive_letter.replace(":", "")
        cmd = f'powershell -NoProfile -Command "(Get-Partition -DriveLetter {drive_letter_clean} -ErrorAction SilentlyContinue).DiskNumber"'
        try:
            disk_num_process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10  # Aumentar timeout a 10 segundos
            )
            if disk_num_process.returncode == 0 and disk_num_process.stdout.strip():
                disk_number = disk_num_process.stdout.strip()
                cmd = f'powershell -NoProfile -Command "Get-Disk -Number {disk_number} | Select-Object -ExpandProperty SerialNumber"'
                try:
                    serial_process = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        shell=True,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        timeout=10  # Aumentar timeout a 10 segundos
                    )
                    if serial_process.returncode == 0 and serial_process.stdout.strip():
                        return serial_process.stdout.strip()
                    else:
                        print(f"No se pudo obtener el número de serie, código de retorno: {serial_process.returncode}, error: {serial_process.stderr}")
                except subprocess.TimeoutExpired:
                    print(f"Tiempo de espera agotado al obtener número de serie para el disco {disk_number}")
                except Exception as e:
                    print(f"Error obteniendo número de serie del disco {disk_number}: {e}")
            else:
                print(f"No se pudo obtener el número de disco para {drive_letter}, código de retorno: {disk_num_process.returncode}, error: {disk_num_process.stderr}")
        except subprocess.TimeoutExpired:
            print(f"Tiempo de espera agotado al obtener número de disco para {drive_letter}")
        except Exception as e:
            print(f"Error obteniendo número de disco: {e}")
    except Exception as e:
        print(f"Error general obteniendo número de serie: {e}")
    try:
        print(f"Intentando método alternativo con WMI para {drive_letter}...")
        drive_letter_clean = drive_letter.replace(":", "")
        wmi_obj = wmi.WMI()
        for physical_disk in wmi_obj.Win32_DiskDrive():
            for partition in physical_disk.associators("Win32_DiskDriveToDiskPartition"):
                for logical_disk in partition.associators("Win32_LogicalDiskToPartition"):
                    if logical_disk.DeviceID.lower() == f"{drive_letter_clean.lower()}:":
                        serial = physical_disk.SerialNumber
                        if serial:
                            print(f"Método alternativo exitoso para {drive_letter}")
                            return serial.strip()
    except Exception as e:
        print(f"Error en método alternativo: {e}")
    return "Serial number not found"

def generate_unique_code():
    try:
        serial_number, model, manufacturer = get_motherboard_info()
        disk_serial = get_disk_serial_number("C:")
        current_date = datetime.now().strftime("%Y-%m-%d")
        combined_info = f"{model}|{disk_serial}|{current_date}"
        encoded = base64.urlsafe_b64encode(combined_info.encode('utf-8')).decode('utf-8')
        formatted = '-'.join([encoded[i:i+4] for i in range(0, len(encoded), 4)])
        return formatted
    except Exception as e:
        print(f"Error generando código: {str(e)}")
        return None

def get_original_info(formatted_code):
    try:
        code = formatted_code.replace('-', '')
        decoded_bytes = base64.urlsafe_b64decode(code)
        decoded_text = decoded_bytes.decode('utf-8')
        parts = decoded_text.split('|')
        if len(parts) < 3:  # Al menos modelo, fabricante y fecha
            raise ValueError("Formato de información inválido")
        return decoded_text
    except Exception as e:
        print(f"Error desencriptando código: {e}")
        raise ValueError("Código inválido")

def get_disk_model_direct(drive_letter):
    """
    Obtiene el modelo del disco directamente desde Windows sin consultas lentas.
    Esta función es ultrarrápida y se utiliza para mostrar las propiedades del disco.
    """
    try:
        # Ensure drive letter is properly formatted
        if not drive_letter.endswith(":"):
            drive_letter = f"{drive_letter}:"
            
        # If drive doesn't exist, return quickly
        if not os.path.exists(f"{drive_letter}\\"):
            return "Unidad no disponible"
            
        # Get basic info (fast operations)
        drive_type = win32file.GetDriveType(f"{drive_letter}\\")
        try:
            volume_info = win32api.GetVolumeInformation(f"{drive_letter}\\")
            fs_type = volume_info[4] if volume_info and len(volume_info) > 4 else ""
        except:
            fs_type = ""
            
        # Determine base type
        if drive_type == win32file.DRIVE_UNKNOWN:
            base_type = "Unidad desconocida"
        elif drive_type == win32file.DRIVE_NO_ROOT_DIR:
            base_type = "Unidad no disponible"
        elif drive_type == win32file.DRIVE_REMOVABLE:
            base_type = "Dispositivo extraíble"
        elif drive_type == win32file.DRIVE_FIXED:
            if drive_letter.upper() == "C:":
                base_type = "Disco del sistema"
            else:
                base_type = "Disco interno"
        elif drive_type == win32file.DRIVE_REMOTE:
            base_type = "Unidad de red"
        elif drive_type == win32file.DRIVE_CDROM:
            base_type = "Unidad óptica"
        elif drive_type == win32file.DRIVE_RAMDISK:
            base_type = "Disco RAM"
        else:
            base_type = "Dispositivo de almacenamiento"
            
        # Add filesystem info if available
        if fs_type:
            model = f"{base_type} ({fs_type})"
        else:
            model = base_type
            
        # Start background improvement if not already in progress
        try:
            if drive_letter not in _DISCOS_EN_MEJORA:
                _DISCOS_EN_MEJORA.append(drive_letter)
                threading.Thread(
                    target=_mejorar_modelo_individual,
                    args=(drive_letter,),
                    daemon=True
                ).start()
        except:
            pass
            
        return model
    except Exception as e:
        print(f"Error obteniendo modelo rápido: {e}")
        return "Dispositivo de almacenamiento"

_DISCOS_EN_MEJORA = []  # Sin uso cuando la caché está desactivada

def _mejorar_modelo_individual(drive_letter):
    """Mejora el modelo de un disco específico en segundo plano"""
    # Mejora desactivada cuando no se usa caché
    return False

_DISK_MODELS_CACHE = {}

def init_disk_models_cache():
    # Caché desactivada: no realizar inicialización
    return

def _improve_disk_models_cache(drives):
    # Caché desactivada
    return


_DISK_MODELS_CACHE = {}
_USB_PORT_CACHE = {}

# Semáforo global para sincronización de acceso a la caché
_USB_PORT_CACHE_LOCK = threading.Semaphore(1)



def get_usb_port_info(drive_letter):
    """
    Función global que usa la nueva implementación optimizada.
    """
    return get_usb_port_info_fast(drive_letter)

# Variables globales para caché
_DISK_MODELS_CACHE = {}
# _USB_PORT_CACHE ahora se importa desde MAPEO_PUERTO


def preload_all_caches():
    # No-op: desactivado para mejorar rendimiento
    return

if __name__ == "__main__":
    unique_code = generate_unique_code()
    print(f"Unique Code: {unique_code}")
    print(f"Original Info: {get_original_info(unique_code)}")

def get_usb_port_info_fast(drive_letter):
    """
    Función optimizada que usa caché primero, luego consulta si es necesario
    """
    # Primero intentar obtener desde caché
    port_id, hub_info = get_cached_usb_port_info(drive_letter)
    
    if port_id and port_id != "No disponible":
        print(f"Puerto USB obtenido desde caché para {drive_letter}: {port_id} ({hub_info})")
        return port_id, hub_info
    
    # Si no está en caché, usar el método directo
    try:
        from MAPEO_PUERTO import get_port_info_for_drive
        
        print(f"Consultando puerto USB para {drive_letter} (no en caché)...")
        port_id, hub_info = get_port_info_for_drive(drive_letter)
        
        # Guardar en caché para futuras consultas
        if port_id and port_id != "No disponible":
            with _USB_PORT_CACHE_LOCK:
                _USB_PORT_CACHE[drive_letter] = {
                    'port_id': port_id,
                    'hub_info': hub_info,
                    'timestamp': time.time()
                }
            print(f"Puerto USB cacheado tras consulta: {port_id} ({hub_info})")
        
        return port_id, hub_info
    except Exception as e:
        print(f"Error obteniendo información de puerto USB para {drive_letter}: {e}")
        return "No disponible", "No disponible"







def load_disk_models_cache():
    """Carga modelos de discos con comando ultra-optimizado"""
    global _DISK_MODELS_CACHE, _DRIVE_TO_MODEL_CACHE, _MODELS_LOADED
    
    if _MODELS_LOADED:
        return
    
    try:
        print("🔍 Cargando modelos de discos con comando optimizado...")
        
        # Tu comando ultra-compacto y rápido
        powershell_cmd = """
        $d=Get-CimInstance Win32_DiskDrive -Property Index,Model|Sort Index
        $m=@{}
        Get-CimInstance Win32_DiskPartition|?{$_.DiskIndex -ne $null}|%{
            $i=$_.DiskIndex
            Get-CimInstance -Query "ASSOCIATORS OF {Win32_DiskPartition.DeviceID='$($_.DeviceID)'} WHERE AssocClass=Win32_LogicalDiskToPartition"|%{
                if($_.DeviceID){
                    $l=$_.DeviceID-replace':'
                    if(!$m[$i]){$m[$i]=@()}
                    $m[$i]+=$l
                }
            }
        }
        
        # Crear resultado con mapeo directo letra -> modelo
        $result = @{}
        $d|%{
            $diskNum = $_.Index
            $model = if($_.Model){$_.Model.Trim()}else{""}
            $letters = $m[$diskNum]
            if($letters -and $model){
                $letters|%{
                    $result[$_] = $model
                }
            }
        }
        
        $result | ConvertTo-Json
        """
        
        result = subprocess.run(
            ["powershell", "-NoProfile", "-Command", powershell_cmd],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW,
            timeout=15
        )
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                drive_mapping = json.loads(result.stdout.strip())
                
                with _DISK_MODELS_CACHE_LOCK:
                    # Crear mapeo directo letra -> modelo
                    for drive_letter, model in drive_mapping.items():
                        _DRIVE_TO_MODEL_CACHE[f"{drive_letter}:"] = model
                        print(f"  {drive_letter}: -> {model}")
                
                _MODELS_LOADED = True
                print(f"✅ Mapeo completado: {len(_DRIVE_TO_MODEL_CACHE)} unidades con modelos")
                        
            except json.JSONDecodeError as e:
                print(f"❌ Error parseando JSON: {e}")
                _MODELS_LOADED = True
                
    except Exception as e:
        print(f"❌ Error cargando modelos: {e}")
        _MODELS_LOADED = True

def create_drive_to_model_mapping():
    """Crea un mapeo directo de letras de unidad a modelos ULTRARRÁPIDO"""
    global _DRIVE_TO_MODEL_CACHE, _MODELS_LOADED
    
    try:
        # Comando ultra-compacto y rápido usando solo registro del sistema
        powershell_cmd = """
        $d=Get-CimInstance Win32_DiskDrive -Property Index,Model|Sort Index
        $m=@{}
        Get-CimInstance Win32_DiskPartition|?{$_.DiskIndex -ne $null}|%{
            $i=$_.DiskIndex
            Get-CimInstance -Query "ASSOCIATORS OF {Win32_DiskPartition.DeviceID='$($_.DeviceID)'} WHERE AssocClass=Win32_LogicalDiskToPartition"|%{
                if($_.DeviceID){
                    $l=$_.DeviceID-replace':'
                    if(!$m[$i]){$m[$i]=@()}
                    $m[$i]+=$l
                }
            }
        }
        
        # Crear resultado con mapeo directo letra -> modelo
        $result = @{}
        $d|%{
            $diskNum = $_.Index
            $model = if($_.Model){$_.Model.Trim()}else{""}
            $letters = $m[$diskNum]
            if($letters -and $model){
                $letters|%{
                    $result[$_] = $model
                }
            }
        }
        
        $result | ConvertTo-Json
        """
        
        result = subprocess.run(
            ["powershell", "-NoProfile", "-Command", powershell_cmd],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW,
            timeout=10
        )
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                drive_mapping = json.loads(result.stdout.strip())
                
                print(f"Unidades encontradas: {list(drive_mapping.keys())}")
                
                with _DISK_MODELS_CACHE_LOCK:
                    # Crear mapeo directo letra -> modelo
                    for drive_letter, model in drive_mapping.items():
                        _DRIVE_TO_MODEL_CACHE[f"{drive_letter}:"] = model
                        print(f"  {drive_letter}: -> {model}")
                
                print(f"✅ Mapeo de unidades completado: {len(_DRIVE_TO_MODEL_CACHE)} unidades")
                        
            except json.JSONDecodeError as e:
                print(f"❌ Error parseando mapeo de unidades: {e}")
                
    except Exception as e:
        print(f"❌ Error creando mapeo de unidades: {e}")

def load_disk_models_cache_sync():
    """Versión síncrona que espera a que el mapeo esté completo"""
    load_disk_models_cache()
    
    # Esperar a que el mapeo esté completo
    import time
    max_wait = 10  # Máximo 10 segundos
    wait_time = 0
    
    while len(_DRIVE_TO_MODEL_CACHE) == 0 and wait_time < max_wait:
        time.sleep(0.5)
        wait_time += 0.5
    
    print(f"🎯 Mapeo completado: {len(_DRIVE_TO_MODEL_CACHE)} unidades listas para usar")

def get_disk_model_from_cache(drive_letter):
    """Obtiene el modelo del disco desde la cache global"""
    global _DISK_MODELS_CACHE, _DRIVE_TO_MODEL_CACHE, _MODELS_LOADED
    
    try:
        # Asegurar que la cache de modelos esté cargada
        if not _MODELS_LOADED:
            load_disk_models_cache()
        
        # Normalizar la letra de unidad
        if not drive_letter.endswith(':'):
            drive_letter = f"{drive_letter}:"
        
        # Obtener directamente desde la cache (sin PowerShell)
        with _DISK_MODELS_CACHE_LOCK:
            return _DRIVE_TO_MODEL_CACHE.get(drive_letter, "")
        
    except Exception as e:
        print(f"Error obteniendo modelo desde cache: {e}")
        return ""

def get_disk_number_for_drive(drive_letter):
    """Obtiene el número de disco físico para una letra de unidad específica (método simplificado)"""
    try:
        # Método más directo usando Get-Partition
        powershell_cmd = f"""
        try {{
            $partition = Get-Partition -DriveLetter {drive_letter} -ErrorAction SilentlyContinue
            if ($partition) {{
                $partition.DiskNumber
            }}
        }} catch {{
            # Método de respaldo usando WMI
            $logicalDisk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='{drive_letter}:'"
            if ($logicalDisk) {{
                $partition = Get-WmiObject -Class Win32_LogicalDiskToPartition | Where-Object {{ $_.Dependent -like "*{drive_letter}:*" }}
                if ($partition) {{
                    $diskDrive = Get-WmiObject -Class Win32_DiskDriveToDiskPartition | Where-Object {{ $_.Dependent -eq $partition.Antecedent }}
                    if ($diskDrive) {{
                        $physicalDisk = Get-WmiObject -Class Win32_DiskDrive | Where-Object {{ $_.DeviceID -like "*$($diskDrive.Antecedent.Split('=')[1].Trim('"').Split('\')[-1])*" }}
                        if ($physicalDisk) {{
                            $physicalDisk.Index
                        }}
                    }}
                }}
            }}
        }}
        """
        
        result = subprocess.run(
            ["powershell", "-NoProfile", "-Command", powershell_cmd],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                return int(result.stdout.strip())
            except ValueError:
                pass
        
        return None
        
    except Exception as e:
        print(f"Error obteniendo número de disco para {drive_letter}: {e}")
        return None
