from PyQt6.QtWidgets import <PERSON><PERSON>ialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QFrame, QGraphicsDropShadowEffect, QLineEdit
from PyQt6.QtCore import Qt, Q<PERSON>imer, QSize, QP<PERSON>tyAnimation, pyqtProperty, QPoint
from PyQt6.QtGui import QIcon, QPixmap, QColor, QPainter, QPainterPath
import os
import json
from io import BytesIO
import AJUSTES
import hashlib
from APARIENCIA import apply_acrylic_and_rounded
from APARIENCIA import (
    ACCENT_POLICY, 
    WIND<PERSON>COMPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, 
    WCA_ACCENT_POLICY,
    DWMWA_WINDOW_CORNER_PREFERENCE,
    DWMWCP_ROUND,
    ACCENT_ENABLE_FLUENT,
    is_windows_11_or_greater
)
import ctypes

class AnimatedButton(QPushButton):
    def __init__(self, icon_path=None, parent=None):
        super().__init__(parent)
        if icon_path:
            self.setIcon(QIcon(icon_path))
            self.setIconSize(QSize(25, 25))  # Initial icon size
        self.setStyleSheet("border: none; background: transparent;")
        self.setMinimumSize(35, 35)
        self.setMaximumSize(35, 35)
        AnimatedButton.add_shadow_effect(self, Qt.GlobalColor.white)
        self.setMouseTracking(True)
        self._icon_size = 25

    @staticmethod
    def add_shadow_effect(widget, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

    @pyqtProperty(int)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QSize(size, size))

    def enterEvent(self, event):
        self.animate_icon_size(30)  # Enlarged icon size

    def leaveEvent(self, event):
        self.animate_icon_size(25)  # Original icon size

    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(50)  # Duration of the animation in milliseconds
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

class CloseConfirmationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Popup | 
            Qt.WindowType.NoDropShadowWindowHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        
        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Mensaje
        message_label = QLabel("¿Desea cerrar la aplicación?")
        message_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                background: transparent;
            }
        """)
        layout.addWidget(message_label)
        
        # Botones
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.accept_button = QPushButton("Aceptar")
        self.cancel_button = QPushButton("Cancelar")
        
        for button in [self.accept_button, self.cancel_button]:
            button.setStyleSheet("""
                QPushButton {
                    background-color: rgba(43, 43, 43, 0.5);
                    color: white;
                    border: none;
                    border-radius: 15px;
                    padding: 8px 16px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: rgba(59, 59, 59, 0.6);
                }
                QPushButton:pressed {
                    background-color: rgba(27, 27, 27, 0.7);
                }
            """)
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(10)
            shadow.setXOffset(0)
            shadow.setYOffset(0)
            shadow.setColor(QColor(0, 0, 0, 60))
            button.setGraphicsEffect(shadow)
            
        button_layout.addWidget(self.accept_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        # Conectar botones
        self.accept_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)

    def showEvent(self, event):
        super().showEvent(event)
        # Aplicar el efecto según la versión de Windows
        hwnd = int(self.winId())
        accent = ACCENT_POLICY()
        if is_windows_11_or_greater():
            accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
            accent.GradientColor = 0x01000000
        else:
            accent.AccentState = ACCENT_ENABLE_FLUENT
            accent.GradientColor = 0xCC000000  # 80% de opacidad para el efecto Fluent
        accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
        
        data = WINDOWCOMPOSITIONATTRIBDATA()
        data.Attribute = WCA_ACCENT_POLICY
        data.SizeOfData = ctypes.sizeof(accent)
        data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
        ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))
        
        # Aplicar bordes redondeados
        ctypes.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            DWMWA_WINDOW_CORNER_PREFERENCE,
            ctypes.byref(ctypes.c_int(DWMWCP_ROUND)),
            ctypes.sizeof(ctypes.c_int)
        )
        
        # Animación de aparición
        self.final_pos = self.pos()
        start_pos = self.final_pos + QPoint(0, 50)
        self.move(start_pos)
        
        anim = QPropertyAnimation(self, b"pos")
        anim.setDuration(300)
        anim.setStartValue(start_pos)
        anim.setEndValue(self.final_pos)
        anim.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()),
            float(rect.y()),
            float(rect.width()),
            float(rect.height()),
            8.0,
            8.0
        )
        # Reducimos la opacidad del fondo negro para coincidir con los otros diálogos
        painter.fillPath(path, QColor(0, 0, 0, 15))

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Escape:
            self.reject()
        elif event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            self.accept()
        else:
            super().keyPressEvent(event)

class ExtensionDiffDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Extensión diferente")
        
        layout = QVBoxLayout(self)
        
        # Mostrar un mensaje genérico
        self.message_label = QLabel(
            "La extensión del siguiente archivo es diferente.\n\n"
            "¿Desea continuar con la copia?"
        )
        self.message_label.setStyleSheet("color: white; font-size: 14px;")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.message_label)
        
        # Botones de aceptar y cancelar
        self.accept_button = QPushButton("Continuar")
        self.accept_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)
        
        layout.addWidget(self.accept_button)
        layout.addWidget(self.cancel_button)

class PortRenameDialog(QDialog):
    def __init__(self, parent=None, drive_letter="", port_info="", hub_info=""):
        super().__init__(parent)
        self.drive_letter = drive_letter
        self.port_info = port_info
        self.hub_info = hub_info
        
        print(f"Creando diálogo para: {drive_letter}, Puerto: {port_info}, Hub: {hub_info}")
        
        # Configurar ventana
        self.setWindowTitle("Renombrar Puerto USB")
        self.setModal(True)
        self.resize(400, 300)
        
        # Aplicar estilo de ventana
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        
        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Título
        title_label = QLabel(f"Información del Puerto - Unidad {drive_letter}")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                padding: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # Información del puerto
        port_label = QLabel(f"Puerto: {port_info}")
        port_label.setStyleSheet("""
            QLabel {
                color: #4285F4;
                font-size: 14px;
                background: transparent;
                padding: 5px;
            }
        """)
        layout.addWidget(port_label)
        
        # Información del hub
        hub_label = QLabel(f"Hub: {hub_info}")
        hub_label.setStyleSheet("""
            QLabel {
                color: #34A853;
                font-size: 14px;
                background: transparent;
                padding: 5px;
            }
        """)
        layout.addWidget(hub_label)
        
        # Mostrar nombre actual si existe
        current_name = self.get_current_port_name()
        if current_name:
            current_label = QLabel(f"Nombre actual: {current_name}")
            current_label.setStyleSheet("""
                QLabel {
                    color: #FFA500;
                    font-size: 14px;
                    background: transparent;
                    padding: 5px;
                    font-weight: bold;
                }
            """)
            layout.addWidget(current_label)
        
        # Campo de entrada para nombre personalizado
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Nombre personalizado para este puerto...")
        
        # Pre-llenar con el nombre actual si existe
        if current_name:
            self.name_input.setText(current_name)
        
        self.name_input.setStyleSheet("""
            QLineEdit {
                background-color: rgba(43, 43, 43, 0.8);
                color: white;
                border: 2px solid #4285F4;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 2px solid #34A853;
            }
        """)
        layout.addWidget(self.name_input)
        
        # Botones
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.cancel_button = QPushButton("Cancelar")
        self.delete_button = QPushButton("Eliminar Nombre")
        self.save_button = QPushButton("Guardar")
        
        # Estilo para botones
        button_style = """
            QPushButton {
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3367D6;
            }
            QPushButton:pressed {
                background-color: #2C5AA0;
            }
        """
        
        self.cancel_button.setStyleSheet(button_style + "QPushButton { background-color: #6C757D; }")
        self.delete_button.setStyleSheet(button_style + "QPushButton { background-color: #DC3545; }")
        self.save_button.setStyleSheet(button_style + "QPushButton { background-color: #4285F4; }")
        
        # Solo mostrar botón eliminar si hay nombre actual
        if current_name:
            button_layout.addWidget(self.delete_button)
            self.delete_button.clicked.connect(self.delete_port_name)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)
        layout.addLayout(button_layout)
        
        # Conectar botones
        self.save_button.clicked.connect(self.save_port_name)
        self.cancel_button.clicked.connect(self.reject)
        
        # Focus en el campo de entrada
        self.name_input.setFocus()
        self.name_input.selectAll()  # Seleccionar todo el texto si hay nombre actual
        
        print("Diálogo creado correctamente")

    def get_current_port_name(self):
        """Obtener el nombre actual del puerto si existe"""
        try:
            config_file = "port_names.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    port_names = json.load(f)
                    return port_names.get(self.port_info, "")
        except Exception as e:
            print(f"Error leyendo nombres de puertos: {e}")
        return ""

    def delete_port_name(self):
        """Eliminar el nombre del puerto"""
        try:
            config_file = "port_names.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    port_names = json.load(f)
                
                if self.port_info in port_names:
                    del port_names[self.port_info]
                    
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(port_names, f, indent=2, ensure_ascii=False)
                    
                    print(f"Nombre eliminado para puerto: {self.port_info}")
                    self.accept()
        except Exception as e:
            print(f"Error eliminando nombre del puerto: {e}")

    def save_port_name(self):
        """Guardar el nombre personalizado del puerto"""
        custom_name = self.name_input.text().strip()
        print(f"Intentando guardar nombre: '{custom_name}' para puerto: {self.port_info}")
        
        if custom_name:
            try:
                # Guardar en archivo de configuración
                config_file = "port_names.json"
                port_names = {}
                
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        port_names = json.load(f)
                
                port_names[self.port_info] = custom_name
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(port_names, f, indent=2, ensure_ascii=False)
                
                print(f"Puerto {self.port_info} renombrado como: {custom_name}")
                self.accept()
                
            except Exception as e:
                print(f"Error guardando nombre del puerto: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("Nombre vacío, cancelando...")
            self.reject()

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Escape:
            self.reject()
        elif event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            self.save_port_name()
        else:
            super().keyPressEvent(event)




