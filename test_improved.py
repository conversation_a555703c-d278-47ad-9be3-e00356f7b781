#!/usr/bin/env python3
"""
Script para probar la implementación mejorada de mapeo de puertos USB
"""

import sys
import os

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from MAPEO_PUERTO import get_port_info_with_usbdeview, get_port_info_for_drive

def test_improved_mapping():
    """Probar el mapeo mejorado de puertos"""
    print("=== PRUEBA DE MAPEO MEJORADO DE PUERTOS USB ===")
    print()
    
    # Letras de unidad que sabemos que existen basadas en el CSV
    test_drives = ['H', 'N', 'E', 'K']
    
    for drive in test_drives:
        print(f"Probando unidad {drive}:")
        try:
            # Probar con la función mejorada de USBDeview
            port_info, hub_info = get_port_info_with_usbdeview(drive)
            print(f"  USBDeview mejorado -> Puerto: {port_info}, Hub: {hub_info}")
            
        except Exception as e:
            print(f"  Error: {e}")
        
        print()

def test_main_function():
    """Probar la función principal que ahora usa USBDeview primero"""
    print("=== PRUEBA DE FUNCIÓN PRINCIPAL ===")
    print()
    
    test_drives = ['H', 'N', 'E', 'K']
    
    for drive in test_drives:
        print(f"Probando unidad {drive}: con función principal:")
        try:
            port_info, hub_info = get_port_info_for_drive(drive)
            print(f"  Resultado -> Puerto: {port_info}, Hub: {hub_info}")
            
        except Exception as e:
            print(f"  Error: {e}")
        
        print()

if __name__ == "__main__":
    print("Iniciando pruebas del sistema mejorado de mapeo de puertos USB...")
    print()
    
    test_improved_mapping()
    test_main_function()
    
    print("Pruebas completadas.")
