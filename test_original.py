#!/usr/bin/env python3
"""
Script para probar el comportamiento del MAPEO_PUERTO original
"""

import sys
import os

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from MAPEO_PUERTO import get_port_info_for_drive, USBPortMapper

def test_individual_drives():
    """Probar cada unidad individualmente"""
    print("=== PRUEBA INDIVIDUAL DE UNIDADES ===")
    print()
    
    # Letras de unidad que sabemos que existen
    test_drives = ['H', 'N', 'E', 'K']
    
    for drive in test_drives:
        print(f"Probando unidad {drive}:")
        try:
            port_info, hub_info = get_port_info_for_drive(drive)
            print(f"  ✓ Puerto: {port_info}")
            print(f"  ✓ Hub: {hub_info}")
            
        except Exception as e:
            print(f"  ✗ Error: {e}")
        
        print()

def test_usb_mapper():
    """Probar el USBPortMapper"""
    print("=== PRUEBA DE USB PORT MAPPER ===")
    print()
    
    try:
        mapper = USBPortMapper()
        print("Mapper creado correctamente")
        
        print("Obteniendo dispositivos USB...")
        devices = mapper.get_usb_devices()
        
        print(f"Dispositivos encontrados: {len(devices)}")
        
        if devices:
            print("\nPrimeros 3 dispositivos:")
            for i, device in enumerate(devices[:3]):
                print(f"  {i+1}. Modelo: {device.get('Model', 'N/A')}")
                print(f"     Letras: {device.get('DriveLetters', 'N/A')}")
                print(f"     Puerto: {device.get('PortInfo', 'N/A')}")
                print(f"     Hub: {device.get('HubInfo', 'N/A')}")
                print()
        else:
            print("No se encontraron dispositivos")
            
    except Exception as e:
        print(f"Error con USBPortMapper: {e}")

def test_simple_powershell():
    """Probar un comando PowerShell simple"""
    print("=== PRUEBA DE POWERSHELL SIMPLE ===")
    print()
    
    import subprocess
    
    try:
        # Comando simple para listar unidades USB
        simple_script = '''
        Get-Volume | Where-Object { $_.DriveType -eq "Removable" } | Select-Object DriveLetter, FileSystemLabel | ConvertTo-Json
        '''
        
        result = subprocess.run(
            ["powershell", "-Command", simple_script],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print(f"Return code: {result.returncode}")
        print(f"Output: {result.stdout}")
        if result.stderr:
            print(f"Error: {result.stderr}")
            
    except Exception as e:
        print(f"Error ejecutando PowerShell simple: {e}")

if __name__ == "__main__":
    print("Probando comportamiento del MAPEO_PUERTO original...")
    print()
    
    test_simple_powershell()
    test_individual_drives()
    test_usb_mapper()
    
    print("Pruebas completadas.")
