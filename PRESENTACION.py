from PyQt6.QtWidgets import (QSplashScreen, QApplication, QGraphicsOpacityEffect, QWidget, QLabel, QGraphicsDropShadowEffect)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRectF, QPoint, QPointF, QSize
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor, QRadialGradient, QPainterPath, QPen, QLinearGradient, QIcon, QFontMetrics, QConicalGradient
import os, sys
import random
import math
from APARIENCIA import apply_acrylic_and_rounded, is_windows_11_or_greater
import win32file
from MONITOREO import get_disk_model_direct

import getpass # Importar getpass para obtener el nombre de usuario

def mapear_discos_antes_de_abrir_app():
    """Función deshabilitada - el mapeo ahora se hace solo al pulsar F4."""
    print("Mapeo automático deshabilitado - usar F4 para mapear puertos individuales.")
    pass

class IconShadowEffect(QWidget):
    def __init__(self, pixmap, parent=None):
        super().__init__(parent)
        self.pixmap = pixmap
        self.opacity = 0.5
        self.blur_radius = 15
        self.setFixedSize(pixmap.size() + QSize(30, 30))
        self._cached_shadow = None
        self._create_cached_shadow()
        
    def _create_cached_shadow(self):
        """Pre-renderiza la sombra para mejorar el rendimiento"""
        self._cached_shadow = QPixmap(self.size())
        self._cached_shadow.fill(Qt.GlobalColor.transparent)
        painter = QPainter(self._cached_shadow)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        x = (self.width() - self.pixmap.width()) // 2
        y = (self.height() - self.pixmap.height()) // 2
        
        for offset in range(self.blur_radius, 0, -1):
            opacity = int(120 * self.opacity * (offset / self.blur_radius))
            painter.save()
            painter.translate(x + offset/2, y + offset/2)
            painter.setOpacity(opacity/255.0)
            
            shadow_pixmap = QPixmap(self.pixmap.size())
            shadow_pixmap.fill(QColor(0, 0, 0, opacity))
            shadow_mask = self.pixmap.mask()
            shadow_pixmap.setMask(shadow_mask)
            
            painter.drawPixmap(0, 0, shadow_pixmap)
            painter.restore()
        
        painter.end()
        
    def paintEvent(self, event):
        if not self._cached_shadow:
            self._create_cached_shadow()
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Dibujar la sombra pre-renderizada
        painter.drawPixmap(0, 0, self._cached_shadow)
        
        # Dibujar el icono
        x = (self.width() - self.pixmap.width()) // 2
        y = (self.height() - self.pixmap.height()) // 2
        painter.drawPixmap(x, y, self.pixmap)

class ModernZWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(200, 200)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        w = self.width()
        h = self.height()
        
        path = QPainterPath()
        
        # Z moderna invertida
        path.moveTo(w * 0.8, h * 0.2)
        path.lineTo(w * 0.2, h * 0.2)
        path.lineTo(w * 0.25, h * 0.25)
        path.lineTo(w * 0.7, h * 0.25)
        path.lineTo(w * 0.3, h * 0.75)
        path.lineTo(w * 0.75, h * 0.75)
        path.lineTo(w * 0.8, h * 0.8)
        path.lineTo(w * 0.2, h * 0.8)
        path.closeSubpath()
        
        thickness = int(w * 0.08)
        
        # Sombras en gris oscuro para la Z
        for i in range(8):
            shadow_path = QPainterPath(path)
            opacity = 70 - i * 7
            shadow_pen = QPen(QColor(40, 40, 40, opacity), thickness)
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            
            painter.setPen(shadow_pen)
            painter.drawPath(shadow_path.translated(i + 2.5, i + 2.5))
        
        # Gradiente blanco para la Z
        gradient = QLinearGradient(w, 0, 0, h)
        gradient.setColorAt(0.0, QColor(255, 255, 255))  # Blanco puro
        gradient.setColorAt(1.0, QColor(240, 240, 240))  # Blanco suave
        
        painter.fillPath(path, gradient)
        
        # Borde blanco brillante
        main_pen = QPen(QColor(255, 255, 255))
        main_pen.setWidth(thickness)
        main_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        main_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        
        painter.setPen(main_pen)
        painter.drawPath(path)
        
        # Brillo superior
        highlight = QLinearGradient(w, 0, w * 0.5, h * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 90))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        
        highlight_path = QPainterPath()
        highlight_path.moveTo(w * 0.8, h * 0.2)
        highlight_path.lineTo(w * 0.2, h * 0.2)
        highlight_path.lineTo(w * 0.5, h * 0.4)
        highlight_path.closeSubpath()
        
        painter.fillPath(highlight_path, highlight)

    def create_icon(self, size=64):
        icon_pixmap = QPixmap(size, size)
        icon_pixmap.fill(Qt.GlobalColor.transparent)
        painter = QPainter(icon_pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        w = size
        h = size
        center_x = w // 2
        center_y = h // 2
        outer_radius = int(w * 0.42)
        
        # Sombra del aro
        for i in range(2):
            shadow_pen = QPen()
            shadow_pen.setWidth(3)
            shadow_pen.setColor(QColor(0, 0, 0, 40 - i * 15))
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            painter.setPen(shadow_pen)
            painter.drawEllipse(QPoint(center_x + i + 1, center_y + i + 1), outer_radius, outer_radius)
        
        # Aro principal con gradiente
        gradient_outer = QLinearGradient(0, center_y - outer_radius, 0, center_y + outer_radius)
        gradient_outer.setColorAt(0.0, QColor(255, 82, 82))
        gradient_outer.setColorAt(0.5, QColor(244, 67, 54))
        gradient_outer.setColorAt(1.0, QColor(211, 47, 47))
        
        pen_outer = QPen()
        pen_outer.setWidth(3)
        pen_outer.setBrush(gradient_outer)
        pen_outer.setCapStyle(Qt.PenCapStyle.RoundCap)
        pen_outer.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        painter.setPen(pen_outer)
        painter.drawEllipse(QPoint(center_x, center_y), outer_radius, outer_radius)
        
        # Z moderna en el centro
        path = QPainterPath()
        z_size = 0.35  # Reducido para mantener proporción con el aro más pequeño
        
        # Líneas de la Z
        path.moveTo(w * (0.5 - z_size/2), h * (0.5 - z_size/2))
        path.lineTo(w * (0.5 + z_size/2), h * (0.5 - z_size/2))
        path.lineTo(w * (0.5 - z_size/2), h * (0.5 + z_size/2))
        path.lineTo(w * (0.5 + z_size/2), h * (0.5 + z_size/2))
        
        thickness = int(w * 0.09)  # Ajustado el grosor de la Z
        
        # Sombra de la Z
        for i in range(2):
            shadow_path = QPainterPath(path)
            shadow_pen = QPen(QColor(0, 0, 0, 40 - i * 10), thickness)
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            painter.setPen(shadow_pen)
            painter.drawPath(shadow_path.translated(i + 1, i + 1))
        
        # Z principal con gradiente
        gradient = QLinearGradient(0, 0, 0, h)
        gradient.setColorAt(0.0, QColor(231, 76, 60))  # Rojo coral moderno
        gradient.setColorAt(0.3, QColor(219, 50, 54))  # Rojo medio elegante
        gradient.setColorAt(0.7, QColor(192, 57, 43))  # Rojo profundo suave
        gradient.setColorAt(1.0, QColor(165, 42, 42))  # Rojo oscuro elegante
        
        main_pen = QPen()
        main_pen.setBrush(gradient)
        main_pen.setWidth(thickness)
        main_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        main_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        
        painter.setPen(main_pen)
        painter.drawPath(path)
        
        # Brillo de la Z
        highlight = QLinearGradient(0, 0, 0, h * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 30))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        
        highlight_pen = QPen()
        highlight_pen.setBrush(highlight)
        highlight_pen.setWidth(int(thickness * 0.8))
        highlight_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        highlight_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        
        painter.setPen(highlight_pen)
        painter.drawPath(path.translated(-1, -1))
        
        painter.end()
        return QIcon(icon_pixmap)

    def draw_text(self, painter):
        text = "ZETACOPY"
        letter_height = 24
        spacing = 4
        stroke_width = 3
        
        font = QFont("Arial Black", letter_height)
        font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, spacing)
        font.setBold(True)
        
        text_path = QPainterPath()
        text_path.addText(0, 0, font, text)
        text_rect = text_path.boundingRect()
        
        start_x = (self.width() - text_rect.width()) // 2
        center_y = 180
        
        text_path.translate(start_x, center_y)
        
        # Sombras en gris oscuro para mejor efecto 3D
        for i in range(8):
            shadow_path = QPainterPath(text_path)
            opacity = 70 - i * 7
            # Sombra gris oscura
            shadow_pen = QPen(QColor(40, 40, 40, opacity), stroke_width)
            shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            shadow_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
            
            painter.setPen(shadow_pen)
            painter.drawPath(shadow_path.translated(i + 2, i + 2))
        
        # Gradiente blanco puro a blanco suave
        gradient = QLinearGradient(start_x, center_y - letter_height, 
                                 start_x + text_rect.width(), center_y)
        gradient.setColorAt(0.0, QColor(255, 255, 255))  # Blanco puro
        gradient.setColorAt(1.0, QColor(240, 240, 240))  # Blanco suave
        
        painter.fillPath(text_path, gradient)
        
        # Brillo sutil
        highlight = QLinearGradient(start_x + text_rect.width(), center_y - letter_height,
                                  start_x + text_rect.width() * 0.5, center_y - letter_height * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 50))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        
        highlight_path = QPainterPath()
        highlight_path.addPath(text_path)
        painter.fillPath(highlight_path, highlight)

class Presentacion(QSplashScreen):
    def __init__(self):
        # Tamaño base y configuración inicial
        # self.base_size = 190 # Eliminado
        # self.shadow_margin = 20 # Eliminado
        # self.total_size = self.base_size + (self.shadow_margin * 2) # Eliminado

        # Nuevas dimensiones para la ventana de presentación
        self.window_width = 270 # Ancho ajustado para el nuevo diseño horizontal
        self.window_height = 100 # Altura ajustada

        # Definir el margen de sombra
        self.shadow_margin = 20
        
        # Crear el pixmap base con el tamaño total
        base_pixmap = QPixmap(self.window_width, self.window_height)
        base_pixmap.fill(Qt.GlobalColor.transparent)
        
        super().__init__(base_pixmap)
        
        # Widget de fondo semitransparente para bloquear clics
        self.bg_blocker = QWidget(self)
        self.bg_blocker.setGeometry(0, 0, self.window_width, self.window_height)
        self.bg_blocker.setStyleSheet("background: rgba(0,0,0,0.01);")  # Casi invisible, pero bloquea clics
        self.bg_blocker.lower()  # Asegura que esté detrás de los demás widgets
        
        # Configurar la ventana
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        
        # Habilitar transparencia
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        
        # Configurar tamaños (estas líneas ya no son necesarias para el círculo)
        # self.circle_size = 170
        # self.light_angle = 0
        
        # Centrar en la pantalla
        self.center_window()
        
        # Configurar el icono y el texto
        self.setup_layout()
        
        # Configurar temporizadores
        self.setup_timers()

    def center_window(self):
        """Centra la ventana en la pantalla"""
        screen = QApplication.primaryScreen().geometry()
        self.move(
            (screen.width() - self.window_width) // 2,
            (screen.height() - self.window_height) // 2
        )

    def setup_layout(self):
        """Configura el icono y el texto en un diseño horizontal"""
        icon_path = os.path.join(os.path.dirname(__file__), 'ICONOS', 'ZETACOPY.ico')
        if os.path.exists(icon_path):
            self.icon_pixmap = QPixmap(icon_path)
            self.icon_pixmap = self.icon_pixmap.scaled(70, 70, 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation)
            
            self.icon_label = QLabel(self)
            self.icon_label.setPixmap(self.icon_pixmap)
            self.icon_label.setFixedSize(70, 70) # Asegurar el tamaño del QLabel
            self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            shadow = QGraphicsDropShadowEffect(self)
            shadow.setBlurRadius(15)
            shadow.setXOffset(0)
            shadow.setYOffset(0)
            shadow.setColor(QColor(0, 0, 0, 220))
            self.icon_label.setGraphicsEffect(shadow)

        self.text_label = QLabel("ZETACOPY", self)
        font_main = QFont("Arial Black", 16)
        font_main.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 1)
        font_main.setBold(True)
        self.text_label.setFont(font_main)
        self.text_label.setStyleSheet("color: #E85D42; background: transparent;")
        
        fm_main = QFontMetrics(font_main)
        text_width = fm_main.horizontalAdvance("ZETACOPY")
        text_height = fm_main.height()
        self.text_label.setFixedSize(text_width + 5, text_height + 5) # Ajustar el tamaño del QLabel para el texto principal
        self.text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        shadow_text = QGraphicsDropShadowEffect(self)
        shadow_text.setBlurRadius(15)
        shadow_text.setXOffset(0)
        shadow_text.setYOffset(0)
        shadow_text.setColor(QColor(0, 0, 0, 220))
        self.text_label.setGraphicsEffect(shadow_text)

        self.license_label = QLabel(self)
        font_license = QFont("Arial Black", 13)
        self.license_label.setFont(font_license)
        self.license_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.license_label.setFixedWidth(45)
        
        is_pro = self.check_license()
        if is_pro:
            self.license_label.setText("PRO")
            self.license_label.setStyleSheet("""
                QLabel {
                    color: #E85D42;
                    font-size: 13px;
                    font-weight: bold;
                    padding: 1px 4px;
                    background-color: rgba(128, 128, 128, 0.3);
                    border-radius: 9px;
                    margin: 0px;
                }
            """)
        else:
            self.license_label.setText("Free")
            self.license_label.setStyleSheet("""
                QLabel {
                    color: #007bff;
                    font-size: 13px;
                    font-weight: bold;
                    padding: 1px 4px;
                    background-color: rgba(128, 128, 128, 0.3);
                    border-radius: 9px;
                    margin: 0px;
                }
            """)
        
        # Calcular el posicionamiento horizontal y vertical
        total_content_width = self.icon_pixmap.width() + 10 + self.text_label.width() + 5 + self.license_label.width()
        
        start_x = (self.window_width - total_content_width) // 2
        vertical_center = self.window_height // 2
        
        # Posicionar el icono
        icon_x = start_x
        icon_y = vertical_center - (self.icon_label.height() // 2)
        self.icon_label.move(icon_x, icon_y)
        self.icon_label.show()
        
        # Posicionar el texto ZETACOPY
        text_x = icon_x + self.icon_label.width() + 10
        text_y = vertical_center - (self.text_label.height() // 2)
        self.text_label.move(text_x, text_y)
        self.text_label.show()
        
        # Posicionar el texto de licencia
        license_x = text_x + self.text_label.width() + 5
        license_y = vertical_center - (self.license_label.height() // 2)
        self.license_label.move(license_x, license_y)
        self.license_label.show()

        # Nuevo QLabel para el mensaje de bienvenida y "Espere un Momento"
        self.welcome_label = QLabel(self)
        username = getpass.getuser() # Obtener el nombre de usuario
        self.welcome_label.setText(f"Hola {username}")
        font_welcome = QFont("Arial", 10)
        font_welcome.setBold(True)
        self.welcome_label.setFont(font_welcome)
        # Texto grisáceo semitransparente
        self.welcome_label.setStyleSheet("color: rgba(180, 180, 180, 200); background: transparent;")

        fm_welcome = QFontMetrics(font_welcome)
        welcome_width_initial = fm_welcome.horizontalAdvance(f"Hola {username}")
        welcome_height = fm_welcome.height()
        # Ajustar el tamaño del QLabel al contenido para un posicionamiento preciso
        self.welcome_label.setFixedSize(welcome_width_initial + 5, welcome_height + 5)
        
        # Posicionar el mensaje de bienvenida centrado debajo de ZETACOPY
        # Calcular el centro horizontal del texto ZETACOPY
        text_center_x = text_x + (self.text_label.width() // 2)
        # Calcular la posición x para el welcome_label para que su centro coincida con el de ZETACOPY
        welcome_x = text_center_x - (self.welcome_label.width() // 2)
        
        # Colocamos el texto cerca de la parte inferior de la ventana.
        welcome_y = self.window_height - (welcome_height + 5) - 5 # Ajuste para que quede más abajo y con margen
        self.welcome_label.move(welcome_x, welcome_y)
        self.welcome_label.show()

        # Efecto de opacidad para la animación de texto
        self.welcome_opacity_effect = QGraphicsOpacityEffect(self.welcome_label)
        self.welcome_label.setGraphicsEffect(self.welcome_opacity_effect)
        self.welcome_opacity_effect.setOpacity(1.0) # Inicialmente visible

    def setup_timers(self):
        """Configura los temporizadores para animación y efectos"""
        # Se eliminan los temporizadores de animación y refresco
        
        # Aplicar efectos iniciales
        QTimer.singleShot(0, self.apply_effects)
        # Temporizador para iniciar la primera transición de texto (Hola -> Espere)
        QTimer.singleShot(2000, self.start_first_text_transition)

    def start_first_text_transition(self):
        """Inicia la animación de desvanecimiento del texto 'Hola [Usuario]'."""
        self.fade_out_animation = QPropertyAnimation(self.welcome_opacity_effect, b"opacity")
        self.fade_out_animation.setDuration(1000) # Duración del desvanecimiento (1 segundo)
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        self.fade_out_animation.finished.connect(self.show_wait_message)
        self.fade_out_animation.start()

    def show_wait_message(self):
        """Cambia el texto a 'Espere un Momento' y comienza la animación de aparición."""
        self.welcome_label.setText("Espere un Momento")
        # Ajustar el tamaño del QLabel al nuevo texto
        fm_welcome = QFontMetrics(self.welcome_label.font())
        welcome_width_wait = fm_welcome.horizontalAdvance("Espere un Momento")
        # Recalcular la posición x para centrar el nuevo texto
        text_center_x = self.text_label.x() + (self.text_label.width() // 2)
        welcome_x_wait = text_center_x - (welcome_width_wait // 2)
        self.welcome_label.setFixedSize(welcome_width_wait + 5, self.welcome_label.height())
        self.welcome_label.move(welcome_x_wait, self.welcome_label.y())
        self.welcome_label.repaint() # Forzar el repintado

        self.fade_in_animation = QPropertyAnimation(self.welcome_opacity_effect, b"opacity")
        self.fade_in_animation.setDuration(1000) # Duración de la aparición (1 segundo)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.finished.connect(lambda: QTimer.singleShot(2000, self.start_second_text_transition))
        self.fade_in_animation.start()

    def start_second_text_transition(self):
        """Inicia la animación de desvanecimiento del texto 'Espere un Momento'."""
        self.fade_out_animation = QPropertyAnimation(self.welcome_opacity_effect, b"opacity")
        self.fade_out_animation.setDuration(1000) # Duración del desvanecimiento (1 segundo)
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        self.fade_out_animation.finished.connect(self.show_info_message)
        self.fade_out_animation.start()

    def show_info_message(self):
        """Cambia el texto a un mensaje informativo y comienza la animación de aparición."""
        info_message = "Cargando componentes..."
        self.welcome_label.setText(info_message)
        # Ajustar el tamaño del QLabel al nuevo texto
        fm_welcome = QFontMetrics(self.welcome_label.font())
        welcome_width_info = fm_welcome.horizontalAdvance(info_message)
        # Recalcular la posición x para centrar el nuevo texto
        text_center_x = self.text_label.x() + (self.text_label.width() // 2)
        welcome_x_info = text_center_x - (welcome_width_info // 2)
        self.welcome_label.setFixedSize(welcome_width_info + 5, self.welcome_label.height())
        self.welcome_label.move(welcome_x_info, self.welcome_label.y())
        self.welcome_label.repaint() # Forzar el repintado

        self.fade_in_animation = QPropertyAnimation(self.welcome_opacity_effect, b"opacity")
        self.fade_in_animation.setDuration(1000) # Duración de la aparición (1 segundo)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.start()

    def apply_effects(self):
        """Aplica los efectos visuales a la ventana de presentación"""
        hwnd = self.winId().__int__()
        # Aplica el efecto acrílico con esquinas redondeadas (rectángulo)
        apply_acrylic_and_rounded(hwnd, mode='dark', is_tooltip=False)

    def paintEvent(self, event):
        # Eliminar todo el dibujo de fondo, sombra y borde
        pass

    def check_license(self):
        """Verifica si existe una licencia válida"""
        try:
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(__file__)
                
            license_path = os.path.join(exe_path, "license.dat")
            
            if not os.path.exists(license_path):
                return False
                
            with open(license_path, "rb") as license_file:
                # Aquí la misma lógica de verificación que en ZETACOPY
                return True
                
        except Exception as e:
            print(f"Error verificando licencia: {e}")
            return False

    def mousePressEvent(self, event):
        """Ignora los clics del ratón para evitar que la pantalla de inicio se cierre prematuramente."""
        pass # No hacer nada cuando se hace clic

    def finish(self, window):
        # Detener el timer de refresco antes de cerrar
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        super().finish(window)

def mostrar_presentacion():
    splash = Presentacion()
    splash.show()
    
    # Forzar el procesamiento de eventos inmediatamente
    QApplication.processEvents()
    
    # Retrasar las operaciones de inicialización
    QTimer.singleShot(500, lambda: _process_background_tasks(splash))
    
    return splash

def _process_background_tasks(splash):
    """Procesa las tareas de fondo mientras mantiene la presentación visible"""
    try:
        # Aquí pueden ir las operaciones de inicialización que necesiten esperar
        QApplication.processEvents()
    except Exception as e:
        print(f"Error en tareas de fondo: {e}")