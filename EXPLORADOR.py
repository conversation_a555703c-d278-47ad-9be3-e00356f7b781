import os
import sys
import shutil
import ctypes
import subprocess
import re
from datetime import datetime
from cryptography.fernet import <PERSON><PERSON>t
from PyQt6.QtWidgets import (
    QLabel, QTreeWidget, QTreeWidgetItem, QAbstractItemView,
    QSizePolicy, QSpacerItem, QVBoxLayout, QHeaderView, QWidget, QHBoxLayout
)
from PyQt6.QtGui import QPixmap, QPainter, QBrush, QPen, QColor
from PyQt6.QtCore import Qt, QFileSystemWatcher, QTimer, QSize, QObject, QThread, pyqtSignal
from PyQt6.QtGui import QFont

# Importaciones de módulos específicos del proyecto
from Barras_Seleccion import BarraSeleccionArchivos
from BARRA_TOTAL import BarraMiniExplorador
from ZETACOPY import get_drive_space, load_config

class FolderSizeCalculator(QThread):
    """Thread para calcular el tamaño de carpetas usando du64.exe"""
    size_calculated = pyqtSignal(str, str)  # folder_path, size_text
    
    def __init__(self, folder_path, du64_path=None, use_du64=False):
        super().__init__()
        self.folder_path = folder_path
        self.du64_path = du64_path
        self.use_du64 = use_du64
        
    def run(self):
        try:
            # Elegir método según configuración
            if self.use_du64 and self.du64_path and os.path.exists(self.du64_path):
                self._run_with_du64()
            else:
                self._run_with_powershell()
        except Exception as e:
            print(f"Error calculando tamaño de {self.folder_path}: {e}")
            self.size_calculated.emit(self.folder_path, "Error")
    
    def _run_with_du64(self):
        """Calcular tamaño usando du64.exe (más rápido)"""
        try:
            cmd = [self.du64_path, '-q', self.folder_path]
            # Configurar para ocultar la consola
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, encoding='utf-8', errors='ignore',
                                  startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            
            if result.returncode == 0:
                output = result.stdout
                # Buscar el patrón "Size: X bytes" en la salida
                size_match = re.search(r'Size:\s+([\d,]+) bytes', output)
                if size_match:
                    size_bytes = int(size_match.group(1).replace(',', ''))
                    size_text = self.format_size(size_bytes)
                    self.size_calculated.emit(self.folder_path, size_text)
                else:
                    # Si no encuentra el patrón, usar PowerShell como fallback
                    self._run_with_powershell()
            else:
                # Si du64 falla, usar PowerShell como fallback
                self._run_with_powershell()
        except subprocess.TimeoutExpired:
            print(f"Timeout con du64 para {self.folder_path}, usando PowerShell")
            self._run_with_powershell()
        except Exception as e:
            print(f"Error con du64 para {self.folder_path}: {e}, usando PowerShell")
            self._run_with_powershell()
    
    def _run_with_powershell(self):
        """Calcular tamaño usando PowerShell (más compatible)"""
        try:
            # Escapar comillas simples duplicándolas para PowerShell
            escaped_path = self.folder_path.replace("'", "''")
            
            powershell_script = f"""
try {{
    # Función para convertir bytes a formato legible (como du)
    function Convert-Size {{
        param ($Bytes)
        if ($Bytes -eq $null -or $Bytes -eq 0) {{ return "0 B" }}
        switch ($Bytes) {{
            {{$_ -ge 1TB}} {{ return "{{0:N1}} TB" -f ($Bytes / 1TB) }}
            {{$_ -ge 1GB}} {{ return "{{0:N1}} GB" -f ($Bytes / 1GB) }}
            {{$_ -ge 1MB}} {{ return "{{0:N1}} MB" -f ($Bytes / 1MB) }}
            {{$_ -ge 1KB}} {{ return "{{0:N1}} KB" -f ($Bytes / 1KB) }}
            default        {{ return "$Bytes B" }}
        }}
    }}
    
    # Verificar que la carpeta existe
    if (-not (Test-Path -LiteralPath '{escaped_path}')) {{
        Write-Output "SIZE:0 B"
        exit
    }}
    
    # Calcular tamaño total recursivo como du (incluyendo todo el árbol de subcarpetas)
    $totalSize = 0
    try {{
        # Método robusto: procesar cada subcarpeta individualmente
        $subfolders = Get-ChildItem -LiteralPath '{escaped_path}' -Directory -ErrorAction SilentlyContinue
        
        if ($subfolders) {{
            foreach ($subfolder in $subfolders) {{
                try {{
                    # Procesar cada subcarpeta por separado para evitar errores globales
                    $subFiles = Get-ChildItem -LiteralPath $subfolder.FullName -Recurse -Force -File -ErrorAction SilentlyContinue
                    if ($subFiles) {{
                        foreach ($file in $subFiles) {{
                            try {{
                                if ($file.Length) {{
                                    $totalSize += $file.Length
                                }}
                            }} catch {{
                                continue
                            }}
                        }}
                    }}
                }} catch {{
                    # Si una subcarpeta falla, continuar con las demás
                    continue
                }}
            }}
        }}
        
        # También incluir archivos directos en la carpeta padre
        try {{
            $directFiles = Get-ChildItem -LiteralPath '{escaped_path}' -Force -File -ErrorAction SilentlyContinue
            if ($directFiles) {{
                foreach ($file in $directFiles) {{
                    try {{
                        if ($file.Length) {{
                            $totalSize += $file.Length
                        }}
                    }} catch {{
                        continue
                    }}
                }}
            }}
        }} catch {{
            # Ignorar si no puede leer archivos directos
        }}
        
        $sizeFormatted = Convert-Size $totalSize
        Write-Output "SIZE:$sizeFormatted"
        
    }} catch {{
        # Método alternativo si el primero falla
        try {{
            $sizeBytes = (Get-ChildItem -LiteralPath '{escaped_path}' -Recurse -Force -ErrorAction SilentlyContinue | 
                         Where-Object {{-not $_.PSIsContainer}} | 
                         Measure-Object -Property Length -Sum).Sum
            
            if ($sizeBytes -eq $null) {{ $sizeBytes = 0 }}
            $sizeFormatted = Convert-Size $sizeBytes
            Write-Output "SIZE:$sizeFormatted"
        }} catch {{
            Write-Output "SIZE:0 B"
        }}
    }}
    
}} catch {{
    Write-Output "SIZE:Error"
}}
"""
            
            # Ejecutar PowerShell con optimizaciones y ocultar consola
            cmd = ['powershell', '-ExecutionPolicy', 'Bypass', '-NoProfile', '-Command', powershell_script]
            # Configurar para ocultar la consola
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=20, encoding='utf-8', errors='ignore',
                                  startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            
            # Debug: imprimir información para carpetas problemáticas
            if "FILMES ESTA SEMANA" in self.folder_path:
                print(f"DEBUG - Carpeta: {self.folder_path}")
                print(f"DEBUG - Return code: {result.returncode}")
                print(f"DEBUG - Stdout: '{result.stdout.strip()}'")
                print(f"DEBUG - Stderr: '{result.stderr.strip()}'")
            
            if result.returncode == 0:
                output = result.stdout.strip()
                if output.startswith("SIZE:"):
                    size_text = output[5:]  # Remover "SIZE:" del inicio
                    self.size_calculated.emit(self.folder_path, size_text)
                else:
                    print(f"DEBUG - Output no válido para {self.folder_path}: '{output}'")
                    self.size_calculated.emit(self.folder_path, "0 B")
            else:
                print(f"DEBUG - Error en PowerShell para {self.folder_path}: {result.stderr}")
                self.size_calculated.emit(self.folder_path, "Error")
        except subprocess.TimeoutExpired:
            print(f"Timeout calculando tamaño de {self.folder_path}")
            self.size_calculated.emit(self.folder_path, "Timeout")
        except Exception as e:
            print(f"Error calculando tamaño de {self.folder_path}: {e}")
            self.size_calculated.emit(self.folder_path, "Error")
    
    def format_size(self, size_bytes):
        """Formatear el tamaño en bytes a una representación legible"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"

class DriveInfoWidget(QWidget):
    """Widget personalizado para mostrar icono y texto del disco"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(8, 8, 8, 8)
        self.layout.setSpacing(8)

        # Para que el fondo de la hoja de estilo se pinte correctamente
        self.setAttribute(Qt.WidgetAttribute.WA_StyledBackground) 

        # Icono del disco
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(20, 20)
        self.layout.addWidget(self.icon_label)

        # Contenedor para los textos (vertical)
        self.text_container_layout = QVBoxLayout()
        self.text_container_layout.setContentsMargins(0, 0, 0, 0)
        self.text_container_layout.setSpacing(0)

        # Primera línea de texto (nombre del disco)
        self.name_label = QLabel()
        self.name_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        self.text_container_layout.addWidget(self.name_label)

        # Segunda línea de texto (información de tamaño/precio/espacio)
        self.info_label = QLabel()
        self.info_label.setStyleSheet("""
            QLabel {
                color: gray;       # Esto hace que el color sea gris
                font-size: 10px;   # Esto hace que el tamaño de la fuente sea de 10 píxeles (más pequeño)
                /* font-weight: bold; # Removido para probar setting directo con QFont */
            }
        """)
        # Establecer la fuente en negrita directamente para info_label
        font_info = self.info_label.font()
        font_info.setBold(True)
        self.info_label.setFont(font_info)
        
        self.text_container_layout.addWidget(self.info_label)

        self.layout.addLayout(self.text_container_layout)
        
        # Eliminamos el stretch que estaba aquí para poder controlarlo mejor en show_drive_contents
        # self.layout.addStretch(1) # Para que los elementos se peguen a la izquierda

        # Configurar el estilo del widget contenedor con fondo y bordes redondeados
        self.setStyleSheet("""
            DriveInfoWidget {
                background-color: rgba(60, 60, 60, 150); /* Gris semitransparente */
                border-radius: 15px; /* Bordes más redondeados */
                border: 1px solid rgba(150, 150, 150, 150); /* Borde gris más ajustado y visible */
                margin-left: 8px;
                margin-right: 8px;
                margin-top: 6px;
                margin-bottom: 6px;
            }
        """)

    def set_drive_info(self, drive_letter, text, explorer_logic):
        """Establece la información del disco con icono y texto"""
        try:
            icon_path = explorer_logic.main_window.get_disk_icon_path(drive_letter)
            if hasattr(icon_path, 'pixmap'):  # Si es un QIcon
                pixmap = icon_path.pixmap(20, 20)
                self.icon_label.setPixmap(pixmap)
            else:  # Si es una ruta de archivo
                pixmap = QPixmap(icon_path)
                if not pixmap.isNull():
                    self.icon_label.setPixmap(pixmap.scaled(20, 20, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
        except Exception as e:
            print(f"Error cargando icono del disco: {e}")
        
        # Dividir el texto en dos líneas
        parts = text.split('\n', 1) # Divide solo en el primer \n
        self.name_label.setText(parts[0])
        if len(parts) > 1:
            self.info_label.setText(parts[1])
        else:
            self.info_label.clear()


    def setText(self, text):
        """Método de compatibilidad con QLabel (ahora con dos labels)"""
        parts = text.split('\n', 1)
        self.name_label.setText(parts[0])
        if len(parts) > 1:
            self.info_label.setText(parts[1])
        else:
            self.info_label.clear()

    def clear(self):
        """Método de compatibilidad con QLabel"""
        self.name_label.clear()
        self.info_label.clear()
        self.icon_label.clear()

class ExplorerLogic(QObject):
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.watched_directories = set()

        # Configurar QFileSystemWatcher para monitoreo en tiempo real
        self.filesystem_watcher = QFileSystemWatcher(self)
        self.filesystem_watcher.directoryChanged.connect(self.on_directory_changed)
        self.filesystem_watcher.fileChanged.connect(self.on_file_changed)

        # Timer para evitar múltiples actualizaciones rápidas
        self.refresh_timer = QTimer(self)
        self.refresh_timer.setSingleShot(True)
        self.refresh_timer.timeout.connect(self.delayed_refresh)
        self.refresh_delay = 500  # 500ms de retraso

        # Inicializar los atributos que se usan en los métodos movidos
        self.main_window.current_explorer_drive = None
        self.main_window.current_drive = None

        # Inicializar las etiquetas de velocidad del explorador
        if not hasattr(self.main_window, 'explorer_speed_labels'):
            self.main_window.explorer_speed_labels = {}
        if not hasattr(self.main_window, 'mini_progress_bars'):
            self.main_window.mini_progress_bars = {}

        # Inicializar last_valid_speed y last_valid_time si no existen en main_window
        if not hasattr(self.main_window, 'last_valid_speed'):
            self.main_window.last_valid_speed = {}
        if not hasattr(self.main_window, 'last_valid_time'):
            self.main_window.last_valid_time = {}
        if not hasattr(self.main_window, 'total_sizes'):
            self.main_window.total_sizes = {}
        if not hasattr(self.main_window, 'queues'):
            self.main_window.queues = {}
        if not hasattr(self.main_window, 'final_space_text'):
            self.main_window.final_space_text = {}
        if not hasattr(self.main_window, 'threads'):
            self.main_window.threads = {}
        
        # Inicializar variables para el cálculo de tamaño de carpetas
        self.folder_size_threads = {}  # Para almacenar los threads de cálculo
        self.folder_size_cache = {}    # Cache de tamaños calculados
        
        # Obtener la ruta de du64.exe (para uso opcional)
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.dirname(os.path.abspath(__file__))
        self.du64_path = os.path.join(base_path, 'du64.exe')
        

    
    def calculate_folder_size(self, folder_path):
        """Iniciar el cálculo del tamaño de una carpeta de forma asíncrona"""
        # Verificar si ya está en cache
        if folder_path in self.folder_size_cache:
            return self.folder_size_cache[folder_path]
        
        # Verificar si ya hay un thread calculando este directorio
        if folder_path in self.folder_size_threads:
            return "Calculando..."
        
        # Obtener configuración para elegir método
        from AJUSTES import load_config
        config = load_config()
        use_du64 = config.get('use_du64', False)
        
        # Crear y iniciar el thread de cálculo
        calculator = FolderSizeCalculator(folder_path, self.du64_path, use_du64)
        calculator.size_calculated.connect(self.on_folder_size_calculated)
        calculator.finished.connect(lambda: self.cleanup_folder_thread(folder_path))
        
        self.folder_size_threads[folder_path] = calculator
        calculator.start()
        
        return "Calculando..."
    
    def on_folder_size_calculated(self, folder_path, size_text):
        """Callback cuando se calcula el tamaño de una carpeta"""
        # Guardar en cache
        self.folder_size_cache[folder_path] = size_text
        
        # Actualizar el item en el tree widget si está visible
        self.update_folder_size_in_tree(folder_path, size_text)
    
    def cleanup_folder_thread(self, folder_path):
        """Limpiar el thread terminado"""
        if folder_path in self.folder_size_threads:
            del self.folder_size_threads[folder_path]
    
    def update_folder_size_in_tree(self, folder_path, size_text):
        """Actualizar el tamaño de una carpeta en el tree widget"""
        try:
            if not hasattr(self.main_window, 'file_tree_widget'):
                return
            
            # Buscar el item correspondiente en el tree widget
            for i in range(self.main_window.file_tree_widget.topLevelItemCount()):
                item = self.main_window.file_tree_widget.topLevelItem(i)
                item_path = item.data(0, Qt.ItemDataRole.UserRole + 1)
                
                if item_path == folder_path:
                    item.setText(1, size_text)
                    break
        except Exception as e:
            print(f"Error actualizando tamaño en tree: {e}")

    def show_drive_contents(self, path, refresh=False):
        try:
            if len(path) == 2 and path[1] == ':':
                path = path + '\\'
            drive_letter = path[:2]
            self.main_window.current_explorer_drive = drive_letter
            self.main_window.current_drive = path
            
            # Crear o configurar el widget de información del disco
            if not hasattr(self.main_window, 'drive_info_label'):
                self.main_window.drive_info_label = DriveInfoWidget(self.main_window)
                self.main_window.main_layout.insertWidget(2, self.main_window.drive_info_label)
            
            # Ocultar etiquetas de velocidad si no hay copia activa
            if not self.main_window.is_copying(drive_letter):
                if drive_letter in self.main_window.explorer_speed_labels:
                    self.main_window.explorer_speed_labels[drive_letter].hide()
                if drive_letter in self.main_window.mini_progress_bars:
                    self.main_window.mini_progress_bars[drive_letter].hide()
                    
            # Obtener información del disco
            volume_name = self.main_window.get_volume_name(drive_letter)
            free_space, total_space = get_drive_space(drive_letter)
            free_space_str = self.main_window.format_size(free_space)
            total_space_str = self.main_window.format_size(total_space)

            # Determinar si el disco está copiando
            is_copying = self.main_window.is_copying(drive_letter)

            # Obtener la cantidad de ficheros en cola
            total_files_in_queue = 0
            if drive_letter in self.main_window.queues:
                total_files_in_queue = self.main_window.queues[drive_letter].qsize()

            if is_copying:
                total_size = self.main_window.total_sizes.get(drive_letter, 0)
                pause_text = ""
                if drive_letter in self.main_window.threads:
                    thread = self.main_window.threads[drive_letter]
                    if getattr(thread, 'paused', False):
                        pause_text = " (Pausado)"

                if total_size > 0:
                    total_size_str = self.main_window.format_size(total_size)
                    
                    # Usar el espacio libre inicial en lugar del actual para calcular el espacio final
                    if hasattr(self.main_window, 'initial_free_space') and drive_letter in self.main_window.initial_free_space:
                        initial_free_space = self.main_window.initial_free_space[drive_letter]
                    else:
                        # Fallback al espacio actual si no hay inicial almacenado
                        initial_free_space = free_space
                        
                    final_space = initial_free_space - total_size
                    final_space_str = self.main_window.format_size(final_space) if final_space is not None else "N/A"

                    # Obtener modo de pago actual
                    config = load_config() # <-- Vuelve a cargar la configuración
                    modo_pago = config.get('modo_pago', 'dispositivo') # <-- Obtiene modo_pago desde la config
                    precio = None

                    if modo_pago == "duracion":
                        duracion_total = 0
                        if drive_letter in self.main_window.queues:
                            for item in self.main_window.queues[drive_letter].queue:
                                source = item.get('source_path')
                                if os.path.exists(source):
                                    duracion = self.main_window.get_video_duration(source)
                                    if duracion is not None:
                                        duracion_total += duracion
                        precio = self.main_window.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
                    elif modo_pago == "ficheros":
                        # Usar el total original para el cálculo del precio
                        if hasattr(self.main_window, 'original_files_count') and drive_letter in self.main_window.original_files_count:
                            total_files_for_price = self.main_window.original_files_count[drive_letter]
                        else:
                            total_files_for_price = total_files_in_queue
                        precio = self.main_window.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files_for_price)
                    else:  # modo dispositivo
                        total_size_gb = total_size / (1024 ** 3)
                        precio = self.main_window.calcular_y_mostrar_precio(drive_letter, total_size_gb)

                    # MODIFICACIÓN: Usar el total original de archivos en lugar del actual en cola
                    # Obtener el total original de archivos que se van a procesar
                    if hasattr(self.main_window, 'original_files_count') and drive_letter in self.main_window.original_files_count:
                        total_files_to_process = self.main_window.original_files_count[drive_letter]
                    else:
                        # Si no existe, calcularlo como total actual (para compatibilidad)
                        total_files_to_process = total_files_in_queue
                        if drive_letter in self.main_window.current_copying_file and self.main_window.current_copying_file[drive_letter] is not None:
                            total_files_to_process += 1
                        # Guardar este total como original si no existe
                        if not hasattr(self.main_window, 'original_files_count'):
                            self.main_window.original_files_count = {}
                        self.main_window.original_files_count[drive_letter] = total_files_to_process

                    # Construir la parte de "ficheros" si aplica
                    files_info_str = ""
                    if total_files_to_process > 0:
                        files_info_str = f" | {total_files_to_process} Ficheros"

                    if precio is not None:
                        if modo_pago == "duracion":
                            drive_info_text = f"{volume_name} ({drive_letter}){pause_text} 𝐓: {total_size_str}{files_info_str}\n(⏱️${precio:.2f}) | 𝐅: {final_space_str}"
                        elif modo_pago == "ficheros":
                            drive_info_text = f"{volume_name} ({drive_letter}){pause_text} 𝐓: {total_size_str}{files_info_str}\n(📜${precio:.2f}) | 𝐅: {final_space_str}"
                        else:  # modo dispositivo
                            drive_info_text = f"{volume_name} ({drive_letter}){pause_text} 𝐓: {total_size_str}{files_info_str}\n(💰${precio:.2f}) | 𝐅: {final_space_str}"
                    else:
                        # Si no hay precio, la segunda línea solo muestra el espacio final
                        drive_info_text = f"{volume_name} ({drive_letter}){pause_text} 𝐓: {total_size_str}{files_info_str}\n𝐅: {final_space_str}"
                elif total_size == 0: # If total_size is 0, but is_copying is true (shouldn't happen often)
                    # En este caso, solo mostrar el espacio libre, pero asegurando el formato
                    # También aquí usar el total original si existe
                    if hasattr(self.main_window, 'original_files_count') and drive_letter in self.main_window.original_files_count:
                        total_files_to_process = self.main_window.original_files_count[drive_letter]
                    else:
                        total_files_to_process = total_files_in_queue
                        if drive_letter in self.main_window.current_copying_file and self.main_window.current_copying_file[drive_letter] is not None:
                            total_files_to_process += 1
                    files_info_str = ""
                    if total_files_to_process > 0:
                        files_info_str = f" | {total_files_to_process} Ficheros"
                    drive_info_text = f"{volume_name} ({drive_letter}){files_info_str}\n{free_space_str}"
            else: # Not copying - mostrar nombre, letra y espacio libre en la misma línea
                drive_info_text = f"{volume_name} ({drive_letter}) {free_space_str}"

            if not refresh:
                try:
                    # Verificar solo si existe el archivo de licencia (rápido)
                    if getattr(sys, 'frozen', False):
                        exe_path = os.path.dirname(sys.executable)
                    else:
                        exe_path = os.path.dirname(os.path.abspath(__file__))
                    license_path = os.path.join(exe_path, "license.dat")
                    if not os.path.exists(license_path):
                        print("No se encontró archivo de licencia")
                        self.main_window.license_active = False
                        self.main_window.disable_main_window()
                        from ACTIVATE import show_license_activation_dialog
                        show_license_activation_dialog(self.main_window, self.main_window)
                        return

                    # Si el archivo existe, verificar solo la fecha (rápido)
                    with open(license_path, "rb") as license_file:
                        combined_data = license_file.read()
                        key, encrypted = combined_data.split(b'|', 1)
                        fernet = Fernet(key)
                        decrypted = fernet.decrypt(encrypted).decode()

                        # Extraer solo la fecha
                        date_str = decrypted.split('||')[-1]
                        expiration_date = datetime.fromisoformat(date_str)
                        if datetime.now() < expiration_date:
                            self.main_window.license_active = True
                            self.main_window.enable_main_window()
                        else:
                            print("La licencia ha expirado.")
                            self.main_window.license_active = False
                            self.main_window.disable_main_window()
                            from ACTIVATE import show_license_activation_dialog
                            show_license_activation_dialog(self.main_window, self.main_window)
                            return
                except Exception as e:
                    print(f"Error en la verificación de licencia: {e}")
                    return

            # Aplicar el texto y el icono a la etiqueta de información del disco
            # El set_drive_info se encarga de dividir en name_label e info_label
            self.main_window.drive_info_label.set_drive_info(drive_letter, drive_info_text, self)
            self.main_window.drive_info_label.show()

            # if not hasattr(self.main_window, 'file_tree_widget_container'):
            #     self.main_window.file_tree_widget_container = RoundedContainerWidget(self.main_window)
            #     self.main_window.main_layout.insertWidget(3, self.main_window.file_tree_widget_container)


            if not hasattr(self.main_window, 'file_tree_widget'):
                self.main_window.file_tree_widget = QTreeWidget(self.main_window)
                self.main_window.file_tree_widget.setColumnCount(2)
                self.main_window.file_tree_widget.setHeaderLabels(["Nombre", "Tamaño"])
                self.main_window.file_tree_widget.header().hide()
                self.main_window.file_tree_widget.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
                self.main_window.file_tree_widget.itemDoubleClicked.connect(self.main_window.on_item_double_clicked)
                self.main_window.file_tree_widget.setAcceptDrops(True)
                self.main_window.file_tree_widget.setDragEnabled(True)
                self.main_window.file_tree_widget.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
                self.main_window.file_tree_widget.keyPressEvent = self.main_window.file_list_key_press_event
                # Eliminar fondo transparente si no se usa RoundedContainerWidget
                self.main_window.file_tree_widget.setStyleSheet("border: none; padding-top: 5px; padding-bottom: 5px;") # Fondo transparente para el tree widget
                self.main_window.main_layout.insertWidget(3, self.main_window.file_tree_widget) # Añadir directamente al layout principal

                # Conectar a los métodos de ExplorerLogic para el arrastre y soltar
                self.main_window.file_tree_widget.dragEnterEvent = self.tree_drag_enter_event
                self.main_window.file_tree_widget.dragMoveEvent = self.tree_drag_move_event
                self.main_window.file_tree_widget.dropEvent = self.tree_drop_event

                self.main_window.file_tree_widget.mousePressEvent = self.main_window.file_list_mouse_press_event
                self.main_window.file_tree_widget.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
                self.main_window.file_tree_widget.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)

                # Aplicar la barra de selección personalizada
                BarraSeleccionArchivos.configurar_tree_widget(self.main_window.file_tree_widget)

            # Actualizar el directorio vigilado
            if path not in self.watched_directories:
                # Remover directorios anteriores si existen
                if self.watched_directories:
                    for watched_dir in list(self.watched_directories):
                        if self.filesystem_watcher.directories():
                            self.filesystem_watcher.removePath(watched_dir)
                        self.watched_directories.remove(watched_dir)

                # Agregar el nuevo directorio
                try:
                    self.filesystem_watcher.addPath(path)
                    self.watched_directories.add(path)
                    print(f"Monitoreando directorio: {path}")
                except Exception as e:
                    print(f"Error al agregar directorio al watcher: {e}")
            if not refresh:
                self.main_window.list_widget.hide()
                self.main_window.nav_widget.show()
                self.main_window.back_button.show()
                self.main_window.new_folder.show()
                self.main_window.new_files.show()
                self.main_window.new_empaketado.show()
                self.main_window.cola_button.show()
                self.main_window.botones.CLEAR.show()
                self.main_window.botones.RENAME.show()
                self.main_window.back_new_folder_layout.setSpacing(1)            # conserva tu separación
                self.main_window.back_new_folder_layout.setContentsMargins(10, 2, 10, 2)  # padding interno del rectángulo
                
                # Configurar el header del tree solo con las columnas
                header_item = self.main_window.file_tree_widget.headerItem()
                header_item.setText(0, "Nombre")
                header_item.setText(1, "Tamaño")
                font = header_item.font(0)
                font.setBold(True)
                header_item.setFont(0, font)
                header_item.setFont(1, font)

                header = self.main_window.file_tree_widget.header()
                header.setDefaultAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
                header.setFixedHeight(25)

                # Gestión de barras de progreso y velocidad
                for mini_bar in self.main_window.mini_progress_bars.values():
                    mini_bar.hide()

                if drive_letter not in self.main_window.mini_progress_bars:
                    print(f"Creando nueva mini barra para {drive_letter}")
                    mini_bar = BarraMiniExplorador(self.main_window)
                    mini_bar.setFixedSize(80, 15)  # Reducimos un poco el ancho
                    mini_bar.set_color(self.main_window.progress_bar_color)
                    self.main_window.mini_progress_bars[drive_letter] = mini_bar
                else:
                    print(f"Usando mini barra existente para {drive_letter}")
                    mini_bar = self.main_window.mini_progress_bars[drive_letter]
                    mini_bar.setFixedSize(80, 15)  # Aseguramos que tenga el tamaño correcto
                
                if not hasattr(self.main_window, 'explorer_speed_labels'):
                    self.main_window.explorer_speed_labels = {}
                if drive_letter not in self.main_window.explorer_speed_labels:
                    speed_label = QLabel(self.main_window.drive_info_label)
                    speed_label.setStyleSheet("color: white; padding: 2px 5px; font-weight: bold; font-size: 10px;")
                    speed_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                    self.main_window.explorer_speed_labels[drive_letter] = speed_label
                else:
                    speed_label = self.main_window.explorer_speed_labels[drive_letter]
                    speed_label.setStyleSheet("color: white; padding: 2px 5px; font-weight: bold; font-size: 10px;")
                    speed_label.setParent(self.main_window.drive_info_label)

                # Añadir la etiqueta de velocidad al layout de DriveInfoWidget PRIMERO
                if hasattr(self.main_window.drive_info_label, 'layout'):
                    # Primero eliminar la etiqueta si ya estaba en el layout
                    for i in range(self.main_window.drive_info_label.layout.count()):
                        item = self.main_window.drive_info_label.layout.itemAt(i)
                        if isinstance(item, QWidget) and item.widget() == speed_label:
                            self.main_window.drive_info_label.layout.removeWidget(speed_label)

                    # Añadir la etiqueta de velocidad primero
                    self.main_window.drive_info_label.layout.addWidget(speed_label)

                # Añadir la barra mini al layout de DriveInfoWidget DESPUÉS de la etiqueta de velocidad
                if hasattr(self.main_window.drive_info_label, 'layout'):
                    # Primero eliminar la barra mini si ya estaba en el layout
                    for i in range(self.main_window.drive_info_label.layout.count()):
                        item = self.main_window.drive_info_label.layout.itemAt(i)
                        if isinstance(item, QWidget) and item.widget() == mini_bar:
                            self.main_window.drive_info_label.layout.removeWidget(mini_bar)

                    # Añadir la barra mini después de la etiqueta de velocidad
                    self.main_window.drive_info_label.layout.addWidget(mini_bar)
                    mini_bar.setParent(self.main_window.drive_info_label)
                    # Añadir un espaciador después de la mini barra para separarla del borde derecho
                    self.main_window.drive_info_label.layout.addSpacerItem(QSpacerItem(10, 1, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum))

                # Mostrar la etiqueta de velocidad solo si este disco está copiando
                if self.main_window.is_copying(drive_letter):
                    current_progress = self.main_window.get_current_progress(drive_letter)
                    if current_progress < 100:
                        mini_bar.show()
                        if hasattr(self.main_window, 'last_valid_speed') and drive_letter in self.main_window.last_valid_speed:
                            speed_value = self.main_window.last_valid_speed[drive_letter]
                            time_remaining = self.main_window.last_valid_time.get(drive_letter, "00:00:00")
                            if speed_value >= 1000:
                                speed_str = f"{speed_value/1000:.1f} GB/s"
                            else:
                                speed_str = f"{speed_value:.1f} MB/s"
                            speed_label.setText(f"{speed_str} - {time_remaining}")
                            speed_label.show()
                        else:
                            speed_label.setText("Calculando...")
                            speed_label.show()
                        if not hasattr(self.main_window, 'speed_update_timer') or not self.main_window.speed_update_timer.isActive():
                            self.main_window.speed_update_timer = QTimer(self.main_window)
                            self.main_window.speed_update_timer.timeout.connect(self.main_window.update_speed_display)
                            self.main_window.speed_update_timer.start(100)
                    else:
                        mini_bar.hide()
                        speed_label.hide()
                else:
                    mini_bar.hide()
                    speed_label.hide()

                # Eliminar las etiquetas y barras del layout de botones
                for i in range(self.main_window.back_new_folder_layout.count()):
                    item = self.main_window.back_new_folder_layout.itemAt(i)
                    if isinstance(item, QWidget) and (item.widget() == speed_label or item.widget() == mini_bar):
                        self.main_window.back_new_folder_layout.removeWidget(item.widget())
            self.main_window.current_drive = path
            current_scroll_position = self.main_window.file_tree_widget.verticalScrollBar().value()
            selected_items = [item.text(0) for item in self.main_window.file_tree_widget.selectedItems()]
            try:
                self.main_window.file_tree_widget.itemDoubleClicked.disconnect(self.main_window.on_file_item_double_clicked)
            except TypeError:
                pass
            self.main_window.file_tree_widget.setSortingEnabled(False)
            self.load_directory_contents(path, selected_items, current_scroll_position)

            # Configurar el tree widget
            self.main_window.file_tree_widget.setIconSize(QSize(25, 25))
            self.main_window.file_tree_widget.header().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
            # Usar ancho fijo para la columna de tamaño para evitar que la ventana crezca
            self.main_window.file_tree_widget.header().setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
            self.main_window.file_tree_widget.header().resizeSection(1, 100)  # Ancho fijo de 100px para la columna de tamaño
            self.main_window.file_tree_widget.header().setStretchLastSection(False)
            self.main_window.file_tree_widget.header().setDefaultAlignment(Qt.AlignmentFlag.AlignLeft)
            self.main_window.file_tree_widget.headerItem().setTextAlignment(0, Qt.AlignmentFlag.AlignLeft)
            self.main_window.file_tree_widget.setRootIsDecorated(False)
            self.main_window.file_tree_widget.itemDoubleClicked.connect(self.main_window.on_file_item_double_clicked)
            self.main_window.file_tree_widget.keyPressEvent = self.main_window.file_list_key_press_event
            if not refresh:
                self.main_window.setup_file_list_connections()
                self.main_window.file_tree_widget.show()
        except Exception as e:
            print(f"Error general en show_drive_contents: {e}")

    def load_directory_contents(self, path, selected_items=None, scroll_position=0):
        """Cargar contenido del directorio de forma optimizada"""
        try:
            system_folders = {
                'System Volume Information',
                '$RECYCLE.BIN',
                'Config.Msi',
                'Recovery',
                '$WinREAgent'
            }
            entries = []
            shown_entries = set()
            with os.scandir(path) as scanner:
                for entry in scanner:
                    try:
                        if (entry.name in system_folders or
                            entry.name.startswith('$') or
                            entry.name.startswith('.') or
                            bool(os.stat(entry.path).st_file_attributes & 0x2)):
                            continue
                        if not os.access(entry.path, os.R_OK):
                            continue
                        if entry.path not in shown_entries:
                            entries.append(entry)
                            shown_entries.add(entry.path)
                    except (PermissionError, OSError):
                        continue

            # Separar carpetas y archivos
            folders = [entry for entry in entries if entry.is_dir()]
            files = [entry for entry in entries if not entry.is_dir()]
            folders.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            entries_sorted = folders + files
            self.main_window.file_tree_widget.clear()
            for entry in entries_sorted:
                if not os.access(entry.path, os.R_OK):
                    continue
                item = QTreeWidgetItem([entry.name])
                is_directory = entry.is_dir()
                if not is_directory:
                    try:
                        size = os.path.getsize(entry.path)
                        item.setText(1, self.main_window.format_size(size))
                    except OSError:
                        item.setText(1, "Error")
                else:
                    # Para carpetas, iniciar el cálculo asíncrono del tamaño
                    size_text = self.calculate_folder_size(entry.path)
                    item.setText(1, size_text)

                item.setTextAlignment(1, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                item.setData(0, Qt.ItemDataRole.UserRole, is_directory)
                item.setData(0, Qt.ItemDataRole.UserRole + 1, entry.path)

                icon = self.main_window.icon_manager.get_icon_for_file(entry.path)
                item.setIcon(0, icon)

                font = item.font(0)
                font.setBold(True)
                item.setFont(0, font)
                item.setFont(1, font)

                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                self.main_window.file_tree_widget.addTopLevelItem(item)

            # Restaurar selección y posición de scroll
            if selected_items:
                self.restore_selection(selected_items)
            if scroll_position > 0:
                self.main_window.file_tree_widget.verticalScrollBar().setValue(scroll_position)
            self.main_window.file_tree_widget.update()
        except Exception as e:
            print(f"Error cargando contenido del directorio: {e}")

    def restore_selection(self, selected_items):
        """Restaurar la selección de elementos"""
        try:
            self.main_window.file_tree_widget.clearSelection()
            for i in range(self.main_window.file_tree_widget.topLevelItemCount()):
                item = self.main_window.file_tree_widget.topLevelItem(i)
                if item.text(0) in selected_items:
                    item.setSelected(True)
        except Exception as e:
            print(f"Error restaurando selección: {e}")

    def on_directory_changed(self, path):
        """Callback cuando el directorio cambia"""
        try:
            # Verificar si el directorio que cambió es el actual
            if hasattr(self.main_window, 'current_drive') and path == self.main_window.current_drive:
                print(f"Directorio cambiado: {path}")
                # Usar timer para evitar múltiples actualizaciones rápidas
                self.refresh_timer.start(self.refresh_delay)
        except Exception as e:
            print(f"Error en on_directory_changed: {e}")

    def on_file_changed(self, path):
        """Callback cuando un archivo cambia"""
        try:
            # Si el archivo está en el directorio actual, actualizar
            if hasattr(self.main_window, 'current_drive') and os.path.dirname(path) == self.main_window.current_drive:
                print(f"Archivo cambiado: {path}")
                self.refresh_timer.start(self.refresh_delay)
        except Exception as e:
            print(f"Error en on_file_changed: {e}")

    def delayed_refresh(self):
        """Actualización retrasada para evitar múltiples refreshes"""
        try:
            if hasattr(self.main_window, 'current_drive') and self.main_window.current_drive:
                print(f"Iniciando actualización de vista del directorio: {self.main_window.current_drive}")
                # Guardar estado actual
                current_scroll_position = self.main_window.file_tree_widget.verticalScrollBar().value()
                selected_items = [item.text(0) for item in self.main_window.file_tree_widget.selectedItems()]
                
                # Usar un timer de un solo disparo para desacoplar la actualización
                # y evitar bloqueos en el hilo principal
                def safe_update():
                    try:
                        print(f"Ejecutando actualización segura para: {self.main_window.current_drive}")
                        self.load_directory_contents(self.main_window.current_drive, selected_items, current_scroll_position)
                        print(f"Actualización completada para: {self.main_window.current_drive}")
                    except Exception as e:
                        print(f"Error en actualización segura: {e}")
                        import traceback
                        traceback.print_exc()
                
                # Usar QTimer para ejecutar la actualización en el hilo de la UI
                # pero después de que se procesen otros eventos pendientes
                QTimer.singleShot(100, safe_update)
                print(f"Actualización programada para: {self.main_window.current_drive}")
        except Exception as e:
            print(f"Error en delayed_refresh: {e}")
            import traceback
            traceback.print_exc()

    def cleanup_filesystem_watcher(self):
        """Limpiar el watcher cuando se cierre la aplicación"""
        try:
            if hasattr(self, 'filesystem_watcher'):
                if self.filesystem_watcher.directories():
                    for directory in self.filesystem_watcher.directories():
                        self.filesystem_watcher.removePath(directory)
                if self.filesystem_watcher.files():
                    for file in self.filesystem_watcher.files():
                        self.filesystem_watcher.removePath(file)
                self.filesystem_watcher.deleteLater()
                print("FileSystemWatcher limpiado correctamente")
        except Exception as e:
            print(f"Error limpiando FileSystemWatcher: {e}")

    def tree_drag_enter_event(self, event):
        if event.source() == self.main_window.file_tree_widget:
            event.accept()
        else:
            self.main_window.list_widget.dragEnterEvent(event)

    def tree_drag_move_event(self, event):
        if event.source() == self.main_window.file_tree_widget:
            target_item = self.main_window.file_tree_widget.itemAt(event.position().toPoint())
            if target_item and target_item.data(0, Qt.ItemDataRole.UserRole):  # Verificar si es directorio
                event.accept()
            else:
                event.ignore()
        else:
            self.main_window.list_widget.dragMoveEvent(event)

    def tree_drop_event(self, event):
        if event.source() == self.main_window.file_tree_widget:
            target_item = self.main_window.file_tree_widget.itemAt(event.position().toPoint())
            if target_item and target_item.data(0, Qt.ItemDataRole.UserRole):  # Es un directorio
                try:
                    selected_items = self.main_window.file_tree_widget.selectedItems()
                    target_path = os.path.join(self.main_window.current_drive, target_item.text(0))
                    for item in selected_items:
                        source_path = os.path.join(self.main_window.current_drive, item.text(0))
                        dest_path = os.path.join(target_path, item.text(0))
                        if os.path.isdir(source_path) and target_path.startswith(source_path):
                            continue
                        if os.path.exists(dest_path):
                            counter = 1
                            base_name, ext = os.path.splitext(item.text(0))
                            while os.path.exists(dest_path):
                                new_name = f"{base_name} ({counter}){ext}"
                                dest_path = os.path.join(target_path, new_name)
                                counter += 1
                        shutil.move(source_path, dest_path)
                    self.main_window.file_tree_widget.clear()
                    self.show_drive_contents(self.main_window.current_drive, refresh=True)
                    event.accept()
                    return
                except Exception as e:
                    print(f"Error moviendo archivos: {e}")
                    event.ignore()
                    return
        self.main_window.list_widget.dropEvent(event)




