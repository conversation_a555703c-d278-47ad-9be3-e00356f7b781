import subprocess
import tempfile
import os
from bs4 import BeautifulSoup

def debug_usbdeview_html():
    """Debuggear el HTML generado por USBDeview"""
    try:
        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            temp_html = f.name
        
        # Ejecutar USBDeview
        cmd = ["USBDeview.exe", "/shtml", temp_html, "/sort", "1", "/nosort"]
        
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW,
            timeout=30
        )
        
        if result.returncode != 0:
            print(f"Error ejecutando USBDeview: {result.stderr}")
            return
        
        # Leer y mostrar contenido
        encodings = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
        content = None
        
        for encoding in encodings:
            try:
                with open(temp_html, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"Archivo leído con codificación: {encoding}")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("No se pudo leer el archivo")
            return
        
        print(f"Tamaño del archivo: {len(content)} caracteres")
        
        # Parsear con BeautifulSoup
        soup = BeautifulSoup(content, 'html.parser')
        
        # Buscar todas las tablas
        tables = soup.find_all('table')
        print(f"\n=== Encontradas {len(tables)} tablas ===")
        
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            print(f"Tabla {i+1}: {len(rows)} filas")
            
            if rows:
                # Mostrar header
                header_cells = rows[0].find_all(['th', 'td'])
                print(f"  Header ({len(header_cells)} columnas):")
                for j, cell in enumerate(header_cells[:10]):  # Solo primeras 10
                    print(f"    {j}: {cell.get_text(strip=True)}")
                
                # Mostrar primera fila de datos
                if len(rows) > 1:
                    data_cells = rows[1].find_all('td')
                    print(f"  Primera fila de datos ({len(data_cells)} columnas):")
                    for j, cell in enumerate(data_cells[:10]):  # Solo primeras 10
                        print(f"    {j}: {cell.get_text(strip=True)}")
        
        # Limpiar archivo temporal
        try:
            os.unlink(temp_html)
        except:
            pass
            
    except Exception as e:
        print(f"Error en debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_usbdeview_html()