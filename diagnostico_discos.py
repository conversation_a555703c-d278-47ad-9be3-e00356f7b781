#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de diagnóstico detallado para analizar por qué algunos discos no obtienen puerto y hub
"""

import subprocess
import json
import os

def create_hidden_startupinfo():
    """Crea un objeto STARTUPINFO para ocultar la ventana de consola."""
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE
    return startupinfo

def diagnosticar_disco(drive_letter):
    """Diagnóstico completo de un disco específico"""
    print(f"\n{'='*60}")
    print(f"🔍 DIAGNÓSTICO COMPLETO PARA {drive_letter}")
    print(f"{'='*60}")
    
    drive = drive_letter.replace(":", "")
    
    # 1. Información básica del disco
    print(f"\n1️⃣ INFORMACIÓN BÁSICA:")
    try:
        command = f'''
        $partition = Get-Partition -DriveLetter {drive} -EA 0
        if ($partition) {{
            $disk = $partition | Get-Disk -EA 0
            if ($disk) {{
                $info = @{{
                    "Model" = $disk.Model
                    "SerialNumber" = $disk.SerialNumber
                    "BusType" = $disk.BusType
                    "Size" = $disk.Size
                    "HealthStatus" = $disk.HealthStatus
                    "OperationalStatus" = $disk.OperationalStatus
                }}
                $info | ConvertTo-Json
            }}
        }}
        '''
        
        startupinfo = create_hidden_startupinfo()
        process = subprocess.run(
            ["powershell", "-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", command],
            capture_output=True,
            text=True,
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW,
            timeout=10
        )
        
        if process.returncode == 0 and process.stdout.strip():
            disk_info = json.loads(process.stdout.strip())
            for key, value in disk_info.items():
                print(f"   {key}: {value}")
        else:
            print(f"   ❌ No se pudo obtener información básica")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # 2. Búsqueda de dispositivos PnP relacionados
    print(f"\n2️⃣ DISPOSITIVOS PnP RELACIONADOS:")
    try:
        command = f'''
        $partition = Get-Partition -DriveLetter {drive} -EA 0
        if ($partition) {{
            $disk = $partition | Get-Disk -EA 0
            if ($disk) {{
                # Buscar dispositivos PnP que contengan el modelo del disco
                $model = $disk.Model
                if ($model) {{
                    $devices = Get-PnpDevice | Where-Object {{ 
                        $_.FriendlyName -like "*$model*" -or 
                        $_.InstanceId -like "*$model*" 
                    }}
                    
                    foreach ($device in $devices) {{
                        $info = @{{
                            "FriendlyName" = $device.FriendlyName
                            "InstanceId" = $device.InstanceId
                            "Class" = $device.Class
                            "Status" = $device.Status
                        }}
                        $info | ConvertTo-Json
                        Write-Output "---"
                    }}
                }}
            }}
        }}
        '''
        
        process = subprocess.run(
            ["powershell", "-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", command],
            capture_output=True,
            text=True,
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW,
            timeout=15
        )
        
        if process.returncode == 0 and process.stdout.strip():
            output_lines = process.stdout.strip().split("---")
            for i, line in enumerate(output_lines):
                if line.strip() and line.strip() != "---":
                    try:
                        device_info = json.loads(line.strip())
                        print(f"   Dispositivo {i+1}:")
                        for key, value in device_info.items():
                            print(f"     {key}: {value}")
                        print()
                    except:
                        pass
        else:
            print(f"   ❌ No se encontraron dispositivos PnP relacionados")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # 3. Búsqueda específica de USBSTOR
    print(f"\n3️⃣ BÚSQUEDA USBSTOR:")
    try:
        command = f'''
        $partition = Get-Partition -DriveLetter {drive} -EA 0
        if ($partition) {{
            $disk = $partition | Get-Disk -EA 0
            if ($disk -and $disk.BusType -eq "USB") {{
                # Buscar dispositivo USBSTOR por modelo
                $pnpDevice = Get-PnpDevice | Where-Object {{ 
                    $_.InstanceId -like "*USBSTOR*" -and 
                    $_.FriendlyName -like "*$($disk.Model)*" 
                }} | Select-Object -First 1
                
                if ($pnpDevice) {{
                    Write-Output "USBSTOR encontrado: $($pnpDevice.FriendlyName)"
                    Write-Output "InstanceId: $($pnpDevice.InstanceId)"
                    
                    # Obtener información de ubicación
                    $locationInfo = ($pnpDevice | Get-PnpDeviceProperty -KeyName 'DEVPKEY_Device_LocationInfo' -EA 0).Data
                    if ($locationInfo) {{
                        Write-Output "LocationInfo: $locationInfo"
                    }} else {{
                        Write-Output "No LocationInfo disponible"
                    }}
                }} else {{
                    Write-Output "No se encontró dispositivo USBSTOR para este disco"
                }}
            }} else {{
                Write-Output "El disco no es de tipo USB (BusType: $($disk.BusType))"
            }}
        }}
        '''
        
        process = subprocess.run(
            ["powershell", "-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", command],
            capture_output=True,
            text=True,
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW,
            timeout=15
        )
        
        if process.returncode == 0 and process.stdout.strip():
            for line in process.stdout.strip().split('\n'):
                if line.strip():
                    print(f"   {line.strip()}")
        else:
            print(f"   ❌ No se pudo ejecutar búsqueda USBSTOR")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    # Diagnosticar los discos problemáticos
    discos_problematicos = ["J:", "H:"]
    
    for disco in discos_problematicos:
        if os.path.exists(f"{disco}\\"):
            diagnosticar_disco(disco)
        else:
            print(f"\n❌ {disco} no existe o no está accesible")
    
    print(f"\n{'='*60}")
    print(f"🏁 DIAGNÓSTICO COMPLETADO")
    print(f"{'='*60}")