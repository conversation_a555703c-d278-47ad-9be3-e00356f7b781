#!/usr/bin/env python3
"""
Script de prueba para el nuevo sistema de mapeo de puertos USB
que aprovecha USBDeview para extraer información de puertos y hubs
"""

import sys
import os

# Agregar el directorio actual al path para importar MAPEO_PUERTO
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from MAPEO_PUERTO import get_port_info_with_usbdeview, get_port_info_for_drive

def test_usbdeview_mapping():
    """Probar el mapeo de puertos usando USBDeview"""
    print("=== PRUEBA DE MAPEO DE PUERTOS USB CON USBDEVIEW ===")
    print()
    
    # Obtener letras de unidad comunes para probar
    test_drives = ['H', 'E', 'N', 'K']  # Basado en el CSV que vimos antes
    
    for drive in test_drives:
        print(f"Probando unidad {drive}:")
        try:
            # Probar con la nueva función de USBDeview
            port_info, hub_info = get_port_info_with_usbdeview(drive)
            print(f"  USBDeview -> Puerto: {port_info}, Hub: {hub_info}")
            
            # Probar con la función principal (que ahora usa USBDeview primero)
            port_info2, hub_info2 = get_port_info_for_drive(drive)
            print(f"  Principal -> Puerto: {port_info2}, Hub: {hub_info2}")
            
        except Exception as e:
            print(f"  Error: {e}")
        
        print()

def test_csv_parsing():
    """Probar el parseo del CSV de USBDeview"""
    print("=== PRUEBA DE PARSEO DE CSV ===")
    print()
    
    # Verificar si existe el archivo CSV de USBDeview
    csv_files = ['usb_info.csv', 'usb_info_test.csv', 'usb_info_headers.csv']
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"Analizando archivo: {csv_file}")
            try:
                with open(csv_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    print(f"  Total de líneas: {len(lines)}")
                    
                    # Mostrar las primeras líneas para análisis
                    for i, line in enumerate(lines[:5]):
                        if line.strip():
                            parts = line.split(',')
                            print(f"  Línea {i+1}: {len(parts)} columnas")
                            if len(parts) > 7:
                                print(f"    Dispositivo: {parts[0][:50]}...")
                                print(f"    Letras unidad: {parts[7]}")
                            print()
                            
            except Exception as e:
                print(f"  Error leyendo {csv_file}: {e}")
            print()

if __name__ == "__main__":
    print("Iniciando pruebas del sistema de mapeo de puertos USB...")
    print()
    
    # Ejecutar pruebas
    test_csv_parsing()
    test_usbdeview_mapping()
    
    print("Pruebas completadas.")
