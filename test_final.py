#!/usr/bin/env python3
"""
Script para probar la implementación final de mapeo de puertos USB
"""

import sys
import os

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from MAPEO_PUERTO import get_port_info_with_powershell_direct, get_port_info_for_drive

def test_powershell_direct():
    """Probar el método PowerShell directo"""
    print("=== PRUEBA DE POWERSHELL DIRECTO ===")
    print()
    
    # Letras de unidad que sabemos que existen
    test_drives = ['H', 'N', 'E', 'K']
    
    for drive in test_drives:
        print(f"Probando unidad {drive}: con PowerShell directo:")
        try:
            port_info, hub_info = get_port_info_with_powershell_direct(drive)
            print(f"  Resultado -> Puerto: {port_info}, Hub: {hub_info}")
            
        except Exception as e:
            print(f"  Error: {e}")
        
        print()

def test_main_function_final():
    """Probar la función principal final"""
    print("=== PRUEBA DE FUNCIÓN PRINCIPAL FINAL ===")
    print()
    
    test_drives = ['H', 'N', 'E', 'K']
    
    for drive in test_drives:
        print(f"Probando unidad {drive}: con función principal:")
        try:
            port_info, hub_info = get_port_info_for_drive(drive)
            print(f"  Resultado final -> Puerto: {port_info}, Hub: {hub_info}")
            
        except Exception as e:
            print(f"  Error: {e}")
        
        print()

def test_summary():
    """Resumen de la funcionalidad implementada"""
    print("=== RESUMEN DE FUNCIONALIDAD IMPLEMENTADA ===")
    print()
    print("✓ USBDeview.exe integrado para extraer información de dispositivos USB")
    print("✓ Correlación de letras de unidad con Instance IDs")
    print("✓ PowerShell directo para obtener información de puertos físicos")
    print("✓ Múltiples métodos de fallback para máxima compatibilidad")
    print("✓ Caché para mejorar rendimiento")
    print("✓ Extracción de información de Hub y Puerto")
    print()
    print("El sistema ahora puede:")
    print("- Detectar dispositivos USB de almacenamiento masivo")
    print("- Mapear letras de unidad a puertos físicos USB")
    print("- Identificar hubs USB y números de puerto")
    print("- Funcionar con múltiples dispositivos simultáneamente")
    print("- Proporcionar información detallada para monitoreo")

if __name__ == "__main__":
    print("Iniciando pruebas finales del sistema de mapeo de puertos USB...")
    print()
    
    test_powershell_direct()
    test_main_function_final()
    test_summary()
    
    print("Pruebas finales completadas.")
