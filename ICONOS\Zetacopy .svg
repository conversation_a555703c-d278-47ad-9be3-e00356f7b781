<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 24 24">
  <defs>
    <!-- Gradiente para efecto 3D del rombo -->
    <radialGradient id="bevel3d" cx="30%" cy="30%" r="70%">
      <stop offset="0%" stop-color="#ff7a59" />
      <stop offset="30%" stop-color="#e85d42" />
      <stop offset="70%" stop-color="#d95336" />
      <stop offset="100%" stop-color="#b8432a" />
    </radialGradient>

    <!-- Sombra más sutil para la Z, ajustada para no invadir el fondo -->
    <filter id="innerGlowZ" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="0.8" result="blur"/> <!-- Reducido de 1.2 a 0.8 -->
      <feOffset dx="0" dy="0.5" result="offset"/> <!-- Reducido de 0.8 a 0.5 -->
      <feMerge>
        <feMergeNode in="offset"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Rombo principal con gradiente brillante -->
  <path fill="url(#bevel3d)"
        d="m13.666 1.429l6.75 3.98l.096.063l.093.078l.106.074a3.22 3.22 0 0 1 1.284 2.39l.005.204v7.284c0 1.175-.643 2.256-1.623 2.793l-6.804 4.302c-.98.538-2.166.538-3.2-.032l-6.695-4.237A3.23 3.23 0 0 1 2 15.502V8.217c0-1.106.57-2.128 1.476-2.705l6.95-4.098c1-.552 2.214-.552 3.24.015"/>

  <!-- Z flotante con sombra ligera -->
  <path fill="white" filter="url(#innerGlowZ)"
        d="M14 7h-4a1 1 0 0 0-1 1l.007.117A1 1 0 0 0 10 9h2.382l-3.276 6.553A1 1 0 0 0 10 17h4a1 1 0 0 0 1-1l-.007-.117A1 1 0 0 0 14 15h-2.382l3.276-6.553A1 1 0 0 0 14 7"/>
</svg>