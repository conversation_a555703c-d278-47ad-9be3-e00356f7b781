from PyQt6.QtWidgets import QPushButton, QLabel, QGraphicsDropShadowEffect, QWidget, QVBoxLayout
from PyQt6.QtGui import QPainter, QColor, QLinearGradient, QPen, QBrush, QPainterPath, QPolygon, QRadialGradient, QIcon, QPixmap
from PyQt6.QtCore import Qt, QPropertyAnimation, pyqtProperty, QPoint, QTimer, QRect, QSize, QEasingCurve, QRectF, QPointF, QEvent
import math

class CustomToolTip(QLabel):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.setStyleSheet("""
            QLabel {
                color: white;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(150)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def paintEvent(self, event):
        if self.isVisible():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            path = QPainterPath()
            rect = QRectF(self.rect()).adjusted(1, 1, -1, -1)
            radius = min(rect.height(), rect.width()) * 0.47
            path.addRoundedRect(rect, radius, radius)
            painter.setPen(Qt.PenStyle.NoPen)
            for i in range(2):
                shadow_path = QPainterPath()
                shadow_rect = rect.adjusted(-i, -i, i, i)
                shadow_path.addRoundedRect(shadow_rect, radius, radius)
                painter.setBrush(QColor(0, 0, 0, 15 - i * 5))
                painter.drawPath(shadow_path)
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0.0, QColor(65, 65, 65, 250))
            gradient.setColorAt(0.5, QColor(55, 55, 55, 250))
            gradient.setColorAt(1.0, QColor(50, 50, 50, 250))
            painter.setBrush(gradient)
            painter.drawPath(path)
            pen = QPen()
            pen.setColor(QColor(255, 255, 255, 30))
            pen.setWidth(1)
            pen.setStyle(Qt.PenStyle.SolidLine)
            painter.setPen(pen)
            painter.drawPath(path)
            super().paintEvent(event)
        
    def hideEvent(self, event):
        self.opacity_animation.stop()
        self.scale_animation.stop()
        super().hideEvent(event)

    def show_tooltip(self, pos):
        self.adjustSize()
        final_pos = pos + QPoint(-self.width() // 2, 35)
        self.move(final_pos)
        self.show()

class CustomBaseButton(QPushButton):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(32, 32)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self._scale = 1.0
        self.setAttribute(Qt.WidgetAttribute.WA_Hover)
        self.setMouseTracking(True)
        
        # Efecto de sombra común
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(8)
        self.shadow.setXOffset(0)
        self.shadow.setYOffset(0)
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.setGraphicsEffect(self.shadow)
        
        # Animación común
        self._animation = QPropertyAnimation(self, b"scale", self)
        self._animation.setDuration(100)
        
        # Tooltip común
        self.tooltip = CustomToolTip("")
        self.tooltip_timer = QTimer(self)
        self.tooltip_timer.setSingleShot(True)
        self.tooltip_timer.timeout.connect(self.show_tooltip)
        self.setStyleSheet("QPushButton { border: none; background: transparent; }")
        self.setEnabled(True)

    @pyqtProperty(float)
    def scale(self):
        return self._scale

    @scale.setter
    def scale(self, value):
        self._scale = value
        self.update()

    def enterEvent(self, event):
        self._animation.setStartValue(1.0)
        self._animation.setEndValue(1.08)  # Aumentado de 1.05 a 1.08
        self._animation.start()
        if self.tooltip.text():
            self.tooltip_timer.start(100)

    def leaveEvent(self, event):
        self._animation.setStartValue(1.08)  # Aumentado de 1.05 a 1.08
        self._animation.setEndValue(1.0)
        self._animation.start()
        self.tooltip_timer.stop()
        if self.tooltip and self.tooltip.isVisible():
            self.tooltip.hide()

    def hideTooltip(self):
        self.tooltip_timer.stop()
        if self.tooltip and self.tooltip.isVisible():
            self.tooltip.hide()

    def show_tooltip(self):
        if not self.tooltip.isVisible() and self.tooltip.text():
            pos = self.mapToGlobal(QPoint(0, 0))
            self.tooltip.show_tooltip(pos + QPoint(self.width()//2, 0))

    def setToolTip(self, text):
        self.tooltip.setText(text)

    def drawButtonBase(self, painter, gradient_colors):
        """Método común para dibujar el botón base con aro exterior y sombra 3D"""
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        button_size = 26  # Aumentado de 22 a 26
        for i in range(3):
            shadow_path = QPainterPath()
            shadow_rect = QRectF(0, 0, button_size, button_size).adjusted(i, i, -i, -i)
            shadow_path.addEllipse(shadow_rect)
            if i == 0:
                shadow_color = QColor(0, 0, 0, 40)
            elif i == 1:
                shadow_color = QColor(0, 0, 0, 25)
            else:
                shadow_color = QColor(0, 0, 0, 15)
            painter.setPen(QPen(shadow_color, 1.0))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawPath(shadow_path)
        
        # Círculo exterior (aro)
        outer_gradient = QLinearGradient(0, 0, 0, button_size)
        outer_gradient.setColorAt(0.0, QColor(255, 255, 255, 60))
        outer_gradient.setColorAt(0.5, QColor(255, 255, 255, 30))
        outer_gradient.setColorAt(1.0, QColor(255, 255, 255, 10))
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QBrush(outer_gradient))
        painter.drawEllipse(1, 1, button_size-2, button_size-2)
        
        # Brillo superior sutil
        highlight = QLinearGradient(0, 2, 0, button_size * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 30))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        painter.setBrush(QBrush(highlight))
        painter.drawEllipse(2, 2, button_size-4, button_size-4)

    def drawFloatingShadow(self, painter, center_x, center_y):
        """Método común para dibujar sombra flotante"""
        painter.setPen(Qt.PenStyle.NoPen)
        shadow_gradient = QRadialGradient(center_x, center_y, 8)
        shadow_gradient.setColorAt(0, QColor(0, 0, 0, 50))
        shadow_gradient.setColorAt(1, QColor(0, 0, 0, 0))
        painter.setBrush(shadow_gradient)
        painter.drawEllipse(QPoint(center_x + 1, center_y + 1), 8, 8)

    def paintEvent(self, event):
        """Template method para el pintado del botón"""
        if not self.isVisible():
            return
        painter = QPainter(self)
        if not painter.isActive():
            return
        try:
            button_size = 26  # Aumentado de 22 a 26
            margin = (self.width() - button_size) / 2
            
            painter.translate(self.width()/2, self.height()/2)
            painter.scale(self._scale, self._scale)
            painter.translate(-self.width()/2, -self.height()/2)
            
            painter.translate(margin, margin)
            
            self.drawButtonBase(painter, self.getGradientColors())
            self.drawSymbol(painter)
        finally:
            painter.end()
    
    def getGradientColors(self):
        """Método base para obtener los colores del gradiente"""
        # Color por defecto gris en caso de que una clase hija no implemente sus propios colores
        return {
            0.0: QColor(158, 158, 158),  # Gris claro
            0.4: QColor(117, 117, 117),  # Gris medio
            0.6: QColor(97, 97, 97),     # Gris medio-oscuro
            1.0: QColor(66, 66, 66)      # Gris oscuro
        }
    
    def drawSymbol(self, painter):
        """Método abstracto para dibujar el símbolo específico"""
        raise NotImplementedError

class ButtonColors:
    """Clase para centralizar los colores de los botones"""
    @staticmethod
    def get_colors(mode="normal", button_type="close"):
        colors = {
            "close": {
                "normal": {
                    0.0: QColor(255, 82, 82),   # Rojo claro
                    0.4: QColor(244, 67, 54),   # Rojo medio
                    0.6: QColor(229, 57, 53),   # Rojo medio-oscuro
                    1.0: QColor(211, 47, 47)    # Rojo oscuro
                },
            },
            "maximize": {
                "normal": {
                    0.0: QColor(66, 165, 245),  # Azul claro
                    0.4: QColor(33, 150, 243),  # Azul medio
                    0.6: QColor(30, 136, 229),  # Azul medio-oscuro
                    1.0: QColor(25, 118, 210)   # Azul oscuro
                },
            },
            "minimize": {
                "normal": {
                    0.0: QColor(129, 199, 132), # Verde claro
                    0.4: QColor(76, 175, 80),   # Verde medio
                    0.6: QColor(67, 160, 71),   # Verde medio-oscuro
                    1.0: QColor(56, 142, 60)    # Verde oscuro
                },
            }
        }
        
        # Colores comunes para modos comparison y copying
        comparison_colors = {
            0.0: QColor(66, 165, 245),  # Azul claro
            0.4: QColor(33, 150, 243),  # Azul medio
            0.6: QColor(30, 136, 229),  # Azul medio-oscuro
            1.0: QColor(25, 118, 210)   # Azul oscuro
        }
        
        copying_colors = {
            0.0: QColor(129, 199, 132), # Verde claro
            0.4: QColor(76, 175, 80),   # Verde medio
            0.6: QColor(67, 160, 71),   # Verde medio-oscuro
            1.0: QColor(56, 142, 60)    # Verde oscuro
        }

        if mode == "comparison":
            return comparison_colors
        elif mode == "copying":
            return copying_colors
        else:
            return colors.get(button_type, {}).get("normal", colors["close"]["normal"])

class WindowControlButton(CustomBaseButton):
    """Clase base para botones de control de ventana"""
    def __init__(self, tooltip="", parent=None):
        super().__init__(parent)
        self.setToolTip(tooltip)
        self.setFixedSize(28, 28)
        self.current_mode = "normal"
        self.clicked.connect(self.play_button_sound)
    def set_mode(self, mode):
        self.current_mode = mode
        self.update()
    def play_button_sound(self):
        """Reproduce el sonido de botón cuando se hace clic"""
        try:
            from SONIDOS import sound_manager
            sound_manager.play_sound('button')
        except Exception as e:
            print(f"Error al reproducir sonido de botón: {e}")
    def drawButtonBase(self, painter, gradient_colors):
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        button_size = self.width() - 4
        
        painter.translate(self.width()/2, self.height()/2)
        painter.scale(self._scale, self._scale)
        painter.translate(-self.width()/2, -self.height()/2)
        
        gradient = QLinearGradient(0, 0, 0, self.height())
        for position, color in gradient_colors.items():
            gradient.setColorAt(position, color)
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QBrush(gradient))
        painter.drawEllipse(2, 2, button_size, button_size)

    def paintEvent(self, event):
        if not self.isVisible():
            return
        painter = QPainter(self)
        if not painter.isActive():
            return
        try:
            painter.translate(self.width()/2, self.height()/2)
            painter.scale(self._scale, self._scale)
            painter.translate(-self.width()/2, -self.height()/2)
            self.drawButtonBase(painter, self.getGradientColors())
            self.drawSymbol(painter)
        finally:
            painter.end()

class CustomCloseButton(WindowControlButton):
    def __init__(self, parent=None):
        super().__init__("Cerrar", parent)
        self.current_mode = "normal"
        
    def getGradientColors(self):
        return ButtonColors.get_colors(self.current_mode, "close")
    
    def drawSymbol(self, painter):
        margin = 9
        painter.setPen(QPen(QColor(255, 255, 255), 2.0, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.drawLine(margin, margin, self.width() - margin, self.height() - margin)
        painter.drawLine(self.width() - margin, margin, margin, self.height() - margin)
    
    def play_button_sound(self):
        """Sobrescribir para que no reproduzca sonido"""
        pass

class CustomMaximizeButton(WindowControlButton):
    def __init__(self, parent=None):
        super().__init__("Maximizar", parent)
        self.current_mode = "normal"
        
    def getGradientColors(self):
        return ButtonColors.get_colors(self.current_mode, "maximize")
    
    def drawSymbol(self, painter):
        margin = 9
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # + blanco
        painter.setPen(QPen(QColor(255, 255, 255), 2.0, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.drawLine(center_x, margin, center_x, self.height() - margin)
        painter.drawLine(margin, center_y, self.width() - margin, center_y)
    
    def play_button_sound(self):
        """Sobrescribir para que no reproduzca sonido"""
        pass

class CustomMinimizeButton(WindowControlButton):
    def __init__(self, parent=None):
        super().__init__("Minimizar", parent)
        self.current_mode = "normal"
        
    def getGradientColors(self):
        return ButtonColors.get_colors(self.current_mode, "minimize")
    
    def drawSymbol(self, painter):
        margin = 9
        center_y = self.height() // 2
        painter.setPen(QPen(QColor(255, 255, 255), 2.0, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.drawLine(margin, center_y, self.width() - margin, center_y)
    
    def play_button_sound(self):
        """Sobrescribir para que no reproduzca sonido"""
        pass

class CustomLoadButton(CustomBaseButton):
    def drawSymbol(self, painter):
        center_x = 13
        center_y = 13
        arrow_width = 10  # Aumentado
        arrow_height = 11  # Aumentado
        painter.setPen(QPen(QColor(156, 39, 176), 1.5, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.setBrush(QColor(156, 39, 176))
        arrow_path = QPainterPath()
        arrow_path.moveTo(center_x, center_y + arrow_height//2)
        arrow_path.lineTo(center_x - arrow_width//2, center_y)
        arrow_path.lineTo(center_x - 2, center_y)
        arrow_path.lineTo(center_x - 2, center_y - arrow_height//2)
        arrow_path.lineTo(center_x + 2, center_y - arrow_height//2)
        arrow_path.lineTo(center_x + 2, center_y)
        arrow_path.lineTo(center_x + arrow_width//2, center_y)
        arrow_path.closeSubpath()
        painter.drawPath(arrow_path)

class CustomModeButton(CustomBaseButton):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_mode = "normal"
    def set_mode(self, mode):
        """Establece el modo actual del botón"""
        self.current_mode = mode
        self.update()  # Forzar el redibujado del botón
    def getGradientColors(self):
        if self.current_mode == "normal":
            return {
                0.0: QColor(255, 82, 82),  # Rojo claro
                0.4: QColor(244, 67, 54),  # Rojo medio
                0.6: QColor(229, 57, 53),  # Rojo medio-oscuro
                1.0: QColor(211, 47, 47)   # Rojo oscuro
            }
        elif self.current_mode == "comparison":
            return {
                0.0: QColor(66, 165, 245),  # Azul claro
                0.4: QColor(33, 150, 243),  # Azul medio
                0.6: QColor(30, 136, 229),  # Azul medio-oscuro
                1.0: QColor(25, 118, 210)   # Azul oscuro
            }
        else:  
            return {
                0.0: QColor(129, 199, 132),  # Verde claro
                0.4: QColor(76, 175, 80),    # Verde medio
                0.6: QColor(67, 160, 71),    # Verde medio-oscuro
                1.0: QColor(56, 142, 60)     # Verde oscuro
            }
    
    def drawSymbol(self, painter):
        center_x = 13
        center_y = 13
        if self.current_mode == "normal":
            # Símbolo de prohibición
            circle_radius = 8
            line_width = 2
            
            # Círculo exterior
            painter.setPen(QPen(QColor(255, 255, 255), line_width, Qt.PenStyle.SolidLine))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawEllipse(QPoint(center_x, center_y), circle_radius, circle_radius)
            
            # Línea diagonal
            painter.setPen(QPen(QColor(255, 255, 255), line_width + 0.5, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
            painter.drawLine(
                center_x - circle_radius//2, center_y - circle_radius//2,
                center_x + circle_radius//2, center_y + circle_radius//2
            )
        elif self.current_mode == "comparison":
            # Dimensiones aumentadas de la carpeta
            folder_width = 16  # Aumentado de 12 a 16
            folder_height = 11  # Aumentado de 8 a 11
            tab_width = int(folder_width * 0.4)
            tab_height = 3  # Aumentado de 2 a 3
            corner_radius = 1.5  # Aumentado de 1 a 1.5
            
            # Punto base de la carpeta (centrado)
            x = center_x - folder_width//2
            y = center_y - folder_height//2
            
            # Sombra de la carpeta
            shadow_offset = 0.8  # Aumentado de 0.5 a 0.8
            shadow_path = QPainterPath()
            shadow_path.moveTo(x + corner_radius + shadow_offset, y + shadow_offset)
            shadow_path.lineTo(x + tab_width + shadow_offset, y + shadow_offset)
            shadow_path.quadTo(x + tab_width + 2 + shadow_offset, y + shadow_offset, 
                             x + tab_width + 2 + shadow_offset, y + 1 + shadow_offset)
            shadow_path.lineTo(x + folder_width - corner_radius + shadow_offset, y + 1 + shadow_offset)
            shadow_path.quadTo(x + folder_width + shadow_offset, y + 1 + shadow_offset,
                             x + folder_width + shadow_offset, y + 1 + corner_radius + shadow_offset)
            shadow_path.lineTo(x + folder_width + shadow_offset, y + folder_height - corner_radius + shadow_offset)
            shadow_path.quadTo(x + folder_width + shadow_offset, y + folder_height + shadow_offset,
                             x + folder_width - corner_radius + shadow_offset, y + folder_height + shadow_offset)
            shadow_path.lineTo(x + corner_radius + shadow_offset, y + folder_height + shadow_offset)
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(QColor(0, 0, 0, 50))  # Sombra un poco más oscura
            painter.drawPath(shadow_path)
            folder_gradient = QLinearGradient(x, y, x, y + folder_height)
            folder_gradient.setColorAt(0.0, QColor(255, 215, 89))  # Amarillo más claro arriba
            folder_gradient.setColorAt(0.5, QColor(255, 205, 69))  # Amarillo medio
            folder_gradient.setColorAt(1.0, QColor(255, 195, 59))  # Amarillo más oscuro abajo
            folder_path = QPainterPath()
            folder_path.moveTo(x + corner_radius, y)
            folder_path.lineTo(x + tab_width, y)
            folder_path.quadTo(x + tab_width + 2, y, x + tab_width + 2, y + 1)
            folder_path.lineTo(x + folder_width - corner_radius, y + 1)
            folder_path.quadTo(x + folder_width, y + 1, x + folder_width, y + 1 + corner_radius)
            folder_path.lineTo(x + folder_width, y + folder_height - corner_radius)
            folder_path.quadTo(x + folder_width, y + folder_height, x + folder_width - corner_radius, y + folder_height)
            folder_path.lineTo(x + corner_radius, y + folder_height)
            folder_path.quadTo(x, y + folder_height, x, y + folder_height - corner_radius)
            folder_path.lineTo(x, y + corner_radius)
            folder_path.quadTo(x, y, x + corner_radius, y)
            painter.setBrush(folder_gradient)
            painter.setPen(QPen(QColor(255, 187, 36), 1.2))  # Borde un poco más grueso
            painter.drawPath(folder_path)
            highlight_gradient = QLinearGradient(x, y, x, y + folder_height * 0.4)  # Aumentado área de brillo
            highlight_gradient.setColorAt(0.0, QColor(255, 255, 255, 100))  # Brillo más intenso
            highlight_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))
            highlight_path = QPainterPath()
            highlight_path.moveTo(x + corner_radius, y + 0.5)
            highlight_path.lineTo(x + folder_width - corner_radius, y + 0.5)
            highlight_path.quadTo(x + folder_width - 0.5, y + 0.5, x + folder_width - 0.5, y + corner_radius)
            highlight_path.lineTo(x + folder_width - 0.5, y + folder_height * 0.4)
            highlight_path.lineTo(x + 0.5, y + folder_height * 0.4)
            highlight_path.lineTo(x + 0.5, y + corner_radius)
            highlight_path.quadTo(x + 0.5, y + 0.5, x + corner_radius, y + 0.5)
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(highlight_gradient)
            painter.drawPath(highlight_path)
            tab_gradient = QLinearGradient(x, y, x, y + tab_height)
            tab_gradient.setColorAt(0.0, QColor(255, 225, 99))  # Más claro
            tab_gradient.setColorAt(1.0, QColor(255, 215, 89))  # Más oscuro
            painter.setBrush(tab_gradient)
            painter.setPen(QPen(QColor(255, 187, 36), 1.2))
            tab_path = QPainterPath()
            tab_path.moveTo(x + tab_width, y)
            tab_path.lineTo(x + tab_width + 2, y)
            tab_path.lineTo(x + tab_width + 2, y + tab_height)
            tab_path.lineTo(x + tab_width, y + tab_height)
            painter.drawPath(tab_path)
        else: 
            symbol_width = 10
            symbol_height = 8
            line_width = 1.5
            painter.setPen(QPen(QColor(255, 255, 255), line_width, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
            points = [
                QPoint(center_x - symbol_width//2, center_y),  # Izquierda
                QPoint(center_x, center_y - symbol_height//2),  # Arriba
                QPoint(center_x + symbol_width//2, center_y),  # Derecha
                QPoint(center_x, center_y + symbol_height//2)   # Abajo
            ]
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawPolygon(points)

def crear_icono_svg(svg_data, size, add_gradient=True):
    """
    Función auxiliar para crear iconos a partir de datos SVG.
    
    Args:
        svg_data (str): Datos SVG en formato string
        size (int): Tamaño del icono en píxeles
        add_gradient (bool): Si se debe agregar el efecto de gradiente de brillo
        
    Returns:
        QIcon: Icono generado
        QPixmap: Pixmap del icono (opcional, solo si add_gradient es False)
    """
    from PyQt6.QtSvg import QSvgRenderer
    from PyQt6.QtGui import QIcon, QPixmap, QPainter, QLinearGradient, QColor, QRadialGradient
    from PyQt6.QtCore import QByteArray, Qt, QPointF, QRectF
    
    # Crear pixmap desde los datos SVG
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    # Convertir el SVG a QByteArray
    svg_bytes = QByteArray(svg_data.encode('utf-8'))
    
    # Renderizar SVG en el pixmap
    renderer = QSvgRenderer(svg_bytes)
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # Dibuja el icono base
    renderer.render(painter)
    
    if add_gradient:
        # Guardar el estado del pintor para restaurarlo después
        painter.save()
        
        # Efecto 1: Gradiente radial para simular iluminación 3D
        radial_gradient = QRadialGradient(
            QPointF(size * 0.3, size * 0.3),  # Centro del gradiente en 30%
            size * 0.7  # Radio del gradiente
        )
        radial_gradient.setColorAt(0, QColor(255, 255, 255, 60))  # Brillo en el centro
        radial_gradient.setColorAt(1, QColor(255, 255, 255, 0))   # Transparente en bordes
        
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceAtop)
        painter.fillRect(pixmap.rect(), radial_gradient)
        
        # Efecto 2: Sombra sutil en la parte inferior
        shadow_gradient = QLinearGradient(QPointF(0, size * 0.7), QPointF(0, size))
        shadow_gradient.setColorAt(0, QColor(0, 0, 0, 0))      # Transparente arriba
        shadow_gradient.setColorAt(1, QColor(0, 0, 0, 40))     # Sombra abajo
        
        painter.fillRect(QRectF(0, size * 0.7, size, size * 0.3), shadow_gradient)
        
        # Efecto 3: Brillo superior como en la versión original
        top_gradient = QLinearGradient(QPointF(0, 0), QPointF(0, size * 0.5))
        top_gradient.setColorAt(0, QColor(255, 255, 255, 80))  # Brillo arriba
        top_gradient.setColorAt(1, QColor(255, 255, 255, 0))   # Transparente abajo
        
        painter.fillRect(QRectF(0, 0, size, size * 0.5), top_gradient)
        
        # Restaurar el estado del pintor
        painter.restore()
    
    painter.end()
    
    if add_gradient:
        return QIcon(pixmap)
    else:
        return QIcon(pixmap), pixmap


def icono_espejo_normal(size=32):
    svg_data = """<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><g fill="none"><path fill="url(#fluentColorErrorCircle160)" d="M2 8a6 6 0 1 1 12 0A6 6 0 0 1 2 8"/><path fill="url(#fluentColorErrorCircle161)" d="M8 10a.75.75 0 1 0 0 1.5a.75.75 0 0 0 0-1.5m0-5.5a.5.5 0 0 0-.492.41L7.5 5v3.5l.008.09a.5.5 0 0 0 .984 0L8.5 8.5V5l-.008-.09A.5.5 0 0 0 8 4.5"/><defs><linearGradient id="fluentColorErrorCircle160" x1="3.875" x2="11.75" y1=".125" y2="15.125" gradientUnits="userSpaceOnUse"><stop stop-color="#ffcd0f"/><stop offset="1" stop-color="#fe8401"/></linearGradient><linearGradient id="fluentColorErrorCircle161" x1="6" x2="9.213" y1="4.5" y2="11.844" gradientUnits="userSpaceOnUse"><stop stop-color="#4a4a4a"/><stop offset="1" stop-color="#212121"/></linearGradient></defs></g></svg>"""
    return crear_icono_svg(svg_data, size, add_gradient=False)

def icono_espejo_comparar(size=32):
    svg_data = """<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill="#329eff" d="M1 4.5A2.5 2.5 0 0 1 3.5 2h2.086a1.5 1.5 0 0 1 1.06.44L8 3.792l-2.06 2.06A.5.5 0 0 1 5.585 6H1zM1 7v4.5A2.5 2.5 0 0 0 3.5 14h3.1A5.5 5.5 0 0 1 15 7.257V6.5A2.5 2.5 0 0 0 12.5 4H9.207l-2.56 2.56A1.5 1.5 0 0 1 5.585 7zm6 4.5a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0m6.5-3a.5.5 0 0 1 .5.5v1.5a.5.5 0 0 1-.5.5H12a.5.5 0 0 1 0-1h.468a2 2 0 0 0-.933-.25a2 2 0 0 0-1.45.586a.5.5 0 0 1-.706-.707A3 3 0 0 1 13 9.152V9a.5.5 0 0 1 .5-.5m-.876 5.532A3 3 0 0 1 10 13.848V14a.5.5 0 0 1-1 0v-1.5a.5.5 0 0 1 .5-.5H11a.5.5 0 0 1 0 1h-.468q.075.042.155.077a2 2 0 0 0 2.227-.413a.5.5 0 0 1 .707.707c-.285.285-.624.51-.997.66"/></svg>"""
    return crear_icono_svg(svg_data, size, add_gradient=False)

def icono_espejo_copiar(size=40):
    svg_data = """<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path fill="#b35aca" d="M11.146 6.854a.5.5 0 0 0 .708-.708L10.707 5H12.5A2.5 2.5 0 0 1 15 7.5V12a3 3 0 1 0 1 .17V7.5A3.5 3.5 0 0 0 12.5 4h-1.793l1.147-1.146a.5.5 0 0 0-.708-.708l-2 2a.5.5 0 0 0 0 .708zM5 8a3 3 0 1 0-1-.17v4.67A3.5 3.5 0 0 0 7.5 16h1.793l-1.147 1.146a.5.5 0 0 0 .708.708l2-2a.5.5 0 0 0 .146-.351v-.006a.5.5 0 0 0-.146-.35l-2-2a.5.5 0 0 0-.708.707L9.293 15H7.5A2.5 2.5 0 0 1 5 12.5z"/></svg>"""
    return crear_icono_svg(svg_data, size, add_gradient=False)

def icono_expulsar_usb(size=32):
    svg_data = """<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#bb32de" d="M10.586 3L4 9.586a2 2 0 0 0-.434 2.18l.068.145A2 2 0 0 0 5.414 13H8v2a1 1 0 0 0 1 1h6l.117-.007A1 1 0 0 0 16 15l-.001-2h2.587A2 2 0 0 0 20 9.586L13.414 3a2 2 0 0 0-2.828 0M15 20a1 1 0 0 1 .117 1.993L15 22H9a1 1 0 0 1-.117-1.993L9 20zm0-3a1 1 0 0 1 .117 1.993L15 19H9a1 1 0 0 1-.117-1.993L9 17z"/></svg>"""
    return crear_icono_svg(svg_data, size, add_gradient=False)

def restaurar_copia(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#3d8a10" d="M16.088 6.412a2.84 2.84 0 0 0-1.347-.955l-1.378-.448a.544.544 0 0 1 0-1.025l1.378-.448A2.84 2.84 0 0 0 16.5 1.774l.011-.034l.448-1.377a.544.544 0 0 1 1.027 0l.447 1.377a2.84 2.84 0 0 0 1.799 1.796l1.377.448l.028.007a.544.544 0 0 1 0 1.025l-1.378.448a2.84 2.84 0 0 0-1.798 1.796l-.448 1.377l-.013.034a.544.544 0 0 1-1.013-.034l-.448-1.377a2.8 2.8 0 0 0-.45-.848m7.695 3.801l-.766-.248a1.58 1.58 0 0 1-.998-.999l-.25-.764a.302.302 0 0 0-.57 0l-.248.764a1.58 1.58 0 0 1-.984.999l-.765.248a.302.302 0 0 0 0 .57l.765.249a1.58 1.58 0 0 1 1 1.002l.248.764a.302.302 0 0 0 .57 0l.249-.764a1.58 1.58 0 0 1 .999-.999l.765-.248a.302.302 0 0 0 0-.57zM12 2.001c.957 0 1.883.134 2.76.385q-.175.107-.37.173l-1.34.44A1.565 1.565 0 0 0 12 4.469c.002.635.416 1.277 1 1.53l1.4.46c.516.175 1.014.566 1.19 1.09l.41 1.45a1.55 1.55 0 0 0 1.46 1.05c.236.003.47-.052.68-.16a1.3 1.3 0 0 0-.18.66c-.004.54.362 1.048.87 1.23q.111.034.244.066c.36.091.79.2.906.554l.014.045c.236.749.506 1.605 1.466 1.605q.185 0 .338-.04c-.93 4.56-4.963 7.992-9.798 7.992c-5.523 0-10-4.477-10-10S6.477 2 12 2M7.47 12.28l.083.072a.75.75 0 0 0 .977-.072l2.72-2.72v6.69l.007.102A.75.75 0 0 0 12 17l.101-.007a.75.75 0 0 0 .649-.743V9.56l2.72 2.721l.084.073a.75.75 0 0 0 .977-1.133l-4-4.002l-.084-.072a.75.75 0 0 0-.977.072l-4 4.001l-.073.084a.75.75 0 0 0 .072.977"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_no_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#f25252" fill-rule="evenodd" d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10m4.066-14.066a.75.75 0 0 1 0 1.06L13.06 12l3.005 3.005a.75.75 0 0 1-1.06 1.06L12 13.062l-3.005 3.005a.75.75 0 1 1-1.06-1.06L10.938 12L7.934 8.995a.75.75 0 1 1 1.06-1.06L12 10.938l3.005-3.005a.75.75 0 0 1 1.06 0" clip-rule="evenodd"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_yes_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48"><circle cx="24" cy="24" r="21" fill="#4caf50"/><path fill="#ccff90" d="M34.6 14.6L21 28.2l-5.6-5.6l-2.8 2.8l8.4 8.4l16.4-16.4z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_reindex_icon(size=32):
    # Datos SVG para el icono de reindexación
    svg_data = """<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 256 256">
    <path fill="#3880ff" d="M128.152.001c20.777 0 40.34 4.854 58.692 14.257l2.426 1.214l-2.123 1.668c-5.156 4.095-9.251 9.403-11.83 15.469l-.758 1.668l-1.516-.758c-14.105-6.673-29.119-10.161-44.74-10.161c-57.782 0-104.644 47.014-104.644 104.644s46.71 104.645 104.493 104.645s104.644-47.014 104.644-104.645c0-13.8-2.578-27.298-8.038-40.038L224 86.296l1.668-.758c6.218-2.275 11.678-6.066 16.076-10.92l1.668-2.123l1.062 2.427C252.209 91.756 256 109.652 256 127.699c0 70.521-57.479 128-128 128S0 198.22 0 127.7S57.479-.3 128.152.001m0 69.764c32 0 58.237 26.086 58.388 58.389c0 32.303-26.085 58.389-58.388 58.389c-32.304 0-58.389-26.086-58.389-58.389S96 69.765 128.152 69.765m83.564-48.227c14.741 0 26.692 11.95 26.692 26.692c0 14.741-11.95 26.692-26.692 26.692s-26.692-11.95-26.692-26.692s11.95-26.692 26.692-26.692" stroke-width="6.5" stroke="#3880ff" />
    </svg>"""
    return crear_icono_svg(svg_data, size, add_gradient=False)

def create_code_icon(size=40): # USA TECNICA  QSvgRenderer  para que sea exactamente el mismo icono
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        <path fill="#b855c1" d="M20.536 20.536C22 19.07 22 16.714 22 12q0-.901-.003-1.692c-.41.402-.902.828-1.429 1.284L18.08 13.75c-.304.264-.645.56-.953.758c-.302.194-1.07.622-1.957.217s-1.066-1.266-1.117-1.62S14 12.312 14 11.917c-1.33.23-2.596.754-3.527 1.48l-.05.04c-.368.288-.72.564-1.004.748a2.8 2.8 0 0 1-.622.314c-.235.08-.817.225-1.417-.127a1.7 1.7 0 0 1-.793-1.106a2.5 2.5 0 0 1-.048-.696c.017-.314.08-.716.14-1.11l.01-.064c.475-3.07 1.812-5.166 3.55-6.473a8.55 8.55 0 0 1 3.76-1.585v-.225c0-.363 0-.766.039-1.107Q13.1 1.999 12 2C7.286 2 4.929 2 3.464 3.464C2 4.93 2 7.286 2 12s0 7.071 1.464 8.535C4.93 22 7.286 22 12 22s7.071 0 8.535-1.465"/>
        <path fill="#b855c1" d="m17.155 2.434l2.357 2.043c1.623 1.406 2.434 2.11 2.434 3.023s-.811 1.616-2.434 3.023l-2.357 2.043c-.714.618-1.07.927-1.363.794c-.292-.134-.292-.606-.292-1.55v-1.524c-2.124 0-4.374.698-5.95 1.93c-.824.644-1.235.966-1.411.862s-.107-.553.032-1.452c.845-5.462 4.63-6.912 7.329-6.912V3.19c0-.944 0-1.416.292-1.55c.293-.133.65.176 1.363.794"/>
    </svg>'''
    return crear_icono_svg(svg_data, size)

def create_clear_icon(size=40): # USA TECNICA  QSvgRenderer  para que sea exactamente el mismo icono
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
        <path fill="#d73939" fill-rule="evenodd" d="M5.692 2.44a1.392 1.392 0 0 1 2.616 0l.149.41H5.543zm-1.745.41l.336-.922a2.892 2.892 0 0 1 5.434 0l.336.922h3.197a.75.75 0 0 1 0 1.5h-.766l-.377 7.19c-.08 1.114-.897 2.078-2.044 2.23a23 23 0 0 1-6.126 0c-1.147-.152-1.965-1.116-2.044-2.23l-.377-7.19H.75a.75.75 0 0 1 0-1.5zm3.49 3.236a.7.7 0 0 0-.886-.017c-.77.605-1.211 1.064-1.902 1.908a.5.5 0 0 0 .387.817h1.339v2.121a.625.625 0 1 0 1.25 0V8.794h1.296a.5.5 0 0 0 .38-.825c-.67-.786-1.096-1.235-1.863-1.883" clip-rule="evenodd"/>
    </svg>'''
    return crear_icono_svg(svg_data, size)

def boton_empaketar(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><path fill="#418826" fill-rule="evenodd" d="M12.578 1.98c1.893-.222 4.91-.43 9.672-.472v8.997c-6.329.03-11.047.217-14.166.396a9.9 9.9 0 0 0-4.037 1.111A84 84 0 0 1 7.27 5.691c1.09-1.937 2.915-3.43 5.308-3.711m27.338 8.92c-3.119-.178-7.837-.366-14.166-.396V1.508c4.762.041 7.779.25 9.672.472c2.393.281 4.218 1.774 5.308 3.71a84 84 0 0 1 3.223 6.322a9.9 9.9 0 0 0-4.037-1.111ZM24 46.5c-7.159 0-12.402-.204-15.744-.395c-3.292-.188-6.006-2.625-6.324-5.99c-.227-2.4-.432-5.77-.432-10.115c0-4.343.205-7.712.432-10.115c.318-3.364 3.032-5.8 6.324-5.989c3.342-.191 8.586-.396 15.744-.396c7.159 0 12.402.205 15.744.396c3.292.188 6.006 2.625 6.324 5.99c.227 2.402.432 5.771.432 10.114s-.205 7.714-.432 10.116c-.318 3.364-3.032 5.8-6.324 5.989c-3.342.191-8.586.396-15.744.396Zm3.418-17.815L27.1 40.871c-.023.855-.59 1.602-1.438 1.721a12 12 0 0 1-1.662.119a12 12 0 0 1-1.662-.119c-.848-.12-1.416-.866-1.438-1.722l-.318-12.185a130 130 0 0 1-3.402-.095c-2.057-.087-2.857-2.037-1.407-3.5A73 73 0 0 1 18 22.947c2.049-1.898 3.573-2.963 4.572-3.55a2.77 2.77 0 0 1 2.855 0c1 .587 2.524 1.652 4.573 3.55c.88.815 1.614 1.528 2.225 2.144c1.45 1.462.65 3.413-1.407 3.5c-.93.04-2.053.073-3.4.095Z" clip-rule="evenodd"/></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_back(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 56 56">
        <!-- Círculo de fondo -->
        <path fill="#7c3aed" d="M28 51.906c13.055 0 23.906-10.828 23.906-23.906c0-13.055-10.875-23.906-23.93-23.906C14.899 4.094 4.095 14.945 4.095 28c0 13.078 10.828 23.906 23.906 23.906"/>
        
        <!-- Sombra negra de la flecha (desplazada) -->
        <path fill="black" 
              opacity="0.3" 
              transform="translate(1,1)"
              d="M32.312 40.188c-.656.656-1.921.562-2.625-.07L19.422 30.46c-1.36-1.313-1.383-3.563 0-4.852l10.265-9.656c.774-.726 1.899-.773 2.602-.07c.727.726.797 1.945.047 2.648L22.21 28.023l10.125 9.54c.703.68.726 1.874-.024 2.624"/>
        
        <!-- Flecha principal blanca -->
        <path fill="white" 
              d="M32.312 40.188c-.656.656-1.921.562-2.625-.07L19.422 30.46c-1.36-1.313-1.383-3.563 0-4.852l10.265-9.656c.774-.726 1.899-.773 2.602-.07c.727.726.797 1.945.047 2.648L22.21 28.023l10.125 9.54c.703.68.726 1.874-.024 2.624"/>
    </svg>'''
    return crear_icono_svg(svg_data, size)

def boton_mapear_puertos(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256"><path fill="#ff8402" d="M252 128a4 4 0 0 1-1.78 3.33l-48 32A4 4 0 0 1 196 160v-24H72v48h36v-8a12 12 0 0 1 12-12h32a12 12 0 0 1 12 12v32a12 12 0 0 1-12 12h-32a12 12 0 0 1-12-12v-8H72a16 16 0 0 1-16-16v-48H8a8 8 0 0 1 0-16h48V72a16 16 0 0 1 16-16h37.17a28 28 0 1 1 0 16H72v48h124V96a4 4 0 0 1 6.22-3.33l48 32A4 4 0 0 1 252 128" stroke-width="6.5" stroke="#ff8402"/></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_pago(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#478c1e" d="M2.047 14.668a1 1 0 0 0 .465.607l1.91 1.104v2.199a1 1 0 0 0 1 1h2.199l1.104 1.91a1.01 1.01 0 0 0 .866.5c.174 0 .347-.046.501-.135L12 20.75l1.91 1.104a1 1 0 0 0 1.366-.365l1.103-1.91h2.199a1 1 0 0 0 1-1V16.38l1.91-1.104a1 1 0 0 0 .365-1.367L20.75 12l1.104-1.908a1 1 0 0 0-.365-1.366l-1.91-1.104v-2.2a1 1 0 0 0-1-1H16.38l-1.103-1.909a1 1 0 0 0-.607-.466a1 1 0 0 0-.759.1L12 3.25l-1.909-1.104a1 1 0 0 0-1.366.365l-1.104 1.91H5.422a1 1 0 0 0-1 1V7.62l-1.91 1.104a1.003 1.003 0 0 0-.365 1.368L3.251 12l-1.104 1.908a1 1 0 0 0-.1.76M12 13c-3.48 0-4-1.879-4-3c0-1.287 1.029-2.583 3-2.915V6.012h2v1.109c1.734.41 2.4 1.853 2.4 2.879h-1l-1 .018C13.386 9.638 13.185 9 12 9c-1.299 0-2 .515-2 1c0 .374 0 1 2 1c3.48 0 4 1.879 4 3c0 1.287-1.029 2.583-3 2.915V18h-2v-1.08c-2.339-.367-3-2.003-3-2.92h2c.011.143.159 1 2 1c1.38 0 2-.585 2-1c0-.325 0-1-2-1"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_reset_alias_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g fill="none"><path fill="url(#fluentColorPeopleSync240)" d="M4.25 13A2.25 2.25 0 0 0 2 15.25v.25S2 20 8 20s6-4.5 6-4.5v-.25A2.25 2.25 0 0 0 11.75 13z"/><path fill="url(#fluentColorPeopleSync241)" d="M4.25 13A2.25 2.25 0 0 0 2 15.25v.25S2 20 8 20s6-4.5 6-4.5v-.25A2.25 2.25 0 0 0 11.75 13z"/><path fill="url(#fluentColorPeopleSync246)" fill-opacity="0.5" d="M4.25 13A2.25 2.25 0 0 0 2 15.25v.25S2 20 8 20s6-4.5 6-4.5v-.25A2.25 2.25 0 0 0 11.75 13z"/><path fill="url(#fluentColorPeopleSync242)" d="M17 11a3 3 0 1 0 0-6a3 3 0 0 0 0 6"/><path fill="url(#fluentColorPeopleSync243)" d="M8 11a4 4 0 1 0 0-8a4 4 0 0 0 0 8"/><path fill="url(#fluentColorPeopleSync244)" fill-rule="evenodd" d="M17.5 23a5.5 5.5 0 1 1 0-11a5.5 5.5 0 0 1 0 11" clip-rule="evenodd"/><path fill="url(#fluentColorPeopleSync245)" fill-rule="evenodd" d="M20 14.5a.5.5 0 0 1 1 0v2a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h1a2.5 2.5 0 0 0-2-1c-.833 0-1.572.407-2.027 1.036a.5.5 0 0 1-.81-.586A3.5 3.5 0 0 1 17.5 14c.98 0 1.865.403 2.5 1.05zm-5 6v-.55A3.5 3.5 0 0 0 17.5 21a3.5 3.5 0 0 0 2.675-1.243a.5.5 0 1 0-.764-.645A2.5 2.5 0 0 1 17.5 20a2.5 2.5 0 0 1-2-1h1a.5.5 0 0 0 0-1h-2a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 1 0" clip-rule="evenodd"/><defs><linearGradient id="fluentColorPeopleSync240" x1="4.854" x2="7.41" y1="13.931" y2="20.927" gradientUnits="userSpaceOnUse"><stop offset=".125" stop-color="#bd96ff"/><stop offset="1" stop-color="#9c6cfe"/></linearGradient><linearGradient id="fluentColorPeopleSync241" x1="8" x2="11.607" y1="12.167" y2="23.721" gradientUnits="userSpaceOnUse"><stop stop-color="#885edb" stop-opacity="0"/><stop offset="1" stop-color="#e362f8"/></linearGradient><linearGradient id="fluentColorPeopleSync242" x1="15.427" x2="18.485" y1="5.798" y2="10.68" gradientUnits="userSpaceOnUse"><stop offset=".125" stop-color="#9c6cfe"/><stop offset="1" stop-color="#7a41dc"/></linearGradient><linearGradient id="fluentColorPeopleSync243" x1="5.902" x2="9.98" y1="4.063" y2="10.574" gradientUnits="userSpaceOnUse"><stop offset=".125" stop-color="#bd96ff"/><stop offset="1" stop-color="#9c6cfe"/></linearGradient><linearGradient id="fluentColorPeopleSync244" x1="12.393" x2="19.984" y1="14.063" y2="21.95" gradientUnits="userSpaceOnUse"><stop stop-color="#52d17c"/><stop offset="1" stop-color="#22918b"/></linearGradient><linearGradient id="fluentColorPeopleSync245" x1="14" x2="18.443" y1="13.417" y2="21.445" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#e3ffd9"/></linearGradient><radialGradient id="fluentColorPeopleSync246" cx="0" cy="0" r="1" gradientTransform="matrix(2.22857 5.125 -5.05993 2.20028 15.2 17.375)" gradientUnits="userSpaceOnUse"><stop stop-color="#30116e"/><stop offset=".268" stop-color="#30116e" stop-opacity="0.812"/><stop offset="1" stop-color="#30116e" stop-opacity="0"/></radialGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_temp_name_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><g fill="none"><path fill="url(#fluentColorPeopleCommunity200)" d="M13.062 9.49a1.5 1.5 0 0 0-1.837 1.06l-.647 2.415a4 4 0 0 0 7.727 2.07l.647-2.414a1.5 1.5 0 0 0-1.06-1.837z"/><path fill="url(#fluentColorPeopleCommunity201)" d="M6.942 9.49a1.5 1.5 0 0 1 1.837 1.06l.647 2.415a4 4 0 1 1-7.727 2.07l-.648-2.414a1.5 1.5 0 0 1 1.061-1.837z"/><path fill="url(#fluentColorPeopleCommunity202)" d="M10 2a3 3 0 1 0 0 6a3 3 0 0 0 0-6"/><path fill="url(#fluentColorPeopleCommunity203)" d="M16.5 4a2.5 2.5 0 1 0 0 5a2.5 2.5 0 0 0 0-5"/><path fill="url(#fluentColorPeopleCommunity204)" d="M3.5 4a2.5 2.5 0 1 0 0 5a2.5 2.5 0 0 0 0-5"/><path fill="url(#fluentColorPeopleCommunity205)" d="M7.5 9A1.5 1.5 0 0 0 6 10.5V14a4 4 0 0 0 8 0v-3.5A1.5 1.5 0 0 0 12.5 9z"/><defs><radialGradient id="fluentColorPeopleCommunity200" cx="0" cy="0" r="1" gradientTransform="rotate(78.837 -.528 14.039)scale(6.28119)" gradientUnits="userSpaceOnUse"><stop stop-color="#0078d4"/><stop offset="1" stop-color="#004695"/></radialGradient><radialGradient id="fluentColorPeopleCommunity201" cx="0" cy="0" r="1" gradientTransform="rotate(61.056 -6.793 7.547)scale(9.32732 6.71383)" gradientUnits="userSpaceOnUse"><stop stop-color="#008ce2"/><stop offset="1" stop-color="#0068c6"/></radialGradient><radialGradient id="fluentColorPeopleCommunity202" cx="0" cy="0" r="1" gradientTransform="rotate(59.931 1.15 10.225)scale(3.74767)" gradientUnits="userSpaceOnUse"><stop offset=".339" stop-color="#3dcbff"/><stop offset="1" stop-color="#14b1ff"/></radialGradient><radialGradient id="fluentColorPeopleCommunity203" cx="0" cy="0" r="1" gradientTransform="rotate(78.837 4.563 12.25)scale(3.66754)" gradientUnits="userSpaceOnUse"><stop stop-color="#0078d4"/><stop offset="1" stop-color="#004695"/></radialGradient><radialGradient id="fluentColorPeopleCommunity204" cx="0" cy="0" r="1" gradientTransform="rotate(47.573 -5.033 5.534)scale(4.09974)" gradientUnits="userSpaceOnUse"><stop stop-color="#008ce2"/><stop offset="1" stop-color="#0068c6"/></radialGradient><radialGradient id="fluentColorPeopleCommunity205" cx="0" cy="0" r="1" gradientTransform="matrix(2.50362 4.865 -4.56496 2.34921 9.23 12.16)" gradientUnits="userSpaceOnUse"><stop offset=".339" stop-color="#3dcbff"/><stop offset="1" stop-color="#14b1ff"/></radialGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_explorer_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48"><g fill="#616161"><path d="m29.175 31.99l2.828-2.827l12.019 12.019l-2.828 2.827z"/><circle cx="20" cy="20" r="16"/></g><path fill="#37474f" d="m32.45 35.34l2.827-2.828l8.696 8.696l-2.828 2.828z"/><circle cx="20" cy="20" r="13" fill="#64b5f6"/><path fill="#bbdefb" d="M26.9 14.2c-1.7-2-4.2-3.2-6.9-3.2s-5.2 1.2-6.9 3.2c-.4.4-.3 1.1.1 1.4c.4.4 1.1.3 1.4-.1C16 13.9 17.9 13 20 13s4 .9 5.4 2.5c.2.2.5.4.8.4c.2 0 .5-.1.6-.2c.4-.4.4-1.1.1-1.5"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_format_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48"><path fill="#ff4848" fill-rule="evenodd" d="M16.336 5.188A8 8 0 0 1 23.37 1h1.26a8 8 0 0 1 7.034 4.188c3.28.067 5.861.169 7.744.264c2.67.134 5.364 1.58 6.188 4.41q.161.551.294 1.168c.54 2.524-1.377 4.585-3.751 4.785c-3.056.259-8.613.55-18.162.55c-9.547 0-15.104-.291-18.16-.55c-2.356-.199-4.338-2.273-3.658-4.821c.188-.707.42-1.35.663-1.92C3.83 6.713 6.21 5.598 8.522 5.47c1.813-.1 4.403-.21 7.814-.282M5.564 18.805a7 7 0 0 1-.695-.095c.821 12.048 1.592 18.537 2.07 21.774c.332 2.251 1.85 4.217 4.226 4.788c2.445.587 6.55 1.227 12.837 1.227c6.286 0 10.392-.64 12.837-1.227c2.375-.571 3.894-2.537 4.226-4.788c.478-3.237 1.249-9.73 2.07-21.782a7 7 0 0 1-.743.103c-3.165.267-8.811.56-18.415.56c-9.602 0-15.248-.293-18.413-.56M19.99 26.8a2 2 0 1 0-3.98.398l1 10a2 2 0 1 0 3.98-.398zm10.21-1.79a2 2 0 0 0-2.19 1.79l-1 10a2 2 0 1 0 3.98.399l1-10a2 2 0 0 0-1.79-2.19" clip-rule="evenodd"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_repair_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g fill="none"><path fill="url(#fluentColorWrenchScrewdriver320)" d="M22 3a1 1 0 0 0-.928.629l-1 2.5a1 1 0 0 0 0 .742L21 9.193V16h5V9.193l.928-2.322a1 1 0 0 0 0-.742l-1-2.5A1 1 0 0 0 25 3z"/><path fill="url(#fluentColorWrenchScrewdriver321)" d="M18 19.5v4a5.5 5.5 0 1 0 11 0v-4z"/><path fill="url(#fluentColorWrenchScrewdriver322)" d="M28 16a1 1 0 0 1 1 1v3H18v-3a1 1 0 0 1 1-1z"/><path fill="url(#fluentColorWrenchScrewdriver323)" d="M8.577 3.525A1 1 0 0 1 9 4.341V9a1 1 0 0 0 2 0V4.341a1 1 0 0 1 1.333-.942A7.002 7.002 0 0 1 13 16.326V26a3 3 0 1 1-6 0v-9.674A7.002 7.002 0 0 1 7.667 3.4a1 1 0 0 1 .91.126"/><defs><linearGradient id="fluentColorWrenchScrewdriver320" x1="25.833" x2="21.762" y1=".523" y2="26.082" gradientUnits="userSpaceOnUse"><stop stop-color="#2bdabe"/><stop offset=".853" stop-color="#0067bf"/></linearGradient><linearGradient id="fluentColorWrenchScrewdriver321" x1="28.998" x2="18.789" y1="29.212" y2="18.534" gradientUnits="userSpaceOnUse"><stop stop-color="#ff6f47"/><stop offset="1" stop-color="#ffcd0f"/></linearGradient><linearGradient id="fluentColorWrenchScrewdriver322" x1="21.028" x2="22.269" y1="16.968" y2="20.384" gradientUnits="userSpaceOnUse"><stop stop-color="#ffa43d"/><stop offset="1" stop-color="#fb5937"/></linearGradient><linearGradient id="fluentColorWrenchScrewdriver323" x1="14.667" x2="6.062" y1="-1.548" y2="29.634" gradientUnits="userSpaceOnUse"><stop stop-color="#2bdabe"/><stop offset="1" stop-color="#0067bf"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_carpeta(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="#f3c514" d="M1 4.5A2.5 2.5 0 0 1 3.5 2h2.086a1.5 1.5 0 0 1 1.06.44L8 3.792l-2.06 2.06A.5.5 0 0 1 5.585 6H1zM1 7v4.5A2.5 2.5 0 0 0 3.5 14h9a2.5 2.5 0 0 0 2.5-2.5v-5A2.5 2.5 0 0 0 12.5 4H9.207l-2.56 2.56A1.5 1.5 0 0 1 5.585 7z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def nueva_carpeta(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
    <path fill="#f3c514" d="M1 4.5A2.5 2.5 0 0 1 3.5 2h2.086a1.5 1.5 0 0 1 1.06.44L8 3.792l-2.06 2.06A.5.5 0 0 1 5.585 6H1zM1 7v4.5A2.5 2.5 0 0 0 3.5 14h3.1A5.5 5.5 0 0 1 15 7.257V6.5A2.5 2.5 0 0 0 12.5 4H9.207l-2.56 2.56A1.5 1.5 0 0 1 5.585 7z"/>
    <circle fill="#28a745" cx="11.5" cy="11.5" r="4.5"/>
    <path fill="black" d="M12.1 9.6a.5.5 0 0 0-1 0V11.1H9.6a.5.5 0 0 0 0 1H11.1v1.5a.5.5 0 0 0 1 0V12.1h1.5a.5.5 0 0 0 0-1H12.1z"/>
    <path fill="white" d="M12 9.5a.5.5 0 0 0-1 0V11H9.5a.5.5 0 0 0 0 1H11v1.5a.5.5 0 0 0 1 0V12h1.5a.5.5 0 0 0 0-1H12z"/>
    </svg>'''
    return crear_icono_svg(svg_data, size)

def create_refresh_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path fill="url(#fluentColorApprovalsApp160)" fill-rule="evenodd" d="M8.571.236a.806.806 0 0 0-1.14 1.14l.73.728H8a6.448 6.448 0 1 0 6.449 6.448a.806.806 0 1 0-1.612 0a4.837 4.837 0 1 1-4.836-4.837h.248l-.818.818a.806.806 0 1 0 1.14 1.14l2.148-2.149a.806.806 0 0 0 0-1.14z" clip-rule="evenodd"/><path fill="url(#fluentColorApprovalsApp161)" fill-rule="evenodd" d="M11.61 5.812a.806.806 0 0 1 0 1.14l-3.472 3.471a.806.806 0 0 1-1.14 0L5.696 9.121a.806.806 0 1 1 1.14-1.14l.732.733l2.902-2.902a.806.806 0 0 1 1.14 0" clip-rule="evenodd"/><defs><linearGradient id="fluentColorApprovalsApp160" x1="1.554" x2="5.941" y1="1.231" y2="17.422" gradientUnits="userSpaceOnUse"><stop stop-color="#0fafff"/><stop offset="1" stop-color="#0067bf"/></linearGradient><linearGradient id="fluentColorApprovalsApp161" x1="10.891" x2="4.999" y1="6.555" y2="9.484" gradientUnits="userSpaceOnUse"><stop stop-color="#42b870"/><stop offset="1" stop-color="#309c61"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_pdf_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#f25252" d="M18.97 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h13.97c1.1 0 2-.9 2-2V5a2 2 0 0 0-2-2m-1.59 13.98c-.03.01-.07.02-.1.02h-2.26a.49.49 0 0 1-.46-.3l-2.46-5.74c-.02-.06-.08-.09-.13-.07a.12.12 0 0 0-.07.07l-1.53 3.65c-.03.07 0 .14.07.17c.***********.05.01h1.68c.1 0 .2.06.24.16l.74 1.64c.07.15-.01.33-.16.4c-.06 0-.1.01-.14.01H6.73c-.15 0-.28-.13-.28-.28c0-.04.01-.07.02-.11l3.9-9.28c.08-.2.28-.33.49-.33h2.25c.22 0 .41.13.49.33l3.92 9.28c.07.14.01.31-.14.37"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_android_app_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56"><path fill="#46ac00" d="M42.172 19.581v20.637a3.434 3.434 0 0 1-3.253 3.437l-.189.005h-2.283v7.04a3.17 3.17 0 0 1-3.01 3.188l-.183.005a3.17 3.17 0 0 1-3.187-3.01l-.005-.183l-.001-7.04h-4.253v7.04a3.17 3.17 0 0 1-3.01 3.188l-.183.005a3.17 3.17 0 0 1-3.187-3.01l-.005-.183v-7.04H17.14a3.434 3.434 0 0 1-3.437-3.252l-.005-.19V19.581zm-32.855-.547a3.173 3.173 0 0 1 3.188 3.01l.005.182v13.299a3.174 3.174 0 0 1-3.193 3.192a3.173 3.173 0 0 1-3.187-3.01l-.005-.182V22.226a3.174 3.174 0 0 1 3.192-3.192m37.236 0a3.173 3.173 0 0 1 3.187 3.01l.005.182v13.299a3.174 3.174 0 0 1-3.192 3.192a3.173 3.173 0 0 1-3.188-3.01l-.005-.182V22.226a3.174 3.174 0 0 1 3.193-3.192M36.774 2.279q.113 0 .215.058a.44.44 0 0 1 .211.54l-.035.076l-2.249 4.063c4.231 2.202 7.12 6.341 7.251 11.114l.005.327H13.698c.005-4.802 2.8-8.999 6.969-11.288l.286-.153l-2.249-4.063a.44.44 0 0 1 .176-.616a.444.444 0 0 1 .568.105l.048.071l2.279 4.107a15.2 15.2 0 0 1 6.16-1.29c2.048 0 3.997.4 5.757 1.119l.403.171l2.278-4.107a.45.45 0 0 1 .401-.234m-15.41 8.59c-.65 0-1.188.538-1.188 1.188s.538 1.188 1.188 1.188s1.188-.538 1.188-1.188s-.538-1.188-1.188-1.188m13.142 0c-.65 0-1.188.538-1.188 1.188s.538 1.188 1.188 1.188s1.188-.538 1.188-1.188s-.538-1.188-1.188-1.188"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_ios_app_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><defs><path id="biApple0" d="M11.182.008C11.148-.03 9.923.023 8.857 1.18c-1.066 1.156-.902 2.482-.878 2.516s1.52.087 2.475-1.258s.762-2.391.728-2.43m3.314 11.733c-.048-.096-2.325-1.234-2.113-3.422s1.675-2.789 1.698-2.854s-.597-.79-1.254-1.157a3.7 3.7 0 0 0-1.563-.434c-.108-.003-.483-.095-1.254.116c-.508.139-1.653.589-1.968.607c-.316.018-1.256-.522-2.267-.665c-.647-.125-1.333.131-1.824.328c-.49.196-1.422.754-2.074 2.237c-.652 1.482-.311 3.83-.067 4.56s.625 1.924 1.273 2.796c.576.984 1.34 1.667 1.659 1.899s1.219.386 1.843.067c.502-.308 1.408-.485 1.766-.472c.357.013 1.061.154 1.782.539c.571.197 1.111.115 1.652-.105c.541-.221 1.324-1.059 2.238-2.758q.52-1.185.473-1.282"/></defs><g fill="#fff"><use href="#biApple0"/><use href="#biApple0"/></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_compressed_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48"><g fill="none"><path fill="#ff5656" d="M9.646 2.191C12.525 1.855 17.181 1.5 24 1.5c1.985 0 3.787.03 5.415.081a1.5 1.5 0 0 1 .398.067c1.147.357 4.228 1.633 8.539 5.837c3.642 3.551 5.145 6.251 5.748 7.689c-.57.16-1.13.44-1.648.863c-1.104.899-2.71 2.412-3.809 3.654a6.47 6.47 0 0 0 0 8.618c1.098 1.243 2.705 2.756 3.809 3.655a4.5 4.5 0 0 0 1.872.92c-.142 3.29-.35 5.741-.55 7.5c-.33 2.899-2.532 5.088-5.42 5.425c-2.88.336-7.535.691-14.354.691s-11.475-.355-14.354-.691c-2.888-.337-5.09-2.526-5.42-5.425c-.2-1.759-.408-4.21-.55-7.5a4.5 4.5 0 0 0 1.872-.92c1.104-.9 2.71-2.412 3.809-3.655a6.47 6.47 0 0 0 0-8.618c-1.098-1.242-2.705-2.755-3.81-3.654a4.5 4.5 0 0 0-1.871-.92c.142-3.291.35-5.742.55-7.5c.33-2.9 2.532-5.089 5.42-5.426"/><path fill="#fff" fill-rule="evenodd" d="M16 16a2 2 0 1 1 0-4h8a2 2 0 1 1 0 4zm0 10a2 2 0 1 1 0-4h16a2 2 0 1 1 0 4zm0 10a2 2 0 1 1 0-4h16a2 2 0 1 1 0 4z" clip-rule="evenodd"/><path fill="#fff" d="M3.654 29.637a1.59 1.59 0 0 1-1.732.19A1.72 1.72 0 0 1 1 28.285v-8.572c0-.655.358-1.253.922-1.54a1.59 1.59 0 0 1 1.732.189c.987.803 2.473 2.204 3.455 3.315a3.47 3.47 0 0 1 0 4.644a34 34 0 0 1-3.455 3.315Zm40.692 0a1.59 1.59 0 0 0 1.732.19c.564-.288.922-.886.922-1.541v-8.572c0-.655-.358-1.253-.922-1.54a1.59 1.59 0 0 0-1.732.189c-.987.803-2.473 2.204-3.455 3.315a3.47 3.47 0 0 0 0 4.644a34 34 0 0 0 3.455 3.315"/></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_image_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><circle cx="7.5" cy="6.5" r="4.5" fill="url(#fluentColorDesignIdeas160)"/><circle cx="7.5" cy="6.5" r="4.5" fill="url(#fluentColorDesignIdeas161)" fill-opacity="0.6"/><circle cx="7.5" cy="6.5" r="4.5" fill="url(#fluentColorDesignIdeas162)" fill-opacity="0.6"/><rect width="8" height="8" x="7" y="6" fill="url(#fluentColorDesignIdeas163)" rx="2"/><path fill="url(#fluentColorDesignIdeas167)" fill-rule="evenodd" d="M7.5 11a4.5 4.5 0 0 0 4.473-5H9a2 2 0 0 0-2 2v2.973q.*************" clip-rule="evenodd"/><path fill="url(#fluentColorDesignIdeas168)" fill-rule="evenodd" d="M7.5 11a4.5 4.5 0 0 0 4.473-5H9a2 2 0 0 0-2 2v2.973q.*************" clip-rule="evenodd"/><path fill="url(#fluentColorDesignIdeas169)" d="M5 9.718c0 3.45-1.062 5.25-2 5.25c-.812 0-2-1.494-2-5.25C1 6.51 1.895 6 3 6c.984 0 2 .268 2 3.718"/><path fill="url(#fluentColorDesignIdeas164)" fill-opacity="0.5" d="M5 9.718c0 3.45-1.062 5.25-2 5.25c-.812 0-2-1.494-2-5.25C1 6.51 1.895 6 3 6c.984 0 2 .268 2 3.718"/><path fill="url(#fluentColorDesignIdeas16a)" d="M5 9.718c0 3.45-1.062 5.25-2 5.25c-.812 0-2-1.494-2-5.25C1 6.51 1.895 6 3 6c.984 0 2 .268 2 3.718"/><path fill="url(#fluentColorDesignIdeas165)" fill-opacity="0.7" d="M5 9.718c0 3.45-1.062 5.25-2 5.25c-.812 0-2-1.494-2-5.25C1 6.51 1.895 6 3 6c.984 0 2 .268 2 3.718"/><path fill="#fff" fill-opacity="0.15" d="M1.046 8.577C1.242 6.384 2.042 6 3 6c.863 0 1.75.206 1.955 2.577a3.95 3.95 0 0 1-1.954.502a3.95 3.95 0 0 1-1.955-.502"/><path fill="url(#fluentColorDesignIdeas166)" d="M3.58 1.125a.5.5 0 0 1 .124.585a.3.3 0 0 0-.017.103c0 .***************.333c.***************.36.342c.13.124.267.27.39.453c.255.383.396.862.396 1.559C5 5.97 4.023 7 3 7S1 5.97 1 4.5c0-.72.302-1.507.689-2.106c.231-.39.556-.717.808-.937q.227-.201.476-.373a.5.5 0 0 1 .608.041"/><defs><radialGradient id="fluentColorDesignIdeas160" cx="0" cy="0" r="1" gradientTransform="matrix(2.45191 -11.92575 11.92566 2.45189 3.836 13.201)" gradientUnits="userSpaceOnUse"><stop offset=".222" stop-color="#4e46e2"/><stop offset=".578" stop-color="#625df6"/><stop offset=".955" stop-color="#e37dff"/></radialGradient><radialGradient id="fluentColorDesignIdeas161" cx="0" cy="0" r="1" gradientTransform="matrix(4.69314 -1.46004 1.91118 6.14327 1.818 8.863)" gradientUnits="userSpaceOnUse"><stop offset=".566" stop-color="#251fba"/><stop offset="1" stop-color="#5e51e4" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorDesignIdeas162" cx="0" cy="0" r="1" gradientTransform="matrix(4.47427 .64949 -.6639 4.57357 1.481 5.525)" gradientUnits="userSpaceOnUse"><stop offset=".566" stop-color="#251fba"/><stop offset="1" stop-color="#5e51e4" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorDesignIdeas163" cx="0" cy="0" r="1" gradientTransform="rotate(42.462 -4.446 13.61)scale(9.45903 12.8479)" gradientUnits="userSpaceOnUse"><stop stop-color="#c354ff"/><stop offset=".158" stop-color="#b339f0"/><stop offset=".429" stop-color="#f24a9d"/><stop offset=".749" stop-color="#ff835c"/><stop offset="1" stop-color="#ffc470"/></radialGradient><radialGradient id="fluentColorDesignIdeas164" cx="0" cy="0" r="1" gradientTransform="matrix(1.51033 8.80711 -24.12887 4.13786 2.833 5.44)" gradientUnits="userSpaceOnUse"><stop offset=".5" stop-color="#dd3ce2" stop-opacity="0"/><stop offset="1" stop-color="#dd3ce2"/></radialGradient><radialGradient id="fluentColorDesignIdeas165" cx="0" cy="0" r="1" gradientTransform="matrix(1.34572 -4.41924 7.80502 2.37673 1.838 6)" gradientUnits="userSpaceOnUse"><stop offset=".169" stop-color="#02888d"/><stop offset=".26" stop-color="#02888d" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorDesignIdeas166" cx="0" cy="0" r="1" gradientTransform="rotate(106.553 .82 2.916)scale(4.56577 4.56847)" gradientUnits="userSpaceOnUse"><stop stop-color="#ff9532"/><stop offset=".251" stop-color="#ff835c"/><stop offset="1" stop-color="#f24a9d"/></radialGradient><linearGradient id="fluentColorDesignIdeas167" x1="11.592" x2="7.523" y1="11.031" y2="5.051" gradientUnits="userSpaceOnUse"><stop offset=".195" stop-color="#6d37cd"/><stop offset=".765" stop-color="#ea71ef"/></linearGradient><linearGradient id="fluentColorDesignIdeas168" x1="12.453" x2="5.964" y1="12.438" y2="3.367" gradientUnits="userSpaceOnUse"><stop offset=".195" stop-color="#7631ff"/><stop offset=".886" stop-color="#e63080"/></linearGradient><linearGradient id="fluentColorDesignIdeas169" x1=".063" x2="4.05" y1="9.807" y2="14.499" gradientUnits="userSpaceOnUse"><stop stop-color="#0fafff"/><stop offset="1" stop-color="#2764e7"/></linearGradient><linearGradient id="fluentColorDesignIdeas16a" x1="6.5" x2="-.5" y1="10.108" y2="10.108" gradientUnits="userSpaceOnUse"><stop offset=".307" stop-color="#0d91e1"/><stop offset=".761" stop-color="#52b471"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_excel_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><defs><linearGradient id="vscodeIconsFileTypeExcel0" x1="4.494" x2="13.832" y1="-2092.086" y2="-2075.914" gradientTransform="translate(0 2100)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#18884f"/><stop offset=".5" stop-color="#117e43"/><stop offset="1" stop-color="#0b6631"/></linearGradient></defs><path fill="#185c37" d="M19.581 15.35L8.512 13.4v14.409A1.19 1.19 0 0 0 9.705 29h19.1A1.19 1.19 0 0 0 30 27.809V22.5Z"/><path fill="#21a366" d="M19.581 3H9.705a1.19 1.19 0 0 0-1.193 1.191V9.5L19.581 16l5.861 1.95L30 16V9.5Z"/><path fill="#107c41" d="M8.512 9.5h11.069V16H8.512Z"/><path d="M16.434 8.2H8.512v16.25h7.922a1.2 1.2 0 0 0 1.194-1.191V9.391A1.2 1.2 0 0 0 16.434 8.2" opacity="0.1"/><path d="M15.783 8.85H8.512V25.1h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191" opacity="0.2"/><path d="M15.783 8.85H8.512V23.8h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191" opacity="0.2"/><path d="M15.132 8.85h-6.62V23.8h6.62a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191" opacity="0.2"/><path fill="url(#vscodeIconsFileTypeExcel0)" d="M3.194 8.85h11.938a1.193 1.193 0 0 1 1.194 1.191v11.918a1.193 1.193 0 0 1-1.194 1.191H3.194A1.19 1.19 0 0 1 2 21.959V10.041A1.19 1.19 0 0 1 3.194 8.85"/><path fill="#fff" d="m5.7 19.873l2.511-3.884l-2.3-3.862h1.847L9.013 14.6c.116.234.2.408.238.524h.017q.123-.281.26-.546l1.342-2.447h1.7l-2.359 3.84l2.419 3.905h-1.809l-1.45-2.711A2.4 2.4 0 0 1 9.2 16.8h-.024a1.7 1.7 0 0 1-.168.351l-1.493 2.722Z"/><path fill="#33c481" d="M28.806 3h-9.225v6.5H30V4.191A1.19 1.19 0 0 0 28.806 3"/><path fill="#107c41" d="M19.581 16H30v6.5H19.581Z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_exe_icon(size=40):
    """
    Crea un icono para archivos ejecutables basado en el SVG proporcionado,
    con el diseño de documento y las letras 'EXE' en color naranja
    
    Args:
        size: Tamaño del icono en píxeles
    
    Returns:
        QIcon: Icono de archivo ejecutable con proporciones precisas
    """
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter()
    
    try:
        if not painter.begin(pixmap):
            return QIcon()
        
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Factor de escala basado en el tamaño original del SVG (16x16)
        scale_factor = size / 16.0
        
        # Color naranja para el icono
        orange_color = QColor("#f29200")
        
        # Crear el path para el documento
        doc_path = QPainterPath()
        
        # Parte principal del documento
        doc_path.moveTo(14 * scale_factor, 4.5 * scale_factor)
        doc_path.lineTo(14 * scale_factor, 14 * scale_factor)
        doc_path.cubicTo(
            14 * scale_factor, 15.1 * scale_factor,
            13.1 * scale_factor, 16 * scale_factor,
            12 * scale_factor, 16 * scale_factor
        )
        doc_path.lineTo(11 * scale_factor, 16 * scale_factor)
        doc_path.lineTo(11 * scale_factor, 15 * scale_factor)
        doc_path.lineTo(12 * scale_factor, 15 * scale_factor)
        doc_path.cubicTo(
            12.55 * scale_factor, 15 * scale_factor,
            13 * scale_factor, 14.55 * scale_factor,
            13 * scale_factor, 14 * scale_factor
        )
        doc_path.lineTo(13 * scale_factor, 4.5 * scale_factor)
        doc_path.lineTo(11 * scale_factor, 4.5 * scale_factor)
        doc_path.cubicTo(
            10.17 * scale_factor, 4.5 * scale_factor,
            9.5 * scale_factor, 3.83 * scale_factor,
            9.5 * scale_factor, 3 * scale_factor
        )
        doc_path.lineTo(9.5 * scale_factor, 1 * scale_factor)
        doc_path.lineTo(4 * scale_factor, 1 * scale_factor)
        doc_path.cubicTo(
            3.45 * scale_factor, 1 * scale_factor,
            3 * scale_factor, 1.45 * scale_factor,
            3 * scale_factor, 2 * scale_factor
        )
        doc_path.lineTo(3 * scale_factor, 11 * scale_factor)
        doc_path.lineTo(2 * scale_factor, 11 * scale_factor)
        doc_path.lineTo(2 * scale_factor, 2 * scale_factor)
        doc_path.cubicTo(
            2 * scale_factor, 0.9 * scale_factor,
            2.9 * scale_factor, 0 * scale_factor,
            4 * scale_factor, 0 * scale_factor
        )
        doc_path.lineTo(9.5 * scale_factor, 0 * scale_factor)
        doc_path.closeSubpath()
        
        # Dibujar el documento
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(orange_color)
        painter.drawPath(doc_path)
        
        # Crear el path para la primera E
        e1_path = QPainterPath()
        e1_path.moveTo(2.575 * scale_factor, 15.202 * scale_factor)
        e1_path.lineTo(0.785 * scale_factor, 15.202 * scale_factor)
        e1_path.lineTo(0.785 * scale_factor, 14.129 * scale_factor)
        e1_path.lineTo(2.47 * scale_factor, 14.129 * scale_factor)
        e1_path.lineTo(2.47 * scale_factor, 13.523 * scale_factor)
        e1_path.lineTo(0.785 * scale_factor, 13.523 * scale_factor)
        e1_path.lineTo(0.785 * scale_factor, 12.498 * scale_factor)
        e1_path.lineTo(2.575 * scale_factor, 12.498 * scale_factor)
        e1_path.lineTo(2.575 * scale_factor, 11.85 * scale_factor)
        e1_path.lineTo(0 * scale_factor, 11.85 * scale_factor)
        e1_path.lineTo(0 * scale_factor, 15.849 * scale_factor)
        e1_path.lineTo(2.575 * scale_factor, 15.849 * scale_factor)
        e1_path.closeSubpath()
        
        # Dibujar la primera E
        painter.drawPath(e1_path)
        
        # Crear el path para la X
        x_path = QPainterPath()
        x_path.moveTo(6.31 * scale_factor, 11.85 * scale_factor)
        x_path.lineTo(5.417 * scale_factor, 11.85 * scale_factor)
        x_path.lineTo(4.594 * scale_factor, 13.289 * scale_factor)
        x_path.lineTo(4.558 * scale_factor, 13.289 * scale_factor)
        x_path.lineTo(3.726 * scale_factor, 11.85 * scale_factor)
        x_path.lineTo(2.795 * scale_factor, 11.85 * scale_factor)
        x_path.lineTo(4.022 * scale_factor, 13.833 * scale_factor)
        x_path.lineTo(2.783 * scale_factor, 15.849 * scale_factor)
        x_path.lineTo(3.644 * scale_factor, 15.849 * scale_factor)
        x_path.lineTo(4.497 * scale_factor, 14.434 * scale_factor)
        x_path.lineTo(4.532 * scale_factor, 14.434 * scale_factor)
        x_path.lineTo(5.382 * scale_factor, 15.849 * scale_factor)
        x_path.lineTo(6.29 * scale_factor, 15.849 * scale_factor)
        x_path.lineTo(5.036 * scale_factor, 13.857 * scale_factor)
        x_path.closeSubpath()
        
        # Dibujar la X
        painter.drawPath(x_path)
        
        # Crear el path para la segunda E
        e2_path = QPainterPath()
        e2_path.moveTo(7.335 * scale_factor, 15.202 * scale_factor)
        e2_path.lineTo(9.125 * scale_factor, 15.202 * scale_factor)
        e2_path.lineTo(9.125 * scale_factor, 15.849 * scale_factor)
        e2_path.lineTo(6.548 * scale_factor, 15.849 * scale_factor)
        e2_path.lineTo(6.548 * scale_factor, 11.85 * scale_factor)
        e2_path.lineTo(9.124 * scale_factor, 11.85 * scale_factor)
        e2_path.lineTo(9.124 * scale_factor, 12.498 * scale_factor)
        e2_path.lineTo(7.334 * scale_factor, 12.498 * scale_factor)
        e2_path.lineTo(7.334 * scale_factor, 13.523 * scale_factor)
        e2_path.lineTo(9.018 * scale_factor, 13.523 * scale_factor)
        e2_path.lineTo(9.018 * scale_factor, 14.129 * scale_factor)
        e2_path.lineTo(7.334 * scale_factor, 14.129 * scale_factor)
        e2_path.closeSubpath()
        
        # Dibujar la segunda E
        painter.drawPath(e2_path)
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def create_word_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><defs><linearGradient id="vscodeIconsFileTypeWord0" x1="4.494" x2="13.832" y1="-1712.086" y2="-1695.914" gradientTransform="translate(0 1720)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2368c4"/><stop offset=".5" stop-color="#1a5dbe"/><stop offset="1" stop-color="#1146ac"/></linearGradient></defs><path fill="#41a5ee" d="M28.806 3H9.705a1.19 1.19 0 0 0-1.193 1.191V9.5l11.069 3.25L30 9.5V4.191A1.19 1.19 0 0 0 28.806 3"/><path fill="#2b7cd3" d="M30 9.5H8.512V16l11.069 1.95L30 16Z"/><path fill="#185abd" d="M8.512 16v6.5l10.418 1.3L30 22.5V16Z"/><path fill="#103f91" d="M9.705 29h19.1A1.19 1.19 0 0 0 30 27.809V22.5H8.512v5.309A1.19 1.19 0 0 0 9.705 29"/><path d="M16.434 8.2H8.512v16.25h7.922a1.2 1.2 0 0 0 1.194-1.191V9.391A1.2 1.2 0 0 0 16.434 8.2" opacity="0.1"/><path d="M15.783 8.85H8.512V25.1h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191" opacity="0.2"/><path d="M15.783 8.85H8.512V23.8h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191" opacity="0.2"/><path d="M15.132 8.85h-6.62V23.8h6.62a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191" opacity="0.2"/><path fill="url(#vscodeIconsFileTypeWord0)" d="M3.194 8.85h11.938a1.193 1.193 0 0 1 1.194 1.191v11.918a1.193 1.193 0 0 1-1.194 1.191H3.194A1.19 1.19 0 0 1 2 21.959V10.041A1.19 1.19 0 0 1 3.194 8.85"/><path fill="#fff" d="M6.9 17.988q.035.276.046.481h.028q.015-.195.065-.47c.05-.275.062-.338.089-.465l1.255-5.407h1.624l1.3 5.326a8 8 0 0 1 .162 1h.022a8 8 0 0 1 .135-.975l1.039-5.358h1.477l-1.824 7.748h-1.727l-1.237-5.126q-.054-.222-.122-.578t-.084-.52h-.021q-.021.189-.084.561t-.1.552L7.78 19.871H6.024L4.19 12.127h1.5l1.131 5.418a5 5 0 0 1 .079.443"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_audio_icon(size=40):
    """
    Crea un icono de audio basado en el SVG proporcionado,
    con una nota musical y ondas de sonido en color azul
    
    Args:
        size: Tamaño del icono en píxeles
    
    Returns:
        QIcon: Icono de audio con proporciones precisas
    """
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter()
    
    try:
        if not painter.begin(pixmap):
            return QIcon()
        
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Factor de escala basado en el tamaño original del SVG (64x64)
        scale_factor = size / 64.0
        
        # Color principal azul
        main_color = QColor("#27aae1")  # Azul claro
        highlight_color = QColor("#e7e6e6")  # Gris claro para las ondas
        
        # Crear el path para la nota musical
        path = QPainterPath()
        
        # Parte principal de la nota musical
        path.moveTo(55.14 * scale_factor, 20.8 * scale_factor)
        path.lineTo(55.23 * scale_factor, 37.564 * scale_factor)
        
        # Curva inferior izquierda
        path.cubicTo(
            49.916 * scale_factor, 33.74 * scale_factor,
            40.09 * scale_factor, 34.713 * scale_factor,
            37.661 * scale_factor, 41.273 * scale_factor
        )
        
        # Parte inferior de la nota
        path.cubicTo(
            36.131 * scale_factor, 45.404 * scale_factor,
            37.198 * scale_factor, 50.16 * scale_factor,
            40.456 * scale_factor, 53.08 * scale_factor
        )
        
        # Curva inferior derecha
        path.cubicTo(
            43.924 * scale_factor, 56.191 * scale_factor,
            49.401 * scale_factor, 56.133 * scale_factor,
            53.566 * scale_factor, 54.765 * scale_factor
        )
        
        # Parte derecha de la nota
        path.cubicTo(
            57.477 * scale_factor, 53.479 * scale_factor,
            60.648 * scale_factor, 50.046 * scale_factor,
            61.245 * scale_factor, 45.963 * scale_factor
        )
        
        # Completar la parte superior
        path.cubicTo(
            61.289 * scale_factor, 45.682 * scale_factor,
            61.301 * scale_factor, 45.393 * scale_factor,
            61.324 * scale_factor, 45.103 * scale_factor
        )
        
        # Línea superior
        path.lineTo(61.323 * scale_factor, 0.013 * scale_factor)
        
        # Línea izquierda (palito de la nota)
        path.lineTo(21.784 * scale_factor, 10.439 * scale_factor)
        
        # Parte inferior izquierda
        path.cubicTo(
            21.599 * scale_factor, 10.5 * scale_factor,
            21.417 * scale_factor, 10.6 * scale_factor,
            21.417 * scale_factor, 11.141 * scale_factor
        )
        
        # Línea vertical izquierda
        path.lineTo(21.511 * scale_factor, 45.877 * scale_factor)
        
        # Curva inferior izquierda (segunda nota)
        path.cubicTo(
            16.198 * scale_factor, 42.052 * scale_factor,
            6.371 * scale_factor, 43.024 * scale_factor,
            3.938 * scale_factor, 49.583 * scale_factor
        )
        
        # Parte inferior de la segunda nota
        path.cubicTo(
            2.407 * scale_factor, 53.717 * scale_factor,
            3.477 * scale_factor, 58.47 * scale_factor,
            6.737 * scale_factor, 61.393 * scale_factor
        )
        
        # Curva inferior derecha (segunda nota)
        path.cubicTo(
            10.204 * scale_factor, 64.504 * scale_factor,
            15.681 * scale_factor, 64.444 * scale_factor,
            19.847 * scale_factor, 63.077 * scale_factor
        )
        
        # Parte derecha de la segunda nota
        path.cubicTo(
            23.759 * scale_factor, 61.791 * scale_factor,
            26.925 * scale_factor, 58.357 * scale_factor,
            27.527 * scale_factor, 54.275 * scale_factor
        )
        
        # Completar la parte superior de la segunda nota
        path.cubicTo(
            27.57 * scale_factor, 53.994 * scale_factor,
            27.578 * scale_factor, 53.704 * scale_factor,
            27.601 * scale_factor, 53.417 * scale_factor
        )
        
        # Línea vertical central
        path.lineTo(27.601 * scale_factor, 48.417 * scale_factor)
        
        # Línea de regreso a la parte superior
        path.cubicTo(
            27.592 * scale_factor, 41.425 * scale_factor,
            27.587 * scale_factor, 34.209 * scale_factor,
            27.601 * scale_factor, 28.307 * scale_factor
        )
        
        # Cerrar el path
        path.lineTo(55.14 * scale_factor, 20.8 * scale_factor)
        
        # Dibujar la nota musical
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(main_color)
        painter.drawPath(path)
        
        # Dibujar las ondas de sonido (izquierda)
        wave_path1 = QPainterPath()
        wave_path1.moveTo(12.06 * scale_factor, 49.651 * scale_factor)
        wave_path1.cubicTo(
            12.556 * scale_factor, 49.196 * scale_factor,
            12.438 * scale_factor, 48.268 * scale_factor,
            11.798 * scale_factor, 47.577 * scale_factor
        )
        wave_path1.cubicTo(
            11.161 * scale_factor, 46.887 * scale_factor,
            10.24 * scale_factor, 46.693 * scale_factor,
            9.744 * scale_factor, 47.148 * scale_factor
        )
        wave_path1.lineTo(7.647 * scale_factor, 49.079 * scale_factor)
        wave_path1.cubicTo(
            7.151 * scale_factor, 49.533 * scale_factor,
            7.276 * scale_factor, 50.463 * scale_factor,
            7.913 * scale_factor, 51.154 * scale_factor
        )
        wave_path1.cubicTo(
            8.548 * scale_factor, 51.844 * scale_factor,
            9.465 * scale_factor, 52.036 * scale_factor,
            9.961 * scale_factor, 51.579 * scale_factor
        )
        wave_path1.closeSubpath()
        
        # Dibujar las ondas de sonido (derecha)
        wave_path2 = QPainterPath()
        wave_path2.moveTo(46.09 * scale_factor, 40.767 * scale_factor)
        wave_path2.cubicTo(
            46.586 * scale_factor, 40.31 * scale_factor,
            46.472 * scale_factor, 39.383 * scale_factor,
            45.832 * scale_factor, 38.694 * scale_factor
        )
        wave_path2.cubicTo(
            45.191 * scale_factor, 38.004 * scale_factor,
            44.274 * scale_factor, 37.809 * scale_factor,
            43.778 * scale_factor, 38.262 * scale_factor
        )
        wave_path2.lineTo(41.681 * scale_factor, 40.196 * scale_factor)
        wave_path2.cubicTo(
            41.185 * scale_factor, 40.649 * scale_factor,
            41.306 * scale_factor, 41.577 * scale_factor,
            41.946 * scale_factor, 42.268 * scale_factor
        )
        wave_path2.cubicTo(
            42.58 * scale_factor, 42.961 * scale_factor,
            43.501 * scale_factor, 43.151 * scale_factor,
            43.997 * scale_factor, 42.696 * scale_factor
        )
        wave_path2.closeSubpath()
        
        # Dibujar las ondas de sonido
        painter.setBrush(highlight_color)
        painter.drawPath(wave_path1)
        painter.drawPath(wave_path2)
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def create_video_icon(size=40):
    """
    Crea un icono de video basado en el SVG proporcionado, con gradientes
    y efectos visuales modernos
    
    Args:
        size: Tamaño del icono en píxeles
    
    Returns:
        QIcon: Icono de video con proporciones precisas
    """
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter()
    
    try:
        if not painter.begin(pixmap):
            return QIcon()
        
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Factor de escala basado en el tamaño original del SVG (24x24)
        scale_factor = size / 24.0
        
        # Crear gradientes radiales y lineales
        # Gradiente para el triángulo de reproducción
        video_gradient = QRadialGradient()
        video_gradient.setCenter(17 * scale_factor, 3 * scale_factor)
        video_gradient.setFocalPoint(17 * scale_factor, 3 * scale_factor)
        video_gradient.setRadius(18.5 * scale_factor)
        video_gradient.setColorAt(0.081, QColor("#f08af4"))  # Rosa claro
        video_gradient.setColorAt(0.394, QColor("#9c6cfe"))  # Púrpura
        video_gradient.setColorAt(1.0, QColor("#4e44db"))    # Azul oscuro
        
        # Gradiente para el rectángulo principal
        rect_gradient = QRadialGradient()
        rect_gradient.setCenter(8.5 * scale_factor, 12 * scale_factor)
        rect_gradient.setFocalPoint(8.5 * scale_factor, 12 * scale_factor)
        rect_gradient.setRadius(21 * scale_factor)
        rect_gradient.setColorAt(0.0, QColor("#f08af4"))    # Rosa claro
        rect_gradient.setColorAt(0.341, QColor("#9c6cfe"))  # Púrpura
        rect_gradient.setColorAt(1.0, QColor("#4e44db"))    # Azul oscuro
        
        # Gradiente para la sombra del triángulo
        shadow_gradient = QLinearGradient(
            14.056 * scale_factor, 12 * scale_factor,
            21.993 * scale_factor, 11.767 * scale_factor
        )
        shadow_gradient.setColorAt(0.0, QColor(49, 42, 154, 255))  # #312a9a
        shadow_gradient.setColorAt(1.0, QColor(49, 42, 154, 0))    # #312a9a con alpha 0
        
        # Gradiente para la barra inferior
        bar_gradient = QLinearGradient(
            3.796 * scale_factor, 13 * scale_factor,
            5.154 * scale_factor, 18.344 * scale_factor
        )
        bar_gradient.setColorAt(0.0, QColor("#3b148a"))  # Púrpura oscuro
        bar_gradient.setColorAt(1.0, QColor("#4b20a0"))  # Púrpura medio
        
        # Dibujar el rectángulo principal (cuerpo de la cámara)
        rect_path = QPainterPath()
        rect_path.addRoundedRect(
            2 * scale_factor, 5 * scale_factor,
            13 * scale_factor, 14 * scale_factor,
            3.25 * scale_factor, 3.25 * scale_factor
        )
        painter.setBrush(rect_gradient)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawPath(rect_path)
        
        # Dibujar el triángulo de reproducción (parte derecha)
        triangle_path = QPainterPath()
        triangle_path.moveTo(11 * scale_factor, 12 * scale_factor)
        triangle_path.lineTo(19.255 * scale_factor, 6.296 * scale_factor)
        triangle_path.lineTo(22 * scale_factor, 7.736 * scale_factor)
        triangle_path.lineTo(22 * scale_factor, 16.264 * scale_factor)
        triangle_path.lineTo(19.255 * scale_factor, 17.704 * scale_factor)
        triangle_path.closeSubpath()
        
        painter.setBrush(video_gradient)
        painter.drawPath(triangle_path)
        
        # Dibujar la sombra del triángulo
        painter.setBrush(shadow_gradient)
        painter.drawPath(triangle_path)
        
        # Dibujar la barra inferior
        bar_path = QPainterPath()
        bar_path.addRoundedRect(
            4 * scale_factor, 13 * scale_factor,
            7 * scale_factor, 4 * scale_factor,
            2 * scale_factor, 2 * scale_factor
        )
        painter.setBrush(bar_gradient)
        painter.setOpacity(0.5)
        painter.drawPath(bar_path)
        painter.setOpacity(1.0)
        
        # Dibujar los círculos blancos (controles)
        painter.setBrush(QColor("#babaff"))  # Color azul claro
        
        # Círculo izquierdo
        left_circle = QPainterPath()
        left_circle.addEllipse(
            7 * scale_factor, 15 * scale_factor,
            1 * scale_factor, 1 * scale_factor
        )
        painter.drawPath(left_circle)
        
        # Círculo derecho
        right_circle = QPainterPath()
        right_circle.addEllipse(
            11 * scale_factor, 15 * scale_factor,
            1 * scale_factor, 1 * scale_factor
        )
        painter.drawPath(right_circle)
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def registro(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g fill="none"><path fill="url(#fluentColorClipboardTextEdit320)" d="M8.5 4A3.5 3.5 0 0 0 5 7.5v19A3.5 3.5 0 0 0 8.5 30h8.974c.146-.328.353-.634.621-.903l8.505-8.505a3 3 0 0 1 .4-.336V7.5A3.5 3.5 0 0 0 23.5 4z"/><path fill="url(#fluentColorClipboardTextEdit321)" fill-opacity="0.9" d="M10 13a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H11a1 1 0 0 1-1-1"/><path fill="url(#fluentColorClipboardTextEdit322)" fill-opacity="0.9" d="M10 18a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2h-5a1 1 0 0 1-1-1"/><path fill="url(#fluentColorClipboardTextEdit323)" fill-opacity="0.9" d="M11 22a1 1 0 1 0 0 2h10a1 1 0 1 0 0-2z"/><path fill="url(#fluentColorClipboardTextEdit329)" fill-opacity="0.4" d="M8.5 4A3.5 3.5 0 0 0 5 7.5v19A3.5 3.5 0 0 0 8.5 30h8.974c.146-.328.353-.634.621-.903l8.505-8.505a3 3 0 0 1 .4-.336V7.5A3.5 3.5 0 0 0 23.5 4z"/><path fill="url(#fluentColorClipboardTextEdit32a)" fill-opacity="0.4" d="M8.5 4A3.5 3.5 0 0 0 5 7.5v19A3.5 3.5 0 0 0 8.5 30h8.974c.146-.328.353-.634.621-.903l8.505-8.505a3 3 0 0 1 .4-.336V7.5A3.5 3.5 0 0 0 23.5 4z"/><path fill="url(#fluentColorClipboardTextEdit32b)" fill-opacity="0.4" d="M8.5 4A3.5 3.5 0 0 0 5 7.5v19A3.5 3.5 0 0 0 8.5 30h8.974c.146-.328.353-.634.621-.903l8.505-8.505a3 3 0 0 1 .4-.336V7.5A3.5 3.5 0 0 0 23.5 4z"/><path fill="url(#fluentColorClipboardTextEdit324)" d="M10 5a3 3 0 0 1 3-3h6a3 3 0 1 1 0 6h-6a3 3 0 0 1-3-3"/><path fill="url(#fluentColorClipboardTextEdit325)" d="m20.539 29.469l7.61-7.543l-4.073-4.074l-7.567 7.64l.308 3.695z"/><path fill="url(#fluentColorClipboardTextEdit326)" d="m26.937 23.14l2.211-2.214a2.88 2.88 0 0 0 .072-4.017a2.88 2.88 0 0 0-4.144-.057l-2.238 2.241z"/><path fill="url(#fluentColorClipboardTextEdit327)" d="M24.094 17.838a5.43 5.43 0 0 0 4.106 4.038l-1.55 1.551a5.43 5.43 0 0 1-4.106-4.04z"/><path fill="url(#fluentColorClipboardTextEdit328)" d="m20.539 29.47l.223-.223s-1.726-.661-2.535-1.47c-.809-.81-1.47-2.534-1.47-2.534l-.248.249a2.66 2.66 0 0 0-.686 1.206l-.79 3.051a1 1 0 0 0 1.217 1.22l3.02-.778a2.8 2.8 0 0 0 1.269-.722"/><defs><linearGradient id="fluentColorClipboardTextEdit320" x1="5" x2="25.632" y1="6.6" y2="31.15" gradientUnits="userSpaceOnUse"><stop stop-color="#36dff1"/><stop offset="1" stop-color="#0094f0"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit321" x1="17.5" x2="8.622" y1="24" y2="13.125" gradientUnits="userSpaceOnUse"><stop stop-color="#9deaff"/><stop offset="1" stop-color="#fff"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit322" x1="17.5" x2="8.622" y1="24" y2="13.125" gradientUnits="userSpaceOnUse"><stop stop-color="#9deaff"/><stop offset="1" stop-color="#fff"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit323" x1="17.5" x2="8.622" y1="24" y2="13.125" gradientUnits="userSpaceOnUse"><stop stop-color="#9deaff"/><stop offset="1" stop-color="#fff"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit324" x1="16" x2="16" y1="2" y2="8" gradientUnits="userSpaceOnUse"><stop stop-color="#ffe06b"/><stop offset="1" stop-color="#fab500"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit325" x1="19.861" x2="26.044" y1="19.948" y2="26.149" gradientUnits="userSpaceOnUse"><stop stop-color="#ffa43d"/><stop offset="1" stop-color="#fb5937"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit326" x1="28.502" x2="25.869" y1="17.485" y2="19.969" gradientUnits="userSpaceOnUse"><stop stop-color="#f97dbd"/><stop offset="1" stop-color="#dd3ce2"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit327" x1="25.469" x2="21.489" y1="21.664" y2="19.902" gradientUnits="userSpaceOnUse"><stop stop-color="#ff921f"/><stop offset="1" stop-color="#ffe994"/></linearGradient><linearGradient id="fluentColorClipboardTextEdit328" x1="14.174" x2="18.325" y1="26.847" y2="30.975" gradientUnits="userSpaceOnUse"><stop offset=".255" stop-color="#ffd394"/><stop offset="1" stop-color="#ff921f"/></linearGradient><radialGradient id="fluentColorClipboardTextEdit329" cx="0" cy="0" r="1" gradientTransform="matrix(8.9375 0 0 7.86963 16 2.556)" gradientUnits="userSpaceOnUse"><stop stop-color="#0a1852"/><stop offset="1" stop-color="#0a1852" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorClipboardTextEdit32a" cx="0" cy="0" r="1" gradientTransform="matrix(8.9375 0 0 8.87743 16 2.556)" gradientUnits="userSpaceOnUse"><stop stop-color="#0a1852"/><stop offset="1" stop-color="#0a1852" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorClipboardTextEdit32b" cx="0" cy="0" r="1" gradientTransform="matrix(-10.31253 10.11112 -4.54174 -4.6322 23.563 22.778)" gradientUnits="userSpaceOnUse"><stop stop-color="#0a1852"/><stop offset="1" stop-color="#0a1852" stop-opacity="0"/></radialGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_usb_icon(size=128):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#258e05" fill-rule="evenodd" d="m5.255 9.54l.767-.767c2.17-2.17 3.254-3.255 4.603-3.255q.301 0 .59.073c1.106.275 2.167 1.336 4.012 3.18v.002h.001c1.58 1.58 2.585 2.585 3.014 3.535c.16.355.24.701.24 1.067c0 1.348-1.085 2.433-3.255 4.603l-.767.767C12.29 20.915 11.206 22 9.857 22s-2.433-1.085-4.602-3.255S2 15.491 2 14.142S3.085 11.71 5.255 9.54m-.53 4.072a.75.75 0 0 1 1.06 0l4.603 4.603a.75.75 0 0 1-1.06 1.06l-4.603-4.602a.75.75 0 0 1 0-1.06M19.83 4.17C21.277 5.616 22 6.34 22 7.238c0 .9-.723 1.622-2.17 3.069l-.635.636a8 8 0 0 0-.334-.47c-.604-.792-1.478-1.666-2.502-2.69l-.142-.142c-1.024-1.024-1.898-1.898-2.69-2.502a8 8 0 0 0-.47-.333l.636-.636C15.14 2.723 15.863 2 16.762 2c.898 0 1.622.723 3.068 2.17m-.209 2.33a.75.75 0 0 1 0 1.06l-.707.708a.75.75 0 1 1-1.06-1.06l.707-.708a.75.75 0 0 1 1.06 0M17.5 4.379a.75.75 0 0 1 0 1.06l-.707.707a.75.75 0 1 1-1.06-1.06l.706-.707a.75.75 0 0 1 1.06 0" clip-rule="evenodd" stroke-width="0.4" stroke="#258e05"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_external_hdd_icon(size=128):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#f77a15" d="M18.842 13.376c1.126 0 2.14.453 2.891 1.181l-2.365-9.379C18.842 3.545 17.9 3 16.737 3H7.263C6.1 3 5.158 3.545 4.632 5.178l-2.365 9.38a4.14 4.14 0 0 1 2.89-1.182z"/><path fill="#f77a15" fill-rule="evenodd" d="M5.158 14.405c-1.167 0-2.2.663-2.75 1.674A3.4 3.4 0 0 0 2 17.703C2 19.552 3.442 21 5.158 21h13.684C20.558 21 22 19.552 22 17.703c0-.593-.15-1.146-.409-1.624c-.549-1.01-1.582-1.674-2.749-1.674zM11.21 17.4a.78.78 0 0 0-.789-.771a.78.78 0 0 0-.79.771v1.029a.78.78 0 0 0 .79.771a.78.78 0 0 0 .79-.771zm1.843-.771a.78.78 0 0 1 .79.771v1.029a.78.78 0 0 1-.79.771a.78.78 0 0 1-.79-.771V17.4a.78.78 0 0 1 .79-.771m3.42.771a.78.78 0 0 0-.789-.771a.78.78 0 0 0-.79.771v1.029a.78.78 0 0 0 .79.771a.78.78 0 0 0 .79-.771zm2.632 0a.78.78 0 0 0-.79-.771a.78.78 0 0 0-.789.771v1.029a.78.78 0 0 0 .79.771a.78.78 0 0 0 .79-.771z" clip-rule="evenodd"/></svg>'''
    return crear_icono_svg(svg_data, size)

def renombrar(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><g fill="none"><path fill="url(#fluentColorTextEditStyle200)" fill-rule="evenodd" d="M5.301 2.228a.73.73 0 0 1 .659-.477l.027-.001h.053c.275.013.54.174.659.478l3.504 9.012l.031.109l.002.01a.75.75 0 0 1-.464.842l-.012.004l-.107.03l-.01.002a.75.75 0 0 1-.842-.464l-.98-2.52H4.179l-.979 2.52a.75.75 0 0 1-.348.391H2.85a.75.75 0 0 1-.493.074l-.01-.002l-.107-.03l-.012-.005a.75.75 0 0 1-.464-.841l.002-.011l.031-.109zM4.55 7.73h2.9L6 4z" clip-rule="evenodd"/><path fill="url(#fluentColorTextEditStyle201)" d="m10.296 16.938l5.582-5.608l-3.206-3.213l-5.587 5.614l.523 2.686z"/><path fill="url(#fluentColorTextEditStyle202)" d="M9.87 17.168a1.5 1.5 0 0 0 .546-.35s-.966.096-2.136-1.073c-1.169-1.17-1.075-2.134-1.075-2.134l-.107.12a1.5 1.5 0 0 0-.283.564l-.792 3.07l-.015.085a.5.5 0 0 0 .619.526l3.085-.76z"/><path fill="url(#fluentColorTextEditStyle203)" d="M9.87 17.168a1.5 1.5 0 0 0 .546-.35s-.966.096-2.136-1.073c-1.169-1.17-1.075-2.134-1.075-2.134l-.107.12a1.5 1.5 0 0 0-.283.564l-.792 3.07l-.015.085a.5.5 0 0 0 .619.526l3.085-.76z"/><path fill="url(#fluentColorTextEditStyle204)" d="M9.87 17.168a1.5 1.5 0 0 0 .546-.35s-.966.096-2.136-1.073c-1.169-1.17-1.075-2.134-1.075-2.134l-.107.12a1.5 1.5 0 0 0-.283.564l-.792 3.07l-.015.085a.5.5 0 0 0 .619.526l3.085-.76z"/><path fill="url(#fluentColorTextEditStyle205)" d="M17.156 6.577a2.263 2.263 0 0 0-3.07.126l-1.57 1.57l3.21 3.21l1.566-1.567l.12-.13a2.276 2.276 0 0 0 0-2.952l-.126-.137z"/><path fill="url(#fluentColorTextEditStyle206)" d="m15.354 11.856l1.231-1.233s-.95.106-2.133-1.076C13.27 8.364 13.38 7.41 13.38 7.41l-1.24 1.238s-.08.978 1.089 2.147c1.17 1.17 2.124 1.061 2.124 1.061"/><defs><linearGradient id="fluentColorTextEditStyle200" x1="1.75" x2="4.604" y1="2.368" y2="13.842" gradientUnits="userSpaceOnUse"><stop stop-color="#0fafff"/><stop offset=".677" stop-color="#0078d4"/><stop offset=".84" stop-color="#0057aa"/><stop offset="1" stop-color="#0057aa"/></linearGradient><linearGradient id="fluentColorTextEditStyle201" x1="9.505" x2="13.769" y1="10.252" y2="14.508" gradientUnits="userSpaceOnUse"><stop stop-color="#ffa43d"/><stop offset="1" stop-color="#fc0000"/></linearGradient><linearGradient id="fluentColorTextEditStyle202" x1="5.904" x2="8.728" y1="14.702" y2="17.53" gradientUnits="userSpaceOnUse"><stop offset=".255" stop-color="#ffd394"/><stop offset="1" stop-color="#ffb15e"/></linearGradient><linearGradient id="fluentColorTextEditStyle203" x1="8.725" x2="7.113" y1="16.255" y2="14.499" gradientUnits="userSpaceOnUse"><stop stop-color="#ac80ff"/><stop offset="1" stop-color="#ceb0ff"/></linearGradient><linearGradient id="fluentColorTextEditStyle204" x1="5.375" x2="8.532" y1="14.832" y2="17.991" gradientUnits="userSpaceOnUse"><stop offset=".255" stop-color="#ffd394"/><stop offset="1" stop-color="#ffb15e"/></linearGradient><linearGradient id="fluentColorTextEditStyle205" x1="16.818" x2="14.81" y1="7.17" y2="9.056" gradientUnits="userSpaceOnUse"><stop stop-color="#f97dbd"/><stop offset="1" stop-color="#dd3ce2"/></linearGradient><linearGradient id="fluentColorTextEditStyle206" x1="14.446" x2="11.308" y1="10.463" y2="9.099" gradientUnits="userSpaceOnUse"><stop stop-color="#ffb15e"/><stop offset="1" stop-color="#ffe994"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_internal_hdd_icon(size=128):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="#029eff"><path d="M8.785 9.896A3.001 3.001 0 0 0 8 4a3 3 0 0 0-.891 5.865c.667-.44 1.396-.91 1.955-1.268c.224-.144.483.115.34.34zM9 7a1 1 0 1 1-2 0a1 1 0 0 1 2 0"/><path d="M4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zm9 1.5a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0m0 13a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0m-9.5.5a.5.5 0 1 1 0-1a.5.5 0 0 1 0 1M4 1.5a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0m2.882 11.177a1.102 1.102 0 0 1-1.56-1.559c.1-.098.396-.314.795-.588a4 4 0 1 1 1.946.47c-.537.813-1.02 1.515-1.181 1.677"/></g></svg>'''
    return crear_icono_svg(svg_data, size)

def create_windows_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
	<path fill="#1492e8" d="M3 6.75A3.75 3.75 0 0 1 6.75 3h6.5v10.25H3zm0 8v6.5A3.75 3.75 0 0 0 6.75 25h6.5V14.75zM14.75 25h6.5A3.75 3.75 0 0 0 25 21.25v-6.5H14.75zM25 13.25v-6.5A3.75 3.75 0 0 0 21.25 3h-6.5v10.25z" />
</svg>'''
    return crear_icono_svg(svg_data, size)

def create_pin_icon(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 512 512"><path fill="#75736f" d="M326.953 22.87L306.68 83.685l20.273 20.273l-115.428 115.427c-16.39-8-34.277-14.452-51.84-18.502c-14.247-3.285-28.136-4.902-40.802-4.772c-16.84.173-31.505 3.44-41.975 9.973L305.914 435.09c11.447-18.345 12.853-49.592 5.2-82.776c-4.05-17.564-10.502-35.45-18.5-51.84l115.427-115.43l20.274 20.274l60.817-20.273L326.954 22.87zM159.207 313.84L22.87 489.13l175.29-136.337z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_unpin_icon(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 512 512"><path fill="#f4bd00" d="M326.953 22.87L306.68 83.685l20.273 20.273l-115.428 115.427c-16.39-8-34.277-14.452-51.84-18.502c-14.247-3.285-28.136-4.902-40.802-4.772c-16.84.173-31.505 3.44-41.975 9.973L305.914 435.09c11.447-18.345 12.853-49.592 5.2-82.776c-4.05-17.564-10.502-35.45-18.5-51.84l115.427-115.43l20.274 20.274l60.817-20.273L326.954 22.87zM159.207 313.84L22.87 489.13l175.29-136.337z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_eject_icon(size=40):
    """
    Crea un icono de expulsión con un diseño moderno en color naranja
    basado en un diseño SVG profesional
    
    Args:
        size: Tamaño del icono en píxeles
    
    Returns:
        QIcon: Icono de expulsión
    """
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter()
    try:
        if not painter.begin(pixmap):
            return QIcon()
        
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Color naranja para el icono
        eject_color = QColor("#f29200")
        
        # Escalar las coordenadas del SVG al tamaño del icono
        scale_factor = size / 256.0
        
        # Crear el path para la parte superior (triángulo con curvas)
        triangle_path = QPainterPath()
        
        # Parte superior (triángulo con curvas)
        triangle_path.moveTo(33.31 * scale_factor, 126.24 * scale_factor)
        triangle_path.cubicTo(
            33.31 * scale_factor, 126.24 * scale_factor,  # punto de control 1
            36.41 * scale_factor, 109.12 * scale_factor,  # punto de control 2
            36.41 * scale_factor, 109.12 * scale_factor   # punto final
        )
        triangle_path.lineTo(110.14 * scale_factor, 31.61 * scale_factor)
        triangle_path.cubicTo(
            110.14 * scale_factor, 31.61 * scale_factor,  # punto de control 1
            128 * scale_factor, 15 * scale_factor,        # punto de control 2
            145.86 * scale_factor, 31.61 * scale_factor   # punto final
        )
        triangle_path.lineTo(219.59 * scale_factor, 109.12 * scale_factor)
        triangle_path.cubicTo(
            219.59 * scale_factor, 109.12 * scale_factor,  # punto de control 1
            222.69 * scale_factor, 126.24 * scale_factor,  # punto de control 2
            222.69 * scale_factor, 126.24 * scale_factor   # punto final
        )
        triangle_path.cubicTo(
            222.69 * scale_factor, 126.24 * scale_factor,  # punto de control 1
            207.76 * scale_factor, 136 * scale_factor,     # punto de control 2
            207.76 * scale_factor, 136 * scale_factor      # punto final
        )
        triangle_path.lineTo(48.24 * scale_factor, 136 * scale_factor)
        triangle_path.cubicTo(
            48.24 * scale_factor, 136 * scale_factor,      # punto de control 1
            33.31 * scale_factor, 126.24 * scale_factor,   # punto de control 2
            33.31 * scale_factor, 126.24 * scale_factor    # punto final
        )
        triangle_path.closeSubpath()
        
        # Crear el path para la parte inferior (rectángulo con bordes redondeados)
        # Ajustar para que tenga el mismo ancho que la base del triángulo
        rect_path = QPainterPath()
        
        # Calcular el ancho del triángulo en su base (aproximadamente)
        triangle_width = (207.76 - 48.24) * scale_factor
        triangle_center_x = (207.76 + 48.24) / 2 * scale_factor
        
        # Hacer el rectángulo un 20% más ancho que la base del triángulo
        rect_width = triangle_width * 1.2
        
        rect_rect = QRectF(
            triangle_center_x - rect_width/2,  # x centrado con el triángulo
            152 * scale_factor,                # y
            rect_width,                        # width más ancho que el triángulo
            48 * scale_factor                  # height
        )
        rect_path.addRoundedRect(rect_rect, 16 * scale_factor, 16 * scale_factor)
        
        # Aplicar un gradiente para dar profundidad
        gradient = QLinearGradient(0, 0, 0, size)
        gradient.setColorAt(0.0, eject_color.lighter(120))
        gradient.setColorAt(0.5, eject_color)
        gradient.setColorAt(1.0, eject_color.darker(110))
        
        # Dibujar las formas
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(gradient)
        painter.drawPath(triangle_path)
        painter.drawPath(rect_path)
        
        # Añadir un borde sutil
        painter.setPen(QPen(QColor(0, 0, 0, 40), 0.5))
        painter.drawPath(triangle_path)
        painter.drawPath(rect_path)
        
        # Añadir un brillo en la parte superior
        highlight_path = QPainterPath()
        highlight_path.moveTo(128 * scale_factor, 40 * scale_factor)  # Punto superior
        highlight_path.lineTo(80 * scale_factor, 80 * scale_factor)   # Punto medio izquierdo
        highlight_path.lineTo(176 * scale_factor, 80 * scale_factor)  # Punto medio derecho
        highlight_path.closeSubpath()
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QColor(255, 255, 255, 60))
        painter.drawPath(highlight_path)
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def create_stop_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#f00" d="M2 12c0-4.714 0-7.071 1.464-8.536C4.93 2 7.286 2 12 2s7.071 0 8.535 1.464C22 4.93 22 7.286 22 12s0 7.071-1.465 8.535C19.072 22 16.714 22 12 22s-7.071 0-8.536-1.465C2 19.072 2 16.714 2 12"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_download_icon(size=40):
    """
    Crea un icono de descarga con un círculo azul intenso, un círculo naranja y una flecha verde extra grande
    posicionada más arriba, con contorno negro pronunciado
    
    Args:
        size: Tamaño del icono en píxeles
    
    Returns:
        QIcon: Icono de descarga
    """
    from PyQt6.QtGui import QIcon, QPixmap, QPainter, QColor, QPen, QPainterPath, QBrush, QLinearGradient
    from PyQt6.QtCore import Qt, QRectF, QPointF
    
    # Crear un pixmap transparente
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    # Crear un pintor
    painter = QPainter()
    try:
        if not painter.begin(pixmap):
            return QIcon()
            
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Definir el centro y radio
        center_x = size / 2
        center_y = size / 2
        
        # Colores exactos de la imagen de referencia
        blue_color = QColor(0, 102, 204)      # Azul más intenso para el círculo exterior
        orange_color = QColor(243, 156, 18)   # Naranja para el círculo interior
        green_color = QColor(46, 204, 113)    # Verde para la flecha
        
        # Dibujar el círculo exterior azul
        outer_radius = size * 0.45
        outer_pen = QPen(blue_color, size * 0.08)
        outer_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(outer_pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        
        # Dibujar el círculo completo
        painter.drawEllipse(QPointF(center_x, center_y), outer_radius, outer_radius)
        
        # Dibujar el círculo interior naranja
        inner_radius = size * 0.25
        painter.setPen(Qt.PenStyle.NoPen)
        
        # Gradiente para el círculo naranja
        orange_gradient = QLinearGradient(center_x, center_y - inner_radius, center_x, center_y + inner_radius)
        orange_gradient.setColorAt(0.0, QColor(243, 156, 18))
        orange_gradient.setColorAt(1.0, QColor(230, 126, 34))
        painter.setBrush(orange_gradient)
        painter.drawEllipse(QPointF(center_x, center_y), inner_radius, inner_radius)
        
        # Dimensiones de la flecha ajustadas
        arrow_height = size * 0.70            # Altura intermedia (entre 0.65 y 0.75)
        arrow_width = size * 0.28             # Palo estrecho
        arrow_head_width = size * 0.65        # Punta ancha
        arrow_head_height = size * 0.28       # Altura de la punta
        
        # Posición de la flecha
        arrow_x = center_x
        arrow_y = center_y - size * 0.30      # Mantiene la posición alta
        
        # Dibujar el cuerpo de la flecha
        arrow_body_left = arrow_x - arrow_width/2
        arrow_body_right = arrow_x + arrow_width/2
        arrow_body_top = arrow_y - arrow_height/2 + arrow_head_height * 0.7  # Factor intermedio (entre 0.6 y 0.8)
        arrow_body_bottom = arrow_y + arrow_height/2
        
        # Crear el path de la flecha con esquinas redondeadas
        arrow_path = QPainterPath()
        corner_radius = size * 0.05  # Radio para las esquinas redondeadas
        
        # Parte superior del palo (redondeada)
        arrow_path.moveTo(arrow_body_left + corner_radius, arrow_body_top)
        arrow_path.lineTo(arrow_body_right - corner_radius, arrow_body_top)
        arrow_path.arcTo(
            arrow_body_right - corner_radius * 2,
            arrow_body_top,
            corner_radius * 2,
            corner_radius * 2,
            90, -90
        )
        
        # Lado derecho del palo
        arrow_path.lineTo(arrow_body_right, arrow_body_bottom - arrow_head_height)
        
        # Punta de flecha (con esquinas redondeadas)
        arrow_path.lineTo(arrow_x + arrow_head_width/2, arrow_body_bottom - arrow_head_height)
        arrow_path.lineTo(arrow_x, arrow_body_bottom)
        arrow_path.lineTo(arrow_x - arrow_head_width/2, arrow_body_bottom - arrow_head_height)
        
        # Lado izquierdo del palo
        arrow_path.lineTo(arrow_body_left, arrow_body_bottom - arrow_head_height)
        
        # Esquina superior izquierda (redondeada)
        arrow_path.arcTo(
            arrow_body_left,
            arrow_body_top,
            corner_radius * 2,
            corner_radius * 2,
            180, -90
        )
        
        arrow_path.closeSubpath()
        
        # Dibujar primero el contorno negro más pronunciado
        outline_pen = QPen(QColor(0, 0, 0), size * 0.06)  # Contorno negro grueso
        outline_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        outline_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(outline_pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawPath(arrow_path)
        
        # Luego dibujar la flecha verde
        green_gradient = QLinearGradient(arrow_x, arrow_y - arrow_height/2, arrow_x, arrow_y + arrow_height/2)
        green_gradient.setColorAt(0.0, QColor(46, 204, 113))  # Verde estándar
        green_gradient.setColorAt(1.0, QColor(39, 174, 96))   # Verde más oscuro
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(green_gradient)
        painter.drawPath(arrow_path)
        
        # Añadir brillo a la flecha
        highlight_path = QPainterPath()
        highlight_path.moveTo(arrow_x - arrow_width/3, arrow_y - arrow_height/2 + arrow_head_height + size * 0.05)
        highlight_path.lineTo(arrow_x - arrow_width/3, arrow_y + arrow_height/2 - arrow_head_height - size * 0.05)
        
        highlight_pen = QPen(QColor(255, 255, 255, 100), size * 0.04)
        highlight_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(highlight_pen)
        painter.drawPath(highlight_path)
        
        # Añadir sombra sutil al borde derecho
        shadow_path = QPainterPath()
        shadow_path.moveTo(arrow_x + arrow_width/3, arrow_y - arrow_height/2 + arrow_head_height + size * 0.05)
        shadow_path.lineTo(arrow_x + arrow_width/3, arrow_y + arrow_height/2 - arrow_head_height - size * 0.05)
        
        shadow_pen = QPen(QColor(0, 0, 0, 40), size * 0.03)
        shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(shadow_pen)
        painter.drawPath(shadow_path)
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def create_upload_icon(size=40):
    """
    Crea un icono de subida con un círculo azul intenso, un círculo naranja y una flecha verde extra grande
    posicionada ligeramente arriba, con contorno negro pronunciado, similar al icono de descarga pero invertido
    
    Args:
        size: Tamaño del icono en píxeles
    
    Returns:
        QIcon: Icono de subida
    """
    from PyQt6.QtGui import QIcon, QPixmap, QPainter, QColor, QPen, QPainterPath, QBrush, QLinearGradient
    from PyQt6.QtCore import Qt, QRectF, QPointF
    
    # Crear un pixmap transparente
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    # Crear un pintor
    painter = QPainter()
    try:
        if not painter.begin(pixmap):
            return QIcon()
            
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Definir el centro y radio
        center_x = size / 2
        center_y = size / 2
        
        # Colores exactos de la imagen de referencia
        blue_color = QColor(0, 102, 204)      # Azul más intenso para el círculo exterior
        orange_color = QColor(243, 156, 18)   # Naranja para el círculo interior
        green_color = QColor(46, 204, 113)    # Verde para la flecha
        
        # Dibujar el círculo exterior azul
        outer_radius = size * 0.45
        outer_pen = QPen(blue_color, size * 0.08)
        outer_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(outer_pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        
        # Dibujar el círculo completo
        painter.drawEllipse(QPointF(center_x, center_y), outer_radius, outer_radius)
        
        # Dibujar el círculo interior naranja
        inner_radius = size * 0.25
        painter.setPen(Qt.PenStyle.NoPen)
        
        # Gradiente para el círculo naranja
        orange_gradient = QLinearGradient(center_x, center_y - inner_radius, center_x, center_y + inner_radius)
        orange_gradient.setColorAt(0.0, QColor(243, 156, 18))
        orange_gradient.setColorAt(1.0, QColor(230, 126, 34))
        painter.setBrush(orange_gradient)
        painter.drawEllipse(QPointF(center_x, center_y), inner_radius, inner_radius)
        
        # Dimensiones de la flecha ajustadas
        arrow_height = size * 0.70            # Altura intermedia
        arrow_width = size * 0.28             # Palo estrecho
        arrow_head_width = size * 0.65        # Punta ancha
        arrow_head_height = size * 0.28       # Altura de la punta
        
        # Posición de la flecha (invertida respecto a la de descarga)
        arrow_x = center_x
        arrow_y = center_y - size * 0.12      # Ajustado un poquito menos hacia arriba
        
        # Crear el path de la flecha con esquinas redondeadas
        arrow_path = QPainterPath()
        corner_radius = size * 0.05  # Radio para las esquinas redondeadas
        
        # Invertir la flecha: rotar 180 grados
        painter.save()
        painter.translate(center_x, center_y - size * 0.12)  # Trasladar al nuevo centro
        painter.rotate(180)
        painter.translate(-(center_x), -(center_y - size * 0.12))  # Ajustar la traslación
        
        # Usar exactamente el mismo código de la flecha de descarga
        arrow_body_left = arrow_x - arrow_width/2
        arrow_body_right = arrow_x + arrow_width/2
        arrow_body_top = arrow_y - arrow_height/2 + arrow_head_height * 0.7
        arrow_body_bottom = arrow_y + arrow_height/2
        
        # Parte superior del palo (redondeada)
        arrow_path.moveTo(arrow_body_left + corner_radius, arrow_body_top)
        arrow_path.lineTo(arrow_body_right - corner_radius, arrow_body_top)
        arrow_path.arcTo(
            arrow_body_right - corner_radius * 2,
            arrow_body_top,
            corner_radius * 2,
            corner_radius * 2,
            90, -90
        )
        
        # Lado derecho del palo
        arrow_path.lineTo(arrow_body_right, arrow_body_bottom - arrow_head_height)
        
        # Punta de flecha (con esquinas redondeadas)
        arrow_path.lineTo(arrow_x + arrow_head_width/2, arrow_body_bottom - arrow_head_height)
        arrow_path.lineTo(arrow_x, arrow_body_bottom)
        arrow_path.lineTo(arrow_x - arrow_head_width/2, arrow_body_bottom - arrow_head_height)
        
        # Lado izquierdo del palo
        arrow_path.lineTo(arrow_body_left, arrow_body_bottom - arrow_head_height)
        
        # Esquina superior izquierda (redondeada)
        arrow_path.arcTo(
            arrow_body_left,
            arrow_body_top,
            corner_radius * 2,
            corner_radius * 2,
            180, -90
        )
        
        arrow_path.closeSubpath()
        
        # Dibujar primero el contorno negro más pronunciado
        outline_pen = QPen(QColor(0, 0, 0), size * 0.06)  # Contorno negro grueso
        outline_pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin)
        outline_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(outline_pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawPath(arrow_path)
        
        # Luego dibujar la flecha verde
        green_gradient = QLinearGradient(arrow_x, arrow_y - arrow_height/2, arrow_x, arrow_y + arrow_height/2)
        green_gradient.setColorAt(0.0, QColor(46, 204, 113))  # Verde estándar
        green_gradient.setColorAt(1.0, QColor(39, 174, 96))   # Verde más oscuro
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(green_gradient)
        painter.drawPath(arrow_path)
        
        # Añadir brillo a la flecha
        highlight_path = QPainterPath()
        highlight_path.moveTo(arrow_x - arrow_width/3, arrow_y - arrow_height/2 + arrow_head_height + size * 0.05)
        highlight_path.lineTo(arrow_x - arrow_width/3, arrow_y + arrow_height/2 - arrow_head_height - size * 0.05)
        
        highlight_pen = QPen(QColor(255, 255, 255, 100), size * 0.04)
        highlight_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(highlight_pen)
        painter.drawPath(highlight_path)
        
        # Añadir sombra sutil al borde derecho
        shadow_path = QPainterPath()
        shadow_path.moveTo(arrow_x + arrow_width/3, arrow_y - arrow_height/2 + arrow_head_height + size * 0.05)
        shadow_path.lineTo(arrow_x + arrow_width/3, arrow_y + arrow_height/2 - arrow_head_height - size * 0.05)
        
        shadow_pen = QPen(QColor(0, 0, 0, 40), size * 0.03)
        shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(shadow_pen)
        painter.drawPath(shadow_path)
        
        # Restaurar la transformación
        painter.restore()
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def create_settings_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48"><g fill="none"><path fill="url(#fluentColorSettings480)" d="M19.494 43.468c1.479.353 2.993.531 4.513.531a19.4 19.4 0 0 0 4.503-.534a1.94 1.94 0 0 0 1.474-1.672l.338-3.071a2.32 2.32 0 0 1 2.183-2.075c.367-.016.732.053 1.068.2l2.807 1.231a1.92 1.92 0 0 0 1.554.01c.247-.105.468-.261.65-.458a20.4 20.4 0 0 0 4.51-7.779a1.94 1.94 0 0 0-.7-2.133l-2.494-1.84a2.326 2.326 0 0 1 0-3.764l2.486-1.836a1.94 1.94 0 0 0 .7-2.138a20.3 20.3 0 0 0-4.515-7.777a1.94 1.94 0 0 0-2.192-.45l-2.806 1.236c-.29.131-.606.2-.926.2a2.34 2.34 0 0 1-2.32-2.088l-.34-3.06a1.94 1.94 0 0 0-1.5-1.681a21.7 21.7 0 0 0-4.469-.519a22 22 0 0 0-4.5.52a1.935 1.935 0 0 0-1.5 1.677l-.34 3.062a2.35 2.35 0 0 1-.768 1.488a2.53 2.53 0 0 1-1.569.6a2.3 2.3 0 0 1-.923-.194l-2.8-1.236a1.94 1.94 0 0 0-2.2.452a20.35 20.35 0 0 0-4.51 7.775a1.94 1.94 0 0 0 .7 2.137l2.488 1.836a2.344 2.344 0 0 1 .701 2.938a2.34 2.34 0 0 1-.7.829l-2.49 1.839a1.94 1.94 0 0 0-.7 2.135a20.3 20.3 0 0 0 4.51 7.782a1.93 1.93 0 0 0 2.193.454l2.818-1.237c.291-.128.605-.194.923-.194h.008a2.34 2.34 0 0 1 2.32 2.074l.338 3.057a1.94 1.94 0 0 0 1.477 1.673M24 30.25a6.25 6.25 0 1 1 0-12.5a6.25 6.25 0 0 1 0 12.5"/><defs><linearGradient id="fluentColorSettings480" x1="33.588" x2="11.226" y1="42.451" y2="7.607" gradientUnits="userSpaceOnUse"><stop stop-color="#70777d"/><stop offset="1" stop-color="#b9c0c7"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_pausa(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><path fill="#f25252" d="M5 2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zm8 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_eye_icon(size=40):
    """Crea un icono de ojo para indicar mostrar"""
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter()
    try:
        if not painter.begin(pixmap):
            return QIcon()
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Centro y dimensiones
        center_x = size / 2
        center_y = size / 2
        eye_width = size * 0.8
        eye_height = size * 0.5
        
        # Dibujar el globo ocular (elipse)
        eye_rect = QRectF(
            center_x - eye_width/2,
            center_y - eye_height/2,
            eye_width,
            eye_height
        )
        
        # Gradiente para el globo ocular
        eye_gradient = QRadialGradient(center_x, center_y, eye_width/2)
        eye_gradient.setColorAt(0.0, QColor(255, 255, 255))
        eye_gradient.setColorAt(0.7, QColor(240, 240, 240))
        eye_gradient.setColorAt(1.0, QColor(224, 224, 224))
        
        # Dibujar el globo ocular
        painter.setPen(QPen(QColor(136, 136, 136), size * 0.01))
        painter.setBrush(eye_gradient)
        painter.drawEllipse(eye_rect)
        
        # Dibujar el iris
        iris_radius = size * 0.16
        iris_gradient = QRadialGradient(center_x, center_y, iris_radius)
        iris_gradient.setColorAt(0.0, QColor(90, 138, 198))
        iris_gradient.setColorAt(0.7, QColor(58, 99, 150))
        iris_gradient.setColorAt(1.0, QColor(26, 67, 118))
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(iris_gradient)
        painter.drawEllipse(QPointF(center_x, center_y), iris_radius, iris_radius)
        
        # Dibujar la pupila
        pupil_radius = size * 0.06
        painter.setBrush(QColor(0, 0, 0))
        painter.drawEllipse(QPointF(center_x, center_y), pupil_radius, pupil_radius)
        
        # Dibujar el brillo del ojo
        highlight_radius = size * 0.04
        painter.setBrush(QColor(255, 255, 255, 180))
        painter.drawEllipse(QPointF(center_x - iris_radius * 0.4, center_y - iris_radius * 0.3), 
                           highlight_radius, highlight_radius)
        
        # Añadir sombra sutil alrededor del ojo
        shadow_path = QPainterPath()
        shadow_path.addEllipse(eye_rect.adjusted(-1, -1, 1, 1))
        shadow_pen = QPen(QColor(0, 0, 0, 30), size * 0.01)
        painter.setPen(shadow_pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawPath(shadow_path)
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def create_eye_crossed_icon(size=40):
    """Crea un icono de ojo tachado para indicar ocultar"""
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    painter = QPainter()
    try:
        if not painter.begin(pixmap):
            return QIcon()
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # Centro y dimensiones
        center_x = size / 2
        center_y = size / 2
        eye_width = size * 0.82
        eye_height = size * 0.52
        
        # Dibujar el globo ocular (elipse)
        eye_rect = QRectF(
            center_x - eye_width/2,
            center_y - eye_height/2,
            eye_width,
            eye_height
        )
        
        # Gradiente para el globo ocular
        eye_gradient = QRadialGradient(center_x, center_y, eye_width/2)
        eye_gradient.setColorAt(0.0, QColor(255, 255, 255))
        eye_gradient.setColorAt(0.7, QColor(240, 240, 240))
        eye_gradient.setColorAt(1.0, QColor(224, 224, 224))
        
        # Dibujar el globo ocular
        painter.setPen(QPen(QColor(136, 136, 136), size * 0.01))
        painter.setBrush(eye_gradient)
        painter.drawEllipse(eye_rect)
        
        # Dibujar el iris
        iris_radius = size * 0.17
        iris_gradient = QRadialGradient(center_x, center_y, iris_radius)
        iris_gradient.setColorAt(0.0, QColor(90, 138, 198))
        iris_gradient.setColorAt(0.7, QColor(58, 99, 150))
        iris_gradient.setColorAt(1.0, QColor(26, 67, 118))
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(iris_gradient)
        painter.drawEllipse(QPointF(center_x, center_y), iris_radius, iris_radius)
        
        # Dibujar la pupila
        pupil_radius = size * 0.065
        painter.setBrush(QColor(0, 0, 0))
        painter.drawEllipse(QPointF(center_x, center_y), pupil_radius, pupil_radius)
        
        # Dibujar el brillo del ojo
        highlight_radius = size * 0.045
        painter.setBrush(QColor(255, 255, 255, 180))
        painter.drawEllipse(QPointF(center_x - iris_radius * 0.4, center_y - iris_radius * 0.3), 
                           highlight_radius, highlight_radius)
        
        # Añadir sombra sutil alrededor del ojo
        shadow_path = QPainterPath()
        shadow_path.addEllipse(eye_rect.adjusted(-1, -1, 1, 1))
        shadow_pen = QPen(QColor(0, 0, 0, 30), size * 0.01)
        painter.setPen(shadow_pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawPath(shadow_path)
        
        # Dibujar la línea diagonal (tachado) - SOMBRA
        shadow_pen = QPen(QColor(180, 0, 0), size * 0.08)
        shadow_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(shadow_pen)
        painter.drawLine(QPointF(size * 0.15, size * 0.15), QPointF(size * 0.85, size * 0.85))
        
        # Dibujar la línea diagonal (tachado) - LÍNEA PRINCIPAL
        line_pen = QPen(QColor(230, 0, 0), size * 0.06)
        line_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(line_pen)
        painter.drawLine(QPointF(size * 0.15, size * 0.15), QPointF(size * 0.85, size * 0.85))
        
        # Brillo en la línea
        highlight_pen = QPen(QColor(255, 150, 150, 100), size * 0.02)
        highlight_pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(highlight_pen)
        painter.drawLine(QPointF(size * 0.18, size * 0.18), QPointF(size * 0.82, size * 0.82))
        
    finally:
        if painter.isActive():
            painter.end()
    
    return QIcon(pixmap)

def icono_MATRIX(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256"><path fill="#f28352" d="M24 80a8 8 0 0 1 4-6.91l96-56a8 8 0 0 1 8.06 0l96 56a8 8 0 0 1 0 13.82l-96 56a8 8 0 0 1-8.06 0l-96-56A8 8 0 0 1 24 80m196 41.09l-92 53.65l-92-53.65a8 8 0 0 0-8 13.82l96 56a8 8 0 0 0 8.06 0l96-56a8 8 0 1 0-8.06-13.82M232 192h-16v-16a8 8 0 0 0-16 0v16h-16a8 8 0 0 0 0 16h16v16a8 8 0 0 0 16 0v-16h16a8 8 0 0 0 0-16m-92 23.76l-12 7l-92-53.67a8 8 0 0 0-8 13.82l96 56a8 8 0 0 0 8.06 0l16-9.33a8 8 0 1 0-8.06-13.82"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_OTROS(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g fill="none"><path fill="url(#fluentColorDocument320)" d="M17 2H8a3 3 0 0 0-3 3v22a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V12l-7-3z"/><path fill="url(#fluentColorDocument322)" fill-opacity="0.5" d="M17 2H8a3 3 0 0 0-3 3v22a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V12l-7-3z"/><path fill="url(#fluentColorDocument321)" d="M17 10V2l10 10h-8a2 2 0 0 1-2-2"/><defs><linearGradient id="fluentColorDocument320" x1="20.4" x2="22.711" y1="2" y2="25.61" gradientUnits="userSpaceOnUse"><stop stop-color="#6ce0ff"/><stop offset="1" stop-color="#4894fe"/></linearGradient><linearGradient id="fluentColorDocument321" x1="21.983" x2="19.483" y1="6.167" y2="10.333" gradientUnits="userSpaceOnUse"><stop stop-color="#9ff0f9"/><stop offset="1" stop-color="#b3e0ff"/></linearGradient><radialGradient id="fluentColorDocument322" cx="0" cy="0" r="1" gradientTransform="rotate(133.108 13.335 7.491)scale(17.438 10.2853)" gradientUnits="userSpaceOnUse"><stop offset=".362" stop-color="#4a43cb"/><stop offset="1" stop-color="#4a43cb" stop-opacity="0"/></radialGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_TELEGRAM(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256"><defs><linearGradient id="logosTelegram0" x1="50%" x2="50%" y1="0%" y2="100%"><stop offset="0%" stop-color="#2aabee"/><stop offset="100%" stop-color="#229ed9"/></linearGradient></defs><path fill="url(#logosTelegram0)" d="M128 0C94.06 0 61.48 13.494 37.5 37.49A128.04 128.04 0 0 0 0 128c0 33.934 13.5 66.514 37.5 90.51C61.48 242.506 94.06 256 128 256s66.52-13.494 90.5-37.49c24-23.996 37.5-56.576 37.5-90.51s-13.5-66.514-37.5-90.51C194.52 13.494 161.94 0 128 0"/><path fill="#fff" d="M57.94 126.648q55.98-24.384 74.64-32.152c35.56-14.786 42.94-17.354 47.76-17.441c1.06-.017 3.42.245 4.96 1.49c1.28 1.05 1.64 2.47 1.82 3.467c.16.996.38 3.266.2 5.038c-1.92 20.24-10.26 69.356-14.5 92.026c-1.78 9.592-5.32 12.808-8.74 13.122c-7.44.684-13.08-4.912-20.28-9.63c-11.26-7.386-17.62-11.982-28.56-19.188c-12.64-8.328-4.44-12.906 2.76-20.386c1.88-1.958 34.64-31.748 35.26-34.45c.08-.338.16-1.598-.6-2.262c-.74-.666-1.84-.438-2.64-.258c-1.14.256-19.12 12.152-54 35.686c-5.1 3.508-9.72 5.218-13.88 5.128c-4.56-.098-13.36-2.584-19.9-4.708c-8-2.606-14.38-3.984-13.82-8.41c.28-2.304 3.46-4.662 9.52-7.072"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_RED(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g fill="none"><path fill="url(#fluentColorOrg246)" fill-rule="evenodd" d="M11 11.5v-3h1.5v3h3.25A2.25 2.25 0 0 1 18 13.75V16h-1.5v-2.25a.75.75 0 0 0-.75-.75h-8a.75.75 0 0 0-.75.75V16H5.5v-2.25a2.25 2.25 0 0 1 2.25-2.25z" clip-rule="evenodd"/><path fill="url(#fluentColorOrg240)" fill-rule="evenodd" d="M11 11.5v-3h1.5v3h3.25A2.25 2.25 0 0 1 18 13.75V16h-1.5v-2.25a.75.75 0 0 0-.75-.75h-8a.75.75 0 0 0-.75.75V16H5.5v-2.25a2.25 2.25 0 0 1 2.25-2.25z" clip-rule="evenodd"/><path fill="url(#fluentColorOrg241)" fill-rule="evenodd" d="M11 11.5v-3h1.5v3h3.25A2.25 2.25 0 0 1 18 13.75V16h-1.5v-2.25a.75.75 0 0 0-.75-.75h-8a.75.75 0 0 0-.75.75V16H5.5v-2.25a2.25 2.25 0 0 1 2.25-2.25z" clip-rule="evenodd"/><path fill="url(#fluentColorOrg242)" fill-rule="evenodd" d="M11 11.5v-3h1.5v3h3.25A2.25 2.25 0 0 1 18 13.75V16h-1.5v-2.25a.75.75 0 0 0-.75-.75h-8a.75.75 0 0 0-.75.75V16H5.5v-2.25a2.25 2.25 0 0 1 2.25-2.25z" clip-rule="evenodd"/><path fill="url(#fluentColorOrg243)" d="M10 18.25a3.75 3.75 0 1 1-7.5 0a3.75 3.75 0 0 1 7.5 0"/><path fill="url(#fluentColorOrg244)" d="M21 18.25a3.75 3.75 0 1 1-7.5 0a3.75 3.75 0 0 1 7.5 0"/><path fill="url(#fluentColorOrg245)" d="M15.5 5.75a3.75 3.75 0 1 1-7.5 0a3.75 3.75 0 0 1 7.5 0"/><defs><radialGradient id="fluentColorOrg240" cx="0" cy="0" r="1" gradientTransform="matrix(0 4.5613 -7.60216 0 11.75 6.246)" gradientUnits="userSpaceOnUse"><stop offset=".553" stop-color="#70777d"/><stop offset="1" stop-color="#70777d" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorOrg241" cx="0" cy="0" r="1" gradientTransform="matrix(-.01956 -4.66947 7.78243 -.0326 6.125 17.75)" gradientUnits="userSpaceOnUse"><stop offset=".549" stop-color="#70777d"/><stop offset="1" stop-color="#70777d" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorOrg242" cx="0" cy="0" r="1" gradientTransform="matrix(.03906 -4.61538 7.69222 .0651 17.355 17.73)" gradientUnits="userSpaceOnUse"><stop offset=".549" stop-color="#70777d"/><stop offset="1" stop-color="#70777d" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorOrg243" cx="0" cy="0" r="1" gradientTransform="rotate(53.616 -7.092 1.06)scale(20.282 17.3706)" gradientUnits="userSpaceOnUse"><stop offset=".529" stop-color="#1ec8b0"/><stop offset="1" stop-color="#1a7f7c"/></radialGradient><radialGradient id="fluentColorOrg244" cx="0" cy="0" r="1" gradientTransform="rotate(53.616 -1.592 11.945)scale(20.282 17.3706)" gradientUnits="userSpaceOnUse"><stop offset=".529" stop-color="#7b7bff"/><stop offset="1" stop-color="#4a43cb"/></radialGradient><radialGradient id="fluentColorOrg245" cx="0" cy="0" r="1" gradientTransform="rotate(53.616 8.027 .253)scale(20.282 17.3706)" gradientUnits="userSpaceOnUse"><stop offset=".529" stop-color="#0fafff"/><stop offset="1" stop-color="#0067bf"/></radialGradient><linearGradient id="fluentColorOrg246" x1="5.5" x2="7.236" y1="8.5" y2="16.765" gradientUnits="userSpaceOnUse"><stop stop-color="#b9c0c7"/><stop offset="1" stop-color="#70777d"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_MOBILES(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64"><path fill="#5c6d6d" d="M50 4.1c0-2-2.1-4.1-4.3-4.1H18.3C16.1 0 14 2.1 14 4.1v55.7c0 2.1 2.1 4.1 4.3 4.1h27.5c2.1 0 4.3-2.1 4.3-4.1V4.1z"/><path fill="#212528" d="M49 59c0 2-2 4-4 4H19c-2 0-4-2-4-4V5c0-2 2-4 4-4h26c2 0 4 2 4 4z"/><g fill="#94989b"><circle cx="43.5" cy="4.5" r="1"/><path d="M35 4.5c0 .3-.1.5-.3.5h-5.4c-.2 0-.3-.2-.3-.5c0-.2.1-.5.3-.5h5.4c.2 0 .3.3.3.5"/></g><path fill="#3e4347" d="M17 8h30v48H17z"/><path fill="#94989b" d="M35.8 60.2c0 .4-.3.8-.8.8h-6c-.4 0-.8-.3-.8-.8v-1.5c0-.4.3-.8.8-.8h6c.4 0 .8.3.8.8z"/><path fill="#42ade2" d="M24 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M31 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#f2b200" d="M38 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M45 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M24 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#ff435e" d="M31 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M38 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M45 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M24 33.9c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M31 33.9c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M38 33.9c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#ff435e" d="M24 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#f2b200" d="M31 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M38 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#f2b200" d="M45 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M24 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M31 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#ff435e" d="M38 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M45 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_ram_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48"><defs><mask id="ipSElectrocardiogram0"><g fill="none" stroke-linejoin="round" stroke-width="4"><path fill="#fff" stroke="#fff" d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"/><path stroke="#000" stroke-linecap="round" d="M11 28.132h5.685L21.223 13l3.672 22l4.553-10.382l3.465 3.514H37"/></g></mask></defs><path fill="#459cff" d="M0 0h48v48H0z" mask="url(#ipSElectrocardiogram0)"/></svg>'''
    return crear_icono_svg(svg_data, size)

def create_ram_warning_icon(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48"><defs><mask id="ipSElectrocardiogram0"><g fill="none" stroke-linejoin="round" stroke-width="4"><path fill="#fff" stroke="#fff" d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"/><path stroke="#000" stroke-linecap="round" d="M11 28.132h5.685L21.223 13l3.672 22l4.553-10.382l3.465 3.514H37"/></g></mask></defs><path fill="#ff4545" d="M0 0h48v48H0z" mask="url(#ipSElectrocardiogram0)"/></svg>'''
    return crear_icono_svg(svg_data, size)

def Logo_Zetacopy(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 24 24">
  <defs>
    <!-- Gradiente para efecto 3D del rombo -->
    <radialGradient id="bevel3d" cx="30%" cy="30%" r="70%">
      <stop offset="0%" stop-color="#ff7a59" />
      <stop offset="30%" stop-color="#e85d42" />
      <stop offset="70%" stop-color="#d95336" />
      <stop offset="100%" stop-color="#b8432a" />
    </radialGradient>

    <!-- Sombra más sutil para la Z, ajustada para no invadir el fondo -->
    <filter id="innerGlowZ" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="0.8" result="blur"/> <!-- Reducido de 1.2 a 0.8 -->
      <feOffset dx="0" dy="0.5" result="offset"/> <!-- Reducido de 0.8 a 0.5 -->
      <feMerge>
        <feMergeNode in="offset"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Rombo principal con gradiente brillante -->
  <path fill="url(#bevel3d)"
        d="m13.666 1.429l6.75 3.98l.096.063l.093.078l.106.074a3.22 3.22 0 0 1 1.284 2.39l.005.204v7.284c0 1.175-.643 2.256-1.623 2.793l-6.804 4.302c-.98.538-2.166.538-3.2-.032l-6.695-4.237A3.23 3.23 0 0 1 2 15.502V8.217c0-1.106.57-2.128 1.476-2.705l6.95-4.098c1-.552 2.214-.552 3.24.015"/>

  <!-- Z flotante con sombra ligera -->
  <path fill="white" filter="url(#innerGlowZ)"
        d="M14 7h-4a1 1 0 0 0-1 1l.007.117A1 1 0 0 0 10 9h2.382l-3.276 6.553A1 1 0 0 0 10 17h4a1 1 0 0 0 1-1l-.007-.117A1 1 0 0 0 14 15h-2.382l3.276-6.553A1 1 0 0 0 14 7"/>
</svg>'''
    return crear_icono_svg(svg_data, size)

def Logo_Zetacopy_azul(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#4292d4" d="M3.25 11.5a8.25 8.25 0 1 1 14.578 5.294l2.675 2.676a.75.75 0 0 1-1.06 1.06l-2.678-2.678A8.25 8.25 0 0 1 3.25 11.5m7-2.75a.75.75 0 0 0 0 1.5h1.147l-1.783 2.852a.75.75 0 0 0 .636 1.148h2.5a.75.75 0 0 0 0-1.5h-1.147l1.783-2.852a.75.75 0 0 0-.636-1.148z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_WhatsApp(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="95.26" height="96" viewBox="0 0 256 258"><defs><linearGradient id="logosWhatsappIcon0" x1="50%" x2="50%" y1="100%" y2="0%"><stop offset="0%" stop-color="#1faf38"/><stop offset="100%" stop-color="#60d669"/></linearGradient><linearGradient id="logosWhatsappIcon1" x1="50%" x2="50%" y1="100%" y2="0%"><stop offset="0%" stop-color="#f9f9f9"/><stop offset="100%" stop-color="#fff"/></linearGradient></defs><path fill="url(#logosWhatsappIcon0)" d="M5.463 127.456c-.006 21.677 5.658 42.843 16.428 61.499L4.433 252.697l65.232-17.104a123 123 0 0 0 58.8 14.97h.054c67.815 0 123.018-55.183 123.047-123.01c.013-32.867-12.775-63.773-36.009-87.025c-23.23-23.25-54.125-36.061-87.043-36.076c-67.823 0-123.022 55.18-123.05 123.004"/><path fill="url(#logosWhatsappIcon1)" d="M1.07 127.416c-.007 22.457 5.86 44.38 17.014 63.704L0 257.147l67.571-17.717c18.618 10.151 39.58 15.503 60.91 15.511h.055c70.248 0 127.434-57.168 127.464-127.423c.012-34.048-13.236-66.065-37.3-90.15C194.633 13.286 162.633.014 128.536 0C58.276 0 1.099 57.16 1.071 127.416m40.24 60.376l-2.523-4.005c-10.606-16.864-16.204-36.352-16.196-56.363C22.614 69.029 70.138 21.52 128.576 21.52c28.3.012 54.896 11.044 74.9 31.06c20.003 20.018 31.01 46.628 31.003 74.93c-.026 58.395-47.551 105.91-105.943 105.91h-.042c-19.013-.01-37.66-5.116-53.922-14.765l-3.87-2.295l-40.098 10.513z"/><path fill="#fff" d="M96.678 74.148c-2.386-5.303-4.897-5.41-7.166-5.503c-1.858-.08-3.982-.074-6.104-.074c-2.124 0-5.575.799-8.492 3.984c-2.92 3.188-11.148 10.892-11.148 26.561s11.413 30.813 13.004 32.94c1.593 2.123 22.033 35.307 54.405 48.073c26.904 10.609 32.379 8.499 38.218 7.967c5.84-.53 18.844-7.702 21.497-15.139c2.655-7.436 2.655-13.81 1.859-15.142c-.796-1.327-2.92-2.124-6.105-3.716s-18.844-9.298-21.763-10.361c-2.92-1.062-5.043-1.592-7.167 1.597c-2.124 3.184-8.223 10.356-10.082 12.48c-1.857 2.129-3.716 2.394-6.9.801c-3.187-1.598-13.444-4.957-25.613-15.806c-9.468-8.442-15.86-18.867-17.718-22.056c-1.858-3.184-.199-4.91 1.398-6.497c1.431-1.427 3.186-3.719 4.78-5.578c1.588-1.86 2.118-3.187 3.18-5.311c1.063-2.126.531-3.986-.264-5.579c-.798-1.593-6.987-17.343-9.819-23.64"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_Discos(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 16 16"><g fill="none"><path fill="url(#fluentColorListBar160)" d="M5 14h7.75c.69 0 1.25-.56 1.25-1.25v-.5c0-.69-.56-1.25-1.25-1.25H5l-.5 1.5zm0-4h7.75c.69 0 1.25-.56 1.25-1.25v-1.5C14 6.56 13.44 6 12.75 6H5l-.5 2zm0-5h7.75C13.44 5 14 4.44 14 3.75v-.5C14 2.56 13.44 2 12.75 2H5l-.5 1.5z"/><path fill="url(#fluentColorListBar161)" d="M5 6v4H3.25C2.56 10 2 9.44 2 8.75v-1.5C2 6.56 2.56 6 3.25 6zm0-4v3H3.25C2.56 5 2 4.44 2 3.75v-.5C2 2.56 2.56 2 3.25 2zm0 9v3H3.25C2.56 14 2 13.44 2 12.75v-.5c0-.69.56-1.25 1.25-1.25z"/><defs><linearGradient id="fluentColorListBar160" x1="3.075" x2="13.243" y1=".286" y2="13.855" gradientUnits="userSpaceOnUse"><stop stop-color="#36dff1"/><stop offset="1" stop-color="#0094f0"/></linearGradient><linearGradient id="fluentColorListBar161" x1="2.713" x2="7.395" y1="3.595" y2="5.464" gradientUnits="userSpaceOnUse"><stop offset=".125" stop-color="#9c6cfe"/><stop offset="1" stop-color="#7a41dc"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_RELOJ(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 20 20"><g fill="none"><path fill="url(#fluentColorClockAlarm200)" d="M16.231 8.144a3.25 3.25 0 1 0-4.375-4.375a6.51 6.51 0 0 1 4.375 4.375"/><path fill="url(#fluentColorClockAlarm207)" d="M16.231 8.144a3.25 3.25 0 1 0-4.375-4.375a6.51 6.51 0 0 1 4.375 4.375"/><path fill="url(#fluentColorClockAlarm201)" d="M8.144 3.769a6.51 6.51 0 0 0-4.375 4.375a3.25 3.25 0 1 1 4.375-4.375"/><path fill="url(#fluentColorClockAlarm208)" d="M8.144 3.769a6.51 6.51 0 0 0-4.375 4.375a3.25 3.25 0 1 1 4.375-4.375"/><path fill="url(#fluentColorClockAlarm202)" d="m3.854 16.854l2-2l-.708-.707l-2 2a.5.5 0 1 0 .708.707"/><path fill="url(#fluentColorClockAlarm203)" d="m16.854 16.147l-2-2l-.708.707l2 2a.5.5 0 0 0 .708-.707"/><path fill="url(#fluentColorClockAlarm204)" d="M17 10a7 7 0 1 1-14 0a7 7 0 0 1 14 0"/><path fill="url(#fluentColorClockAlarm205)" d="M15.75 10a5.75 5.75 0 1 1-11.5 0a5.75 5.75 0 0 1 11.5 0"/><path fill="url(#fluentColorClockAlarm206)" fill-rule="evenodd" d="M9.5 6a.5.5 0 0 1 .5.5V10h2.5a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5v-4a.5.5 0 0 1 .5-.5" clip-rule="evenodd"/><defs><linearGradient id="fluentColorClockAlarm200" x1="18.009" x2="12.174" y1="8.141" y2="3.368" gradientUnits="userSpaceOnUse"><stop stop-color="#ff6f47"/><stop offset="1" stop-color="#ffcd0f"/></linearGradient><linearGradient id="fluentColorClockAlarm201" x1="8.152" x2="2.317" y1="8.141" y2="3.368" gradientUnits="userSpaceOnUse"><stop stop-color="#ff6f47"/><stop offset="1" stop-color="#ffcd0f"/></linearGradient><linearGradient id="fluentColorClockAlarm202" x1="3" x2="3.547" y1="12.921" y2="17.946" gradientUnits="userSpaceOnUse"><stop stop-color="#cad2d9"/><stop offset="1" stop-color="#70777d"/></linearGradient><linearGradient id="fluentColorClockAlarm203" x1="3" x2="3.547" y1="12.921" y2="17.946" gradientUnits="userSpaceOnUse"><stop stop-color="#cad2d9"/><stop offset="1" stop-color="#70777d"/></linearGradient><linearGradient id="fluentColorClockAlarm204" x1="5.333" x2="12.333" y1="2.222" y2="17.778" gradientUnits="userSpaceOnUse"><stop stop-color="#1ec8b0"/><stop offset="1" stop-color="#2764e7"/></linearGradient><linearGradient id="fluentColorClockAlarm205" x1="6" x2="13.5" y1="3" y2="19" gradientUnits="userSpaceOnUse"><stop stop-color="#fdfdfd"/><stop offset="1" stop-color="#dedeff"/></linearGradient><linearGradient id="fluentColorClockAlarm206" x1="10" x2="12.358" y1="6.278" y2="10.207" gradientUnits="userSpaceOnUse"><stop stop-color="#1ec8b0"/><stop offset="1" stop-color="#2764e7"/></linearGradient><radialGradient id="fluentColorClockAlarm207" cx="0" cy="0" r="1" gradientTransform="matrix(6.5 -6.5 6.5 6.5 10 10)" gradientUnits="userSpaceOnUse"><stop offset=".644" stop-color="#ff6f47"/><stop offset=".942" stop-color="#ff6f47" stop-opacity="0"/></radialGradient><radialGradient id="fluentColorClockAlarm208" cx="0" cy="0" r="1" gradientTransform="matrix(-6.5 -6.5 6.5 -6.5 10 10)" gradientUnits="userSpaceOnUse"><stop offset=".659" stop-color="#ff6f47"/><stop offset=".949" stop-color="#ff6f47" stop-opacity="0"/></radialGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_Ficheros_copia(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#4a9bff" d="M15 4H5v16h14V8h-4V4zM3 2.992C3 2.444 3.447 2 3.999 2H16l5 5v13.993A1 1 0 0 1 20.007 22H3.993A1 1 0 0 1 3 21.008V2.992zM11 11V8h2v3h3v2h-3v3h-2v-3H8v-2h3z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def Icono_BUSCADOR(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="#4292d4" d="M3.25 11.5a8.25 8.25 0 1 1 14.578 5.294l2.675 2.676a.75.75 0 0 1-1.06 1.06l-2.678-2.678A8.25 8.25 0 0 1 3.25 11.5m7-2.75a.75.75 0 0 0 0 1.5h1.147l-1.783 2.852a.75.75 0 0 0 .636 1.148h2.5a.75.75 0 0 0 0-1.5h-1.147l1.783-2.852a.75.75 0 0 0-.636-1.148z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def Icono_MOBILES(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 64 64"><path fill="#5c6d6d" d="M50 4.1c0-2-2.1-4.1-4.3-4.1H18.3C16.1 0 14 2.1 14 4.1v55.7c0 2.1 2.1 4.1 4.3 4.1h27.5c2.1 0 4.3-2.1 4.3-4.1V4.1z"/><path fill="#212528" d="M49 59c0 2-2 4-4 4H19c-2 0-4-2-4-4V5c0-2 2-4 4-4h26c2 0 4 2 4 4z"/><g fill="#94989b"><circle cx="43.5" cy="4.5" r="1"/><path d="M35 4.5c0 .3-.1.5-.3.5h-5.4c-.2 0-.3-.2-.3-.5c0-.2.1-.5.3-.5h5.4c.2 0 .3.3.3.5"/></g><path fill="#3e4347" d="M17 8h30v48H17z"/><path fill="#94989b" d="M35.8 60.2c0 .4-.3.8-.8.8h-6c-.4 0-.8-.3-.8-.8v-1.5c0-.4.3-.8.8-.8h6c.4 0 .8.3.8.8z"/><path fill="#42ade2" d="M24 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M31 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#f2b200" d="M38 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M45 14.7c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M24 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#ff435e" d="M31 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M38 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M45 53.2c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M24 33.9c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M31 33.9c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M38 33.9c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#ff435e" d="M24 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#f2b200" d="M31 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c7e755" d="M38 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#f2b200" d="M45 27.5c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M24 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#42ade2" d="M31 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#ff435e" d="M38 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/><path fill="#c28fef" d="M45 21.1c0 .5-.4.8-.8.8h-3.3c-.5 0-.8-.4-.8-.8v-3.3c0-.5.4-.8.8-.8h3.3c.5 0 .8.4.8.8z"/></svg>'''
    return crear_icono_svg(svg_data, size)
class DeleteButton(QPushButton):
    """Botón de eliminación con icono de cruz roja"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(16, 16)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # Definir colores directamente sin usar ButtonColors
        self.normal_color = QColor(220, 50, 50, 0)  # Transparente en estado normal
        self.hover_color = QColor(220, 50, 50, 80)  # Rojo semitransparente al pasar el mouse
        self.pressed_color = QColor(220, 50, 50, 120)  # Rojo más intenso al presionar
        
        # Crear tooltip personalizado
        self.tooltip = CustomToolTip("Eliminar", self)
        self.tooltip.hide()
        
        # Instalar filtro de eventos para el tooltip
        self.installEventFilter(self)
        
        # Estado del botón
        self._hovered = False
        self._pressed = False
    
    def eventFilter(self, obj, event):
        """Filtro de eventos para mostrar/ocultar tooltip"""
        if obj == self:
            if event.type() == QEvent.Type.Enter:
                # Mostrar tooltip cuando el mouse entra
                pos = self.mapToGlobal(QPoint(0, self.height()))
                self.tooltip.move(pos.x() - 10, pos.y() + 5)
                self.tooltip.show()
            elif event.type() == QEvent.Type.Leave:
                # Ocultar tooltip cuando el mouse sale
                self.tooltip.hide()
        return super().eventFilter(obj, event)
    
    def hideTooltip(self):
        """Oculta el tooltip personalizado"""
        if hasattr(self, 'tooltip'):
            self.tooltip.hide()
    
    def enterEvent(self, event):
        """Maneja el evento cuando el mouse entra en el botón"""
        self._hovered = True
        self.update()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Maneja el evento cuando el mouse sale del botón"""
        self._hovered = False
        self.update()
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Maneja el evento cuando se presiona el botón"""
        if event.button() == Qt.MouseButton.LeftButton:
            self._pressed = True
            self.update()
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """Maneja el evento cuando se suelta el botón"""
        if event.button() == Qt.MouseButton.LeftButton:
            self._pressed = False
            self.update()
        super().mouseReleaseEvent(event)
    
    def paintEvent(self, event):
        """Dibuja el botón con el icono de cruz roja"""
        painter = QPainter()
        painter.begin(self)
        
        # Dibujar el fondo según el estado
        if self._pressed:
            color = self.pressed_color
        elif self._hovered:
            color = self.hover_color
        else:
            color = self.normal_color
        
        # Dibujar el fondo redondeado
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(color)
        painter.drawRoundedRect(self.rect(), 8, 8)
        
        # Dibujar la cruz (X)
        pen = QPen(QColor(220, 50, 50), 2.0)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        
        margin = 4
        painter.drawLine(margin, margin, self.width()-margin, self.height()-margin)
        painter.drawLine(self.width()-margin, margin, margin, self.height()-margin)
        
        painter.end()
def icono_TRANSFERENCIA(size=16):
    """
    Icono azul de transferencia para mostrar antes del total de GB a copiar
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 1792 1792"><path fill="#1a8e43" d="M336 0h1120q139 0 237.5 98.5T1792 336v1120q0 139-98.5 237.5T1456 1792H336q-139 0-237.5-98.5T0 1456V336Q0 197 98.5 98.5T336 0m1082 299H374q-31 0-53 22t-22 53v149q0 31 22 52.5t53 21.5h373v821q0 31 22 53t53 22h149q31 0 52.5-22t21.5-53V597h373q31 0 53-21.5t22-52.5V374q0-31-22-53t-53-22"/></svg>'''
    return crear_icono_svg(svg_data, size)
def icono_total(size=16):
    """Crea un icono de Total con forma de embudo/tolva relleno en color rojo"""
    svg_data = """<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 48 48">
        <path fill="#dc2626" stroke="#dc2626" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M5.5 8.832h37l-4.986 8.761h-8.342v13.374L24 39.168l-5.172-8.201V17.593h-8.341z"/>
    </svg>"""
    return crear_icono_svg(svg_data, size)