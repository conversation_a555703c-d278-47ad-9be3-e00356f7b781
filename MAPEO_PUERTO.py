import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import subprocess
import json
import os
import threading
import time
from typing import Dict, List, Optional, Tuple

# Importaciones para PyQt6 (diálogo moderno)
from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMainWindow
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPainter, QBrush, QColor, QPen, QPainterPath
import sys
import ctypes

class USBPortMapper:
    def __init__(self):
        self.port_names_file = "usb_port_names.json"
        self.port_names = self.load_port_names()
        
    def load_port_names(self) -> Dict[str, str]:
        """Cargar nombres personalizados de puertos desde archivo JSON"""
        try:
            if os.path.exists(self.port_names_file):
                with open(self.port_names_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error cargando nombres de puertos: {e}")
        return {}
    
    def save_port_names(self):
        """Guardar nombres personalizados de puertos"""
        try:
            with open(self.port_names_file, 'w', encoding='utf-8') as f:
                json.dump(self.port_names, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error guardando nombres de puertos: {e}")

    def get_usb_devices(self) -> List[Dict]:
        """Obtener información de dispositivos USB usando PowerShell mejorado"""
        powershell_script = '''
Clear-Host
Write-Host "=== DISCOS USB CONECTADOS (INFORMACION DE PUERTO FISICO) ===" -ForegroundColor Green
Write-Host ""

# Obtener todos los dispositivos USB que sean controladores o hubs para poder rastrear conexiones
$usbControllers = Get-WmiObject Win32_USBController
$usbHubs = Get-PnpDevice -Class USB -Status OK | Where-Object { $_.FriendlyName -like "*USB Root Hub*" -or $_.FriendlyName -like "*Generic USB Hub*" }

# Obtener discos físicos USB
$usbDisks = Get-WmiObject Win32_DiskDrive | Where-Object { $_.InterfaceType -eq "USB" }

if ($usbDisks.Count -eq 0) {
    Write-Host "No se encontraron discos USB conectados." -ForegroundColor Red
    return @()
}

$results = @()
$exportLines = @()

foreach ($disk in $usbDisks) {
    Write-Host "Modelo: " -ForegroundColor Cyan -NoNewline
    Write-Host $disk.Model -ForegroundColor White

    # Obtener letra de unidad
    $driveLetters = @()
    $partitions = Get-WmiObject Win32_DiskPartition | Where-Object { $_.DiskIndex -eq $disk.Index }
    foreach ($partition in $partitions) {
        $logicalDisks = Get-WmiObject Win32_LogicalDiskToPartition | Where-Object {
            $_.Antecedent -like "*$($partition.DeviceID.Replace('\\','\\\\'))*"
        }
        foreach ($logicalDisk in $logicalDisks) {
            $driveLetter = $logicalDisk.Dependent.Split('"')[1]
            $driveLetters += $driveLetter
        }
    }

    Write-Host "Letra: " -ForegroundColor Green -NoNewline
    if ($driveLetters.Count -gt 0) {
        Write-Host ($driveLetters -join ", ") -ForegroundColor Yellow
    } else {
        Write-Host "Sin asignar" -ForegroundColor Red
    }

    $portInfo = "No disponible"
    $pnpDeviceID = $disk.PNPDeviceID

    # Usar Get-PnpDevice para obtener la información más detallada del dispositivo USB
    # y su padre para rastrear el camino físico
    $pnpDevice = Get-PnpDevice -InstanceId $pnpDeviceID -ErrorAction SilentlyContinue
    if ($pnpDevice) {
        # Recorrer la cadena de padres hasta encontrar un hub o controlador USB
        $parent = $pnpDevice
        $pathParts = @($pnpDevice.FriendlyName) # Empezar con el nombre del dispositivo
        while ($parent) {
            $parentInstanceId = (Get-PnpDeviceProperty -InstanceId $parent.InstanceId -KeyName "DEVPKEY_Device_Parent").Data
            if (-not $parentInstanceId) {
                break # No more parents
            }
            $parent = Get-PnpDevice -InstanceId $parentInstanceId -ErrorAction SilentlyContinue
            if ($parent) {
                $pathParts += $parent.FriendlyName
                # Si el padre es un hub o controlador, podemos intentar obtener la LocationInformation
                try {
                    $regPath = "HKLM:\\SYSTEM\\CurrentControlSet\\Enum\\$($parent.InstanceId)"
                    $regInfo = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
                    if ($regInfo.LocationInformation) {
                        $portInfo = $regInfo.LocationInformation
                        break # Found location information
                    }
                } catch {
                    # Handle error if registry path not found or permission issue
                }
            }
        }

        # Si no se encontró en la cadena de padres, intentar directamente con el dispositivo
        if ($portInfo -eq "No disponible" -and $pnpDevice.InstanceId) {
            try {
                $regPath = "HKLM:\\SYSTEM\\CurrentControlSet\\Enum\\$($pnpDevice.InstanceId)"
                $regInfo = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
                if ($regInfo.LocationInformation) {
                    $portInfo = $regInfo.LocationInformation
                }
            } catch { }
        }
    }

    Write-Host "Puerto: " -ForegroundColor Gray -NoNewline
    Write-Host $portInfo -ForegroundColor White
    Write-Host "Debug - PNP ID del disco: " -ForegroundColor DarkGray -NoNewline
    Write-Host $disk.PNPDeviceID -ForegroundColor DarkGray
    Write-Host ""

    # Extraer información del hub
    $hubInfo = "No disponible"
    if ($portInfo -match "Port_#(\\d+)\\.Hub_#(\\d+)") {
        $hubInfo = "Hub $($matches[2])"
    }

    # Crear líneas para exportar al archivo PUERTOS_USB.txt
    foreach ($driveLetter in $driveLetters) {
        if ($driveLetter -and $portInfo -ne "No disponible") {
            $exportLines += "$($driveLetter):=$portInfo|$hubInfo"
        }
    }

    # Identificar marca del disco
    $diskBrand = "Unknown"
    if ($disk.Model -like "*WD*" -or $disk.Model -like "*Western*") {
        $diskBrand = "WD"
    } elseif ($disk.Model -like "*Kingston*") {
        $diskBrand = "Kingston"
    } elseif ($disk.Model -like "*Samsung*") {
        $diskBrand = "Samsung"
    } elseif ($disk.Model -like "*SanDisk*") {
        $diskBrand = "SanDisk"
    } elseif ($disk.Model -like "*Toshiba*") {
        $diskBrand = "Toshiba"
    } elseif ($disk.Model -like "*Seagate*") {
        $diskBrand = "Seagate"
    }

    $deviceInfo = @{
        Model = $disk.Model
        DriveLetters = $driveLetters -join ", "
        PortInfo = $portInfo
        HubInfo = $hubInfo
        PNPDeviceID = $disk.PNPDeviceID
        Brand = $diskBrand
        Size = [math]::Round($disk.Size / 1GB, 2)
    }

    $results += $deviceInfo
}

# Exportar a archivo PUERTOS_USB.txt
if ($exportLines.Count -gt 0) {
    $exportLines | Out-File -FilePath "PUERTOS_USB.txt" -Encoding UTF8
}

# Convertir a JSON
$results | ConvertTo-Json -Depth 3
'''
        
        try:
            # Configurar para ocultar la consola
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            result = subprocess.run(
                ["powershell", "-Command", powershell_script],
                capture_output=True,
                text=True,
                encoding='utf-8',
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0 and result.stdout.strip():
                devices_data = json.loads(result.stdout.strip())
                # Si solo hay un dispositivo, PowerShell no devuelve una lista
                if isinstance(devices_data, dict):
                    devices_data = [devices_data]
                return devices_data
            else:
                print(f"Error en PowerShell: {result.stderr}")
                return []
                
        except Exception as e:
            print(f"Error ejecutando PowerShell: {e}")
            return []

def get_port_info_with_usbdeview_fast(drive_letter: str) -> Tuple[str, str]:
    """Método rápido usando USBDeview para obtener información de puerto"""
    import tempfile
    import csv
    import re

    try:
        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            temp_csv = f.name

        try:
            # Ejecutar USBDeview rápidamente
            cmd = [
                "USBDeview.exe",
                "/DisplayDisconnected", "0",  # Solo conectados
                "/scomma", temp_csv
            ]

            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10  # Timeout corto
            )

            if result.returncode == 0 and os.path.exists(temp_csv):
                # Parsear CSV rápidamente
                with open(temp_csv, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Buscar línea que contenga nuestra letra de unidad
                for line in content.split('\n'):
                    if not line.strip():
                        continue

                    # Verificar si contiene nuestra letra de unidad
                    if f'"{drive_letter.upper()}:"' in line or f'{drive_letter.upper()}:' in line:
                        # Esta línea contiene nuestro dispositivo
                        parts = line.split(',')
                        if len(parts) > 35:  # Asegurar que hay Instance ID
                            instance_id = parts[35].strip('"')

                            # Extraer puerto del Instance ID usando regex rápido
                            # Buscar patrón como USB\VID_xxxx&PID_xxxx\serial
                            if 'USB\\' in instance_id:
                                # Generar puerto basado en hash del serial (rápido y consistente)
                                serial_part = instance_id.split('\\')[-1] if '\\' in instance_id else instance_id
                                port_hash = abs(hash(serial_part)) % 9999
                                port_info = f"Port_#{port_hash:04d}"

                                # Buscar hub en otros dispositivos del CSV
                                hub_info = find_hub_for_device_fast(content, instance_id)

                                return port_info, hub_info

                # Si no se encontró por letra de unidad, buscar en dispositivos con Port_# en el nombre
                for line in content.split('\n'):
                    if 'Port_#' in line and 'Hub_#' in line:
                        # Extraer información de puerto directamente
                        port_match = re.search(r'Port_#(\d+)\.Hub_#(\d+)', line)
                        if port_match:
                            port_info = f"Port_#{port_match.group(1).zfill(4)}.Hub_#{port_match.group(2).zfill(4)}"
                            hub_info = f"Hub {port_match.group(2)}"
                            return port_info, hub_info

            return "No disponible", "No disponible"

        finally:
            # Limpiar archivo temporal
            try:
                os.unlink(temp_csv)
            except:
                pass

    except Exception as e:
        print(f"Error en USBDeview rápido: {e}")
        return "No disponible", "No disponible"

def find_hub_for_device_fast(csv_content: str, device_instance_id: str) -> str:
    """Encontrar hub asociado a un dispositivo rápidamente"""
    try:
        # Buscar hubs en el CSV
        for line in csv_content.split('\n'):
            if 'Hub_#' in line and 'Port_#' in line:
                # Este es un hub, extraer número
                hub_match = re.search(r'Hub_#(\d+)', line)
                if hub_match:
                    return f"Hub {hub_match.group(1)}"

        return "Hub USB"
    except:
        return "Hub USB"

def get_port_info_for_drive(drive_letter: str) -> Tuple[str, str]:
    """Obtener información del puerto USB usando método optimizado (USBDeview + PowerShell rápido)"""
    try:
        # Normalizar formato
        clean_drive = drive_letter.upper().replace(':', '')

        print(f"Obteniendo información de puerto para: {clean_drive}:")

        # MÉTODO 1: USBDeview (rápido) - Intentar primero
        try:
            port_info, hub_info = get_port_info_with_usbdeview_fast(clean_drive)
            if port_info != "No disponible":
                print(f"✓ USBDeview rápido: {port_info} ({hub_info})")
                return port_info, hub_info
        except Exception as e:
            print(f"⚠ USBDeview falló: {e}")

        # MÉTODO 2: PowerShell optimizado (fallback)
        print("Usando PowerShell optimizado...")
        drive_letter_ps = f"{clean_drive}:"

        # Script PowerShell optimizado - más rápido, menos operaciones
        powershell_script = f'''
        $driveLetter = "{clean_drive}"

        try {{
            # Método rápido: usar CIM en lugar de WMI y evitar loops
            $volume = Get-Volume -DriveLetter $driveLetter -ErrorAction SilentlyContinue
            if (-not $volume) {{
                Write-Output "No disponible|No disponible"
                exit
            }}

            # Obtener partición directamente
            $partition = Get-Partition -DriveLetter $driveLetter -ErrorAction SilentlyContinue
            if (-not $partition) {{
                Write-Output "No disponible|No disponible"
                exit
            }}

            # Usar CIM para obtener disco (más rápido que WMI)
            $disk = Get-CimInstance -ClassName Win32_DiskDrive | Where-Object {{ $_.Index -eq $partition.DiskNumber }} | Select-Object -First 1
            if (-not $disk -or $disk.InterfaceType -ne "USB") {{
                Write-Output "No disponible|No disponible"
                exit
            }}

            # Método optimizado: buscar directamente en LocationInformation sin loops
            $pnpDeviceID = $disk.PNPDeviceID
            $portInfo = "No disponible"

            # Intentar obtener LocationInformation directamente del dispositivo
            try {{
                $locationInfo = (Get-PnpDeviceProperty -InstanceId $pnpDeviceID -KeyName 'DEVPKEY_Device_LocationInfo' -ErrorAction SilentlyContinue).Data
                if ($locationInfo -and $locationInfo -match 'Port_#(\\d+)\\.Hub_#(\\d+)') {{
                    $portInfo = $locationInfo
                }}
            }} catch {{ }}

            # Si no se encontró, intentar con el dispositivo padre directo (sin loop)
            if ($portInfo -eq "No disponible") {{
                try {{
                    $parentId = (Get-PnpDeviceProperty -InstanceId $pnpDeviceID -KeyName "DEVPKEY_Device_Parent" -ErrorAction SilentlyContinue).Data
                    if ($parentId) {{
                        $parentLocationInfo = (Get-PnpDeviceProperty -InstanceId $parentId -KeyName 'DEVPKEY_Device_LocationInfo' -ErrorAction SilentlyContinue).Data
                        if ($parentLocationInfo -and $parentLocationInfo -match 'Port_#(\\d+)\\.Hub_#(\\d+)') {{
                            $portInfo = $parentLocationInfo
                        }}
                    }}
                }} catch {{ }}
            }}

            # Extraer información del hub
            $hubInfo = "No disponible"
            if ($portInfo -match "Port_#(\\d+)\\.Hub_#(\\d+)") {{
                $hubInfo = "Hub $($matches[2])"
            }}

            Write-Output "$portInfo|$hubInfo"

        }} catch {{
            Write-Output "No disponible|No disponible"
        }}
        '''
        
        # Configurar para ocultar la consola
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        
        result = subprocess.run(
            ["powershell", "-Command", powershell_script],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=15,
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        
        if result.returncode == 0 and result.stdout.strip():
            output = result.stdout.strip()
            print(f"PowerShell output: {output}")
            
            if '|' in output:
                port_id, hub_info = output.split('|', 1)
                print(f"Puerto encontrado: {port_id}, Hub: {hub_info}")
                return port_id, hub_info
            else:
                return output, "No disponible"
        else:
            print(f"Error en PowerShell: {result.stderr}")
            return "No disponible", "No disponible"
            
    except Exception as e:
        print(f"Error obteniendo información de puerto: {e}")
        import traceback
        traceback.print_exc()
        return "No disponible", "No disponible"



def show_rename_dialog_with_port_info(parent, drive_letter: str):
    """Función de compatibilidad - ya no necesaria"""
    print("show_rename_dialog_with_port_info llamado - función obsoleta")
    pass

class USBPortDialog:
    def __init__(self, parent, device_info: Dict, port_mapper: USBPortMapper):
        self.device_info = device_info
        self.port_mapper = port_mapper
        self.result = None
        
        # Crear ventana de diálogo
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Renombrar Puerto USB")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Centrar ventana
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        self.create_widgets()
        
    def create_widgets(self):
        # Frame principal
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_label = ttk.Label(main_frame, text="Información del Dispositivo USB", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Frame de información
        info_frame = ttk.LabelFrame(main_frame, text="Detalles del Dispositivo", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Información del dispositivo
        info_items = [
            ("Modelo:", self.device_info.get('Model', 'N/A')),
            ("Marca:", self.device_info.get('Brand', 'N/A')),
            ("Tamaño:", f"{self.device_info.get('Size', 0)} GB"),
            ("Letras de unidad:", self.device_info.get('DriveLetters', 'Sin asignar')),
            ("Puerto:", self.device_info.get('PortInfo', 'No disponible')),
            ("Hub:", self.device_info.get('HubInfo', 'No disponible'))
        ]
        
        for i, (label, value) in enumerate(info_items):
            ttk.Label(info_frame, text=label, font=('Arial', 9, 'bold')).grid(
                row=i, column=0, sticky=tk.W, padx=(0, 10), pady=2)
            ttk.Label(info_frame, text=str(value), font=('Arial', 9)).grid(
                row=i, column=1, sticky=tk.W, pady=2)
        
        # Frame de renombrado
        rename_frame = ttk.LabelFrame(main_frame, text="Renombrar Puerto", padding="10")
        rename_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Campo de nombre personalizado
        ttk.Label(rename_frame, text="Nombre personalizado para este puerto:").pack(anchor=tk.W)
        
        self.name_var = tk.StringVar()
        # Cargar nombre existente si existe
        port_key = self.device_info.get('PortInfo', '')
        if port_key in self.port_mapper.port_names:
            self.name_var.set(self.port_mapper.port_names[port_key])
        
        self.name_entry = ttk.Entry(rename_frame, textvariable=self.name_var, width=50)
        self.name_entry.pack(fill=tk.X, pady=(5, 10))
        self.name_entry.focus()
        
        # Sugerencias
        suggestions_text = "Sugerencias: 'Puerto Frontal Izquierdo', 'Hub USB 3.0 - Puerto 1', etc."
        ttk.Label(rename_frame, text=suggestions_text, font=('Arial', 8), 
                 foreground='gray').pack(anchor=tk.W)
        
        # Frame de botones
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Botones
        ttk.Button(button_frame, text="Cancelar", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="Guardar", 
                  command=self.save).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Eliminar Nombre", 
                  command=self.delete_name).pack(side=tk.LEFT)
        
        # Bind Enter key
        self.dialog.bind('<Return>', lambda e: self.save())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
    def save(self):
        name = self.name_var.get().strip()
        port_key = self.device_info.get('PortInfo', '')
        
        if not port_key or port_key == 'No disponible':
            messagebox.showwarning("Advertencia", "No se puede renombrar: información de puerto no disponible")
            return
            
        if name:
            self.port_mapper.port_names[port_key] = name
            self.port_mapper.save_port_names()
            messagebox.showinfo("Éxito", f"Puerto renombrado como: '{name}'")
        else:
            # Si está vacío, eliminar el nombre personalizado
            if port_key in self.port_mapper.port_names:
                del self.port_mapper.port_names[port_key]
                self.port_mapper.save_port_names()
        
        self.result = name
        self.dialog.destroy()
        
    def delete_name(self):
        port_key = self.device_info.get('PortInfo', '')
        if port_key in self.port_mapper.port_names:
            del self.port_mapper.port_names[port_key]
            self.port_mapper.save_port_names()
            self.name_var.set("")
            messagebox.showinfo("Éxito", "Nombre personalizado eliminado")
        
    def cancel(self):
        self.result = None
        self.dialog.destroy()

class USBPortMapperGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Mapeador de Puertos USB")
        self.root.geometry("800x600")
        
        self.port_mapper = USBPortMapper()
        self.devices = []
        
        self.create_widgets()
        self.refresh_devices()
        
    def create_widgets(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título y botones
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Dispositivos USB Conectados", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        ttk.Button(header_frame, text="Actualizar", 
                  command=self.refresh_devices).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(header_frame, text="Auto-detectar", 
                  command=self.auto_detect).pack(side=tk.RIGHT)
        
        # Treeview para mostrar dispositivos
        columns = ('Modelo', 'Marca', 'Tamaño', 'Unidades', 'Puerto', 'Nombre Personalizado')
        self.tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)
        
        # Configurar columnas
        column_widths = {'Modelo': 200, 'Marca': 80, 'Tamaño': 80, 'Unidades': 80, 'Puerto': 150, 'Nombre Personalizado': 150}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview y scrollbar
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind doble click
        self.tree.bind('<Double-1>', self.on_device_double_click)
        
        # Frame de estado
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_var = tk.StringVar(value="Listo")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
    def refresh_devices(self):
        """Actualizar lista de dispositivos"""
        self.status_var.set("Detectando dispositivos USB...")
        self.root.update()
        
        # Limpiar treeview
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Obtener dispositivos en hilo separado para no bloquear UI
        threading.Thread(target=self._refresh_devices_thread, daemon=True).start()
        
    def _refresh_devices_thread(self):
        """Hilo para actualizar dispositivos"""
        try:
            self.devices = self.port_mapper.get_usb_devices()
            self.root.after(0, self._update_tree_view)
        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error: {e}"))
    
    def _update_tree_view(self):
        """Actualizar TreeView con los dispositivos"""
        for device in self.devices:
            port_info = device.get('PortInfo', 'No disponible')
            custom_name = self.port_mapper.port_names.get(port_info, '')
            
            values = (
                device.get('Model', 'N/A'),
                device.get('Brand', 'N/A'),
                f"{device.get('Size', 0)} GB",
                device.get('DriveLetters', 'Sin asignar'),
                port_info,
                custom_name
            )
            
            self.tree.insert('', tk.END, values=values)
        
        count = len(self.devices)
        self.status_var.set(f"Encontrados {count} dispositivo(s) USB")
        
    def on_device_double_click(self, event):
        """Manejar doble click en dispositivo"""
        selection = self.tree.selection()
        if not selection:
            return
            
        item = self.tree.item(selection[0])
        values = item['values']
        
        # Encontrar el dispositivo correspondiente
        device_model = values[0]
        device_port = values[4]
        
        device_info = None
        for device in self.devices:
            if (device.get('Model') == device_model and 
                device.get('PortInfo') == device_port):
                device_info = device
                break
        
        if device_info:
            dialog = USBPortDialog(self.root, device_info, self.port_mapper)
            self.root.wait_window(dialog.dialog)
            # Actualizar vista después del diálogo
            self.refresh_devices()
    
    def auto_detect(self):
        """Auto-detectar y mostrar diálogos para dispositivos sin nombre"""
        unnamed_devices = []
        for device in self.devices:
            port_info = device.get('PortInfo', '')
            if port_info and port_info != 'No disponible' and port_info not in self.port_mapper.port_names:
                unnamed_devices.append(device)
        
        if not unnamed_devices:
            messagebox.showinfo("Info", "Todos los dispositivos ya tienen nombres asignados")
            return
        
        for device in unnamed_devices:
            dialog = USBPortDialog(self.root, device, self.port_mapper)
            self.root.wait_window(dialog.dialog)
        
        # Actualizar vista
        self.refresh_devices()
    
    def run(self):
        self.root.mainloop()

def map_port(parent):
    """Función de compatibilidad - ya no necesaria"""
    print("map_port llamado - función obsoleta")
    pass

def main():
    """Función principal"""
    map_port()

if __name__ == "__main__":
    main()

def clear_drive_from_cache(drive_letter: str):
    """Limpia la información de caché para una unidad específica"""
    try:
        print(f"Limpiando caché para unidad: {drive_letter}")
        
        # Limpiar caché de puerto USB usando la función de MONITOREO
        from MONITOREO import clear_usb_port_cache_for_drive
        clear_usb_port_cache_for_drive(drive_letter)
        
    except Exception as e:
        print(f"Error limpiando caché para {drive_letter}: {e}")

def clear_usb_port_cache():
    """Limpia toda la caché de puertos USB"""
    try:
        print("Limpiando caché completa de puertos USB...")
        
        # Limpiar cualquier caché que pueda existir
        # Por ahora solo registramos la acción
        
    except Exception as e:
        print(f"Error limpiando caché completa: {e}")

def get_usb_devices_with_usbtreeview():
    """Función de compatibilidad para obtener dispositivos USB"""
    try:
        print("Obteniendo dispositivos USB...")
        # Retornar lista vacía por ahora
        return []
    except Exception as e:
        print(f"Error obteniendo dispositivos USB: {e}")
        return []
def get_usb_port_info_fast(drive_letter: str, usb_devices=None) -> Tuple[str, str]:
    """Obtener información del puerto USB de forma rápida usando caché"""
    try:
        # Primero intentar obtener desde la caché de MONITOREO
        try:
            from MONITOREO import get_usb_port_info_with_cache
            return get_usb_port_info_with_cache(drive_letter)
        except ImportError:
            # Si no se puede importar MONITOREO, usar método directo
            print(f"No se pudo importar caché de MONITOREO, usando método directo para {drive_letter}")
            return get_port_info_for_drive(drive_letter)
        
    except Exception as e:
        print(f"Error en get_usb_port_info_fast para {drive_letter}: {e}")
        return "No disponible", "No disponible"
from APARIENCIA import apply_acrylic_and_rounded

class ModernPortRenameDialog(QDialog):
    """Diálogo moderno para renombrar puertos USB con efecto acrílico"""
    
    def __init__(self, parent=None, drive_letter="", port_info="", hub_info=""):
        super().__init__(parent)
        self.drive_letter = drive_letter
        self.port_info = port_info
        self.hub_info = hub_info
        
        print(f"Creando diálogo moderno para: {drive_letter}, Puerto: {port_info}, Hub: {hub_info}")
        
        self.setup_window()
        self.create_ui()
        self.apply_styles()
        self.connect_signals()
        
        print("Diálogo moderno creado correctamente")

    def setup_window(self):
        """Configurar la ventana sin barra de título y con efecto acrílico"""
        # Sin barra de título, pero con bordes redondeados
        self.setWindowFlags(
            Qt.WindowType.Dialog | 
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint
        )
        
        # Tamaño más compacto
        self.setFixedSize(280, 150)
        self.setModal(True)
        
        # Efecto acrílico/blur
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # Centrar en pantalla
        if self.parent():
            parent_geo = self.parent().geometry()
            x = parent_geo.x() + (parent_geo.width() - self.width()) // 2
            y = parent_geo.y() + (parent_geo.height() - self.height()) // 2
            self.move(x, y)

    def showEvent(self, event):
        """Aplicar efecto acrílico cuando se muestra la ventana"""
        super().showEvent(event)
        
        # Aplicar efecto acrílico y bordes redondeados usando APARIENCIA.py
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd, mode='dark', is_tooltip=False)
        
        print("Efecto acrílico aplicado al diálogo de mapeo")

    def create_ui(self):
        """Crear la interfaz de usuario"""
        # Layout principal con márgenes reducidos
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 12, 15, 12)
        main_layout.setSpacing(10)
        
        # Solo mostrar información del puerto centrada
        port_label = QLabel(f"Puerto: {self.port_info}")
        port_label.setObjectName("portLabel")
        port_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(port_label)
        
        # Campo de entrada más pequeño y redondeado
        self.name_input = QLineEdit()
        self.name_input.setObjectName("nameInput")
        self.name_input.setPlaceholderText("Nombre personalizado...")
        self.name_input.setFixedHeight(32)
        
        # Pre-llenar con nombre actual si existe
        current_name = self.get_current_port_name()
        if current_name:
            self.name_input.setText(current_name)
        
        main_layout.addWidget(self.name_input)
        
        # Botones con tamaño dinámico
        button_layout = QHBoxLayout()
        
        # Verificar si hay nombre actual para mostrar botón eliminar
        has_current_name = bool(current_name)
        
        if has_current_name:
            # Tres botones - tamaño más pequeño
            button_layout.setSpacing(3)
            
            self.delete_button = QPushButton("Eliminar")
            self.delete_button.setObjectName("deleteButton")
            self.delete_button.setFixedHeight(24)
            self.delete_button.setMinimumWidth(60)
            self.delete_button.setCursor(Qt.CursorShape.PointingHandCursor)
            button_layout.addWidget(self.delete_button)
            
            self.cancel_button = QPushButton("Cancelar")
            self.cancel_button.setObjectName("cancelButton")
            self.cancel_button.setFixedHeight(24)
            self.cancel_button.setMinimumWidth(60)
            self.cancel_button.setCursor(Qt.CursorShape.PointingHandCursor)
            
            self.save_button = QPushButton("Guardar")
            self.save_button.setObjectName("saveButton")
            self.save_button.setFixedHeight(24)
            self.save_button.setMinimumWidth(60)
            self.save_button.setCursor(Qt.CursorShape.PointingHandCursor)
        else:
            # Dos botones - tamaño normal
            button_layout.setSpacing(8)
            
            self.cancel_button = QPushButton("Cancelar")
            self.cancel_button.setObjectName("cancelButton")
            self.cancel_button.setFixedHeight(28)
            self.cancel_button.setMinimumWidth(70)
            self.cancel_button.setCursor(Qt.CursorShape.PointingHandCursor)
            
            self.save_button = QPushButton("Guardar")
            self.save_button.setObjectName("saveButton")
            self.save_button.setFixedHeight(28)
            self.save_button.setMinimumWidth(70)
            self.save_button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)
        main_layout.addLayout(button_layout)

    def apply_styles(self):
        """Aplicar estilos modernos con efecto acrílico"""
        self.setStyleSheet("""
            ModernPortRenameDialog {
                background: rgba(30, 30, 30, 0.85);
                border-radius: 12px;
            }
            
            QLabel#portLabel {
                color: #E8EAED;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                padding: 4px 0px;
            }
            
            QLineEdit#nameInput {
                background: rgba(45, 45, 45, 0.9);
                color: #E8EAED;
                border: none;
                border-radius: 16px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 400;
            }
            
            QLineEdit#nameInput:focus {
                background: rgba(50, 50, 50, 0.95);
                outline: none;
            }
            
            QLineEdit#nameInput::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }
            
            QPushButton {
                border: none;
                border-radius: 12px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: 500;
            }
            
            QPushButton#saveButton {
                background: #4285F4;
                color: white;
            }
            
            QPushButton#saveButton:hover {
                background: #3367D6;
            }
            
            QPushButton#saveButton:pressed {
                background: #2C5AA0;
            }
            
            QPushButton#cancelButton {
                background: rgba(95, 99, 104, 0.8);
                color: #E8EAED;
            }
            
            QPushButton#cancelButton:hover {
                background: rgba(95, 99, 104, 1.0);
            }
            
            QPushButton#deleteButton {
                background: rgba(234, 67, 53, 0.8);
                color: white;
            }
            
            QPushButton#deleteButton:hover {
                background: #EA4335;
            }
        """)

    def connect_signals(self):
        """Conectar señales de los botones"""
        self.save_button.clicked.connect(self.save_port_name)
        self.cancel_button.clicked.connect(self.reject)
        
        # Conectar botón eliminar si existe
        if hasattr(self, 'delete_button'):
            self.delete_button.clicked.connect(self.delete_port_name)

    def get_current_port_name(self):
        """Obtener el nombre actual del puerto desde port_names.json"""
        try:
            port_mapper = USBPortMapper()
            return port_mapper.port_names.get(self.port_info, "")
        except Exception as e:
            print(f"Error obteniendo nombre actual: {e}")
            return ""

    def delete_port_name(self):
        """Eliminar el nombre del puerto"""
        try:
            port_mapper = USBPortMapper()
            if self.port_info in port_mapper.port_names:
                del port_mapper.port_names[self.port_info]
                port_mapper.save_port_names()
                self.name_input.clear()
                print(f"Nombre eliminado para puerto: {self.port_info}")
        except Exception as e:
            print(f"Error eliminando nombre: {e}")

    def save_port_name(self):
        """Guardar el nombre del puerto"""
        try:
            name = self.name_input.text().strip()
            
            if self.port_info == "No disponible":
                print("No se puede guardar: información de puerto no disponible")
                return
            
            port_mapper = USBPortMapper()
            
            if name:
                port_mapper.port_names[self.port_info] = name
                port_mapper.save_port_names()
                print(f"Puerto {self.port_info} renombrado como: {name}")
            else:
                # Si está vacío, eliminar el nombre
                if self.port_info in port_mapper.port_names:
                    del port_mapper.port_names[self.port_info]
                    port_mapper.save_port_names()
                    print(f"Nombre eliminado para puerto: {self.port_info}")
            
            self.accept()
            
        except Exception as e:
            print(f"Error guardando nombre de puerto: {e}")

    def keyPressEvent(self, event):
        """Manejar teclas de acceso rápido"""
        if event.key() == Qt.Key.Key_Escape:
            self.reject()
        elif event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            self.save_port_name()
        else:
            super().keyPressEvent(event)

    def paintEvent(self, event):
        """Pintar fondo con bordes redondeados"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Crear path con bordes redondeados
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()),
            float(rect.y()),
            float(rect.width()),
            float(rect.height()),
            12.0,
            12.0
        )
        
        # Fondo sutil para complementar el efecto acrílico
        painter.fillPath(path, QColor(0, 0, 0, 15))
        
        super().paintEvent(event)
