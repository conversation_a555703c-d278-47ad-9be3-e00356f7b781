from PyQt6.QtWidgets import QMenu, QStyle, QProxyStyle, QWidget, QVBoxLayout, QToolButton, QFrame, QSizePolicy, QLabel, QToolTip
from PyQt6.QtGui import QPainter, QColor, QIcon, QPainterPath, QCursor
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QEventLoop, QEvent, QSize, QTimer, QPoint, QRect
import os, win32api, json, subprocess, ctypes
from datetime import datetime
from AJUSTES import load_config, save_config
from APARIENCIA import (
    ACCENT_POLICY, 
    WINDOWCOMPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, 
    WCA_ACCENT_POLICY, 
    ACCENT_ENABLE_FLUENT,
    is_windows_11_or_greater
)
import sys
from CREAR import icono_MATRIX, create_eye_crossed_icon, boton_pago, boton_mapear_puertos, create_stop_icon, create_eject_icon, renombrar, registro, create_explorer_icon, create_temp_name_icon, create_reset_alias_icon
import time

class CustomMenuStyle(QProxyStyle):
    def styleHint(self, hint, *args): return 1 if hint == QStyle.StyleHint.SH_Menu_Scrollable else super().styleHint(hint, *args)
class RoundedMenu(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent, Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 1, 0, 1)
        self.layout.setSpacing(0)
        self.setMinimumWidth(220)
        
        hwnd = int(self.winId())
        ctypes.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            33,  # DWMWA_WINDOW_CORNER_PREFERENCE
            ctypes.byref(ctypes.c_int(2)),  # DWMWCP_ROUND
            ctypes.sizeof(ctypes.c_int)
        )
        
        # Aplicar el efecto Fluent en Windows 10 o Acrílico en Windows 11
        accent = ACCENT_POLICY()
        if is_windows_11_or_greater():
            accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
            accent.GradientColor = 0x01000000
        else:
            accent.AccentState = ACCENT_ENABLE_FLUENT
            accent.GradientColor = 0x20000000  # Probamos con 12.5% de opacidad
        accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
        data = WINDOWCOMPOSITIONATTRIBDATA()
        data.Attribute = WCA_ACCENT_POLICY
        data.SizeOfData = ctypes.sizeof(accent)
        data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
        ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))
        
        self.actions = {}
        self.loop = None
        self.setStyleSheet("""
            QWidget {
                background: transparent;
                border-radius: 8px;
            }
            QToolButton {
                color: white;
                background: transparent;
                border: none;
                padding: 4px 8px;
                text-align: left;
                font-size: 13px;
                font-weight: bold;
                border-radius: 6px;
                margin: 0px 0px;
                width: 100%;
            }
            QToolButton:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        """)

    def addSeparator(self):
        line = QFrame(self)
        line.setFrameShape(QFrame.Shape.HLine)
        line.setStyleSheet("""
            background: rgba(255, 255, 255, 0.1);
            margin: 0px 0px;
            border: none;
        """)
        line.setFixedHeight(1)
        self.layout.addWidget(line)

    def addAction(self, icon, text, callback=None):
        button = QToolButton(self)
        button.setIcon(icon)
        button.setText(text)
        button.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button.setFixedHeight(28)
        button.setIconSize(QSize(18, 18))
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        button.setStyleSheet("""
            QToolButton {
                color: white;
                background: transparent;
                border: none;
                text-align: left;
                font-size: 11px;
                font-weight: bold;
                padding: 2px 8px;
                padding-left: 8px;
                width: 100%;
                border-radius: 0px;
                transition: all 0.1s ease-in-out;
            }
            QToolButton:hover {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 0px;
                font-size: 11.5px;
                padding-left: 10px;
            }
            QToolButton::icon {
                position: absolute;
                left: 8px;
                width: 18px;
                height: 18px;
                transition: all 0.1s ease-in-out;
            }
            QToolButton:hover::icon {
                width: 20px;
                height: 20px;
                left: 9px;
            }
        """)
        
        self.layout.addWidget(button)
        if callback:
            button.clicked.connect(lambda: self.handle_action(text, callback))
        return button

    def handle_action(self, text, callback):
        self.clicked_action = text
        if callback:
            callback()
        if self.loop:
            self.loop.quit()
        self.hide()

    def exec(self, pos):
        self.move(pos)
        self.show()
        self.loop = QEventLoop()
        self.clicked_action = None
        self.loop.exec()
        return self.clicked_action

    def focusOutEvent(self, event):
        self.hide()
        if self.loop:
            self.loop.quit()
        super().focusOutEvent(event)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()), 
            float(rect.y()), 
            float(rect.width()), 
            float(rect.height()), 
            8.0, 
            8.0
        )
        painter.fillPath(path, QColor(0, 0, 0, 50))

class RoundedTooltip(QLabel):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        
        # Calcular el ancho del texto y añadir espacio extra
        fm = self.fontMetrics()
        text_width = fm.horizontalAdvance(text)
        self.setMinimumWidth(text_width + 50)  # Añadir 100px extra de espacio
        self.setMinimumHeight(35)  # Altura fija para el tooltip
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Crear el path redondeado
        path = QPainterPath()
        path.addRoundedRect(
            0, 
            0, 
            self.width(), 
            self.height(), 
            20.0,  # Radio de las esquinas
            20.0   
        )
        
        # Dibujar el fondo
        painter.fillPath(path, QColor(43, 43, 43, 255))
        
        # Dibujar el texto centrado
        painter.setPen(QColor(255, 255, 255))
        text_rect = self.rect()
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, self.text())

def create_context_menu(parent, volume_name, drive_letter):
    menu = RoundedMenu(parent)
    try:
        clean_volume_name = f"{win32api.GetVolumeInformation(f"{drive_letter}\\")[0]} ({drive_letter})"
    except:
        clean_volume_name = volume_name
        if '[' in clean_volume_name and ']' in clean_volume_name:
            clean_volume_name = clean_volume_name.split('] ')[-1]
        if '|' in clean_volume_name:
            clean_volume_name = clean_volume_name.split('|')[1].strip()
            if not clean_volume_name.endswith(f"({drive_letter})"):
                clean_volume_name = f"{clean_volume_name} ({drive_letter})"

    # Verificar el estado del pago leyendo DISCOS_EXENTOS.txt
    try:
        if getattr(sys, 'frozen', False):
            exe_path = os.path.dirname(sys.executable)
        else:
            exe_path = os.path.dirname(os.path.abspath(__file__))
        exentos_path = os.path.join(exe_path, "DISCOS_EXENTOS.txt")
        
        # Obtener el nombre del volumen para la comparación
        volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
        disco_actual = f"{volume_name} ({drive_letter})"
        if os.path.exists(exentos_path):
            with open(exentos_path, "r", encoding='utf-8') as f:
                try:
                    discos_exentos = json.load(f)
                    esta_exento = disco_actual in discos_exentos
                except json.JSONDecodeError:
                    esta_exento = False
        else:
            esta_exento = False
    except Exception as e:
        print(f"Error al verificar estado de pago: {e}")
        esta_exento = False
    
    # Verificar si la licencia está activa
    license_active = False
    if hasattr(parent.main_window, 'license_active'):
        license_active = parent.main_window.license_active

    def show_activation_dialog():
        from ACTIVATE import show_license_activation_dialog
        menu.hide()
        show_license_activation_dialog(parent.main_window, parent.main_window)

    def wrap_callback(callback, check_license=False):
        if not check_license or license_active:
            return callback
        return show_activation_dialog

    def map_port_and_hide():
        menu.hide()
        parent.main_window.map_port()
    
    def rename_and_hide():
        menu.hide()
        parent.main_window.show_rename_dialog()
    
    def temp_name_and_hide():
        menu.hide()
        parent.main_window.edit_temporary_name()
    
    def eject_and_hide():
        menu.hide()
        parent.main_window.expulsar_disco()
    
    def set_as_matrix_and_hide():
        menu.hide()
        # Obtener información del disco
        try:
            volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
            
            # Crear la entrada para config.json
            entry = {
                'directory': f"{drive_letter}\\",
                'directory_name': '',
                'drive_letter': drive_letter,
                'drive_name': volume_name,
                'timestamp': time.time(),
                'selected': True  # Establecer como seleccionado
            }
            
            # Cargar configuración existente
            if getattr(sys, 'frozen', False):
                app_path = os.path.dirname(sys.executable)
            else:
                app_path = os.path.dirname(os.path.abspath(__file__))
            
            config_path = os.path.join(app_path, 'config.json')
            
            config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # Inicializar la sección de matriz si no existe
            if 'matrix' not in config:
                config['matrix'] = []
            
            # Verificar si el disco ya existe en la lista
            disk_exists = False
            for existing_entry in config['matrix']:
                # Desmarcar cualquier entrada seleccionada previamente
                existing_entry['selected'] = False
                if existing_entry.get('directory', '') == f"{drive_letter}\\":
                    # Actualizar la entrada existente
                    existing_entry['selected'] = True
                    existing_entry['timestamp'] = time.time()
                    disk_exists = True
            
            # Añadir nueva entrada si no existe
            if not disk_exists:
                config['matrix'].append(entry)
            
            # Guardar la configuración
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            # Mostrar tooltip de confirmación
            tooltip_text = f"✓ {volume_name} ({drive_letter}) establecido como MATRIX"
            tooltip = RoundedTooltip(tooltip_text, parent.main_window)
            tooltip.setStyleSheet("""
                RoundedTooltip {
                    color: white;
                    font-weight: bold;
                    font-size: 13px;
                }
            """)
            
            # Posicionar el tooltip
            pos = QCursor.pos()
            tooltip.move(pos.x() + 10, pos.y() + 10)
            tooltip.show()
            
            # Ocultar después de 2 segundos
            QTimer.singleShot(2000, tooltip.deleteLater)
        except Exception as e:
            print(f"Error al establecer MATRIX: {e}")
            import traceback
            traceback.print_exc()

    # Importar la función para crear el icono de MATRIX

    
    # Crear el icono de MATRIX directamente
    matrix_icon = icono_MATRIX(size=24)
    actions = {
        'explore': (f"  {clean_volume_name}", create_explorer_icon(size=25), lambda: explore_drive(drive_letter)),
        'registry': ("  Crear Registro", registro(size=24), wrap_callback(lambda: create_registry(drive_letter), True)),
        'hide': ("  Ocultar Dispositivos", create_eye_crossed_icon(size=24), lambda: hide_drive(parent, parent.selectedItems()[0])),
        'rename': ("  Renombrar (F2)", renombrar(size=24), rename_and_hide),
        'temp_name': ("  Nombre Temporal (F3)", create_temp_name_icon(size=24), temp_name_and_hide),
        'alias': ("  Restablecer Alias Temporal", create_reset_alias_icon(size=24), lambda: reset_temporary_alias(parent)),
        'stop': ("  Cancelar Copia", create_stop_icon(size=24), wrap_callback(lambda: parent.main_window.botones.clear_queue(), True)),
        'payment': ("  ACTIVAR PAGO" if esta_exento else "  DESACTIVAR PAGO", boton_pago(size=30), wrap_callback(lambda: toggle_payment_calculation(drive_letter, parent.main_window), True)),
        'port': ("  Mapear Puerto (F4)", boton_mapear_puertos(size=24), map_port_and_hide),
        'matrix': ("  Establecer MATRIX", matrix_icon, set_as_matrix_and_hide),
        'eject': ("  EXPULSAR", create_eject_icon(size=24), wrap_callback(eject_and_hide, True))
    }
    menu_actions = {}
    for k, (text, icon, callback) in actions.items():
        if isinstance(icon, str):  # Si es una ruta de archivo
            icon_path = os.path.join(os.path.dirname(__file__), 'ICONOS', icon)
            button = menu.addAction(QIcon(icon_path), text, callback)
        else:  # Si es un QIcon ya creado
            button = menu.addAction(icon, text, callback)
        
        # Deshabilitar opciones si la licencia no está activa
        if not license_active and k in ['stop', 'registry', 'rename', 'temp_name', 'matrix', 'payment', 'eject']:
            button.setEnabled(False)
            button.setStyleSheet("""
                QToolButton {
                    color: rgba(255, 255, 255, 0.3);
                    background: transparent;
                }
                QToolButton:hover {
                    background: transparent;
                }
            """)
        menu_actions[k] = button
        if k != list(actions.keys())[-1]:
            menu.addSeparator()
    
    class DummyAction:
        def __init__(self, signal):
            self.signal = signal
            self.triggered = signal
    return (menu, 
            DummyAction(menu_actions['alias']),
            DummyAction(menu_actions['registry']),
            DummyAction(menu_actions['hide']),
            DummyAction(menu_actions['explore']),
            DummyAction(menu_actions['stop']),
            DummyAction(menu_actions['payment']),
            DummyAction(menu_actions['port']),
            DummyAction(menu_actions['rename']),
            DummyAction(menu_actions['temp_name']),
            DummyAction(menu_actions['matrix']),
            DummyAction(menu_actions['eject']))

def toggle_payment_calculation(drive_letter, main_window):
    try:
        # Normalizar la letra de unidad
        if not drive_letter.endswith('\\'):
            drive_letter = drive_letter.rstrip(':') + ':'
            
        # Obtener nombre del volumen
        volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
        disco_actual = f"{volume_name} ({drive_letter})"
        
        # Obtener el modo de pago actual
        config = load_config()
        modo_pago = config.get('modo_pago', 'dispositivo')
        
        # Usar la ruta correcta para el archivo
        if getattr(sys, 'frozen', False):
            exe_path = os.path.dirname(sys.executable)
        else:
            exe_path = os.path.dirname(os.path.abspath(__file__))
        exentos_path = os.path.join(exe_path, "DISCOS_EXENTOS.txt")
        
        # Cargar discos exentos
        if os.path.exists(exentos_path):
            with open(exentos_path, "r", encoding='utf-8') as f:
                try:
                    discos_exentos = json.load(f)
                except json.JSONDecodeError:
                    discos_exentos = []
        else:
            discos_exentos = []
        
        # Verificar si el disco está en la lista
        esta_exento = disco_actual in discos_exentos
        
        # Actualizar la lista
        if esta_exento:
            discos_exentos = [d for d in discos_exentos if d != disco_actual]
            mensaje = f"Se ha activado el cálculo de precio para {disco_actual}"
        else:
            discos_exentos.append(disco_actual)
            mensaje = f"Se ha desactivado el cálculo de precio para {disco_actual}"
        
        # Guardar la lista actualizada
        with open(exentos_path, "w", encoding='utf-8') as f:
            json.dump(discos_exentos, f, ensure_ascii=False)
        
        # Actualizar la lista en la ventana principal
        main_window.discos_exentos = discos_exentos
        
        # Crear tooltip personalizado
        tooltip_text = "✗ Cálculo de precio DESACTIVADO" if esta_exento else "✓ Cálculo de precio ACTIVADO"
        tooltip = RoundedTooltip(tooltip_text, main_window)
        tooltip.setStyleSheet("""
            RoundedTooltip {
                color: white;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        
        # Posicionar el tooltip
        pos = QCursor.pos()
        tooltip.move(pos.x() + 10, pos.y() + 10)
        tooltip.show()
        
        # Ocultar después de 2 segundos
        QTimer.singleShot(2000, tooltip.deleteLater)
        
        # Verificar si el disco tiene archivos en cola o está copiando
        if drive_letter in main_window.total_sizes and main_window.total_sizes[drive_letter] > 0:
            total_size = main_window.total_sizes[drive_letter]
            precio = None
            total_size_str = main_window.format_size(total_size)
            if modo_pago == "duracion":
                duracion_total = 0
                if drive_letter in main_window.queues:
                    for source, _ in list(main_window.queues[drive_letter].queue):
                        if os.path.exists(source):
                            duracion = main_window.get_video_duration(source)
                            if duracion is not None:
                                duracion_total += duracion
                precio = main_window.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
            elif modo_pago == "ficheros":
                # Contar archivos en cola para este disco
                total_files = 0
                if drive_letter in main_window.queues:
                    total_files = len(list(main_window.queues[drive_letter].queue))
                precio = main_window.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files)
            else:  # modo dispositivo
                total_size_gb = total_size / (1024 ** 3)
                precio = main_window.calcular_y_mostrar_precio(drive_letter, total_size_gb)
            
            # Actualizar la interfaz
            for i in range(main_window.list_widget.count()):
                item = main_window.list_widget.item(i)
                if drive_letter in item.text():
                    widget = main_window.list_widget.itemWidget(item)
                    if widget is not None:
                        volume_label = widget.volume_label
                        original_text = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                        
                        # Actualizar etiqueta según el modo y si está exento
                        if precio is not None and not disco_actual in discos_exentos:
                            if modo_pago == "duracion":
                                volume_label.setText(
                                    f"{original_text} ({drive_letter}) 𝐓: {total_size_str}\n⏱️${precio:.2f}"
                                )
                            elif modo_pago == "ficheros":
                                volume_label.setText(
                                    f"{original_text} ({drive_letter}) 𝐓: {total_size_str}\n📜${precio:.2f}"
                                )
                            else:
                                volume_label.setText(
                                    f"{original_text} ({drive_letter}) 𝐓: {total_size_str}\n💰${precio:.2f}"
                                )
                        else:
                            # Cuando está exento o no hay precio, mantener el total en la primera línea:
                            volume_label.setText(f"{original_text} ({drive_letter}) 𝐓: {total_size_str}")
                    break
        else:
            # Si no hay archivos en cola, solo mostrar el nombre del volumen
            for i in range(main_window.list_widget.count()):
                item = main_window.list_widget.item(i)
                if drive_letter in item.text():
                    widget = main_window.list_widget.itemWidget(item)
                    if widget is not None:
                        volume_label = widget.volume_label
                        original_text = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                        volume_label.setText(f"{original_text} ({drive_letter})")
                    break
        
        # Actualizar en la ventana de ajustes si está abierta
        if hasattr(main_window, 'settings_dialog') and main_window.settings_dialog:
            main_window.settings_dialog.discos_exentos = discos_exentos
            main_window.settings_dialog.actualizar_lista_discos()
        return mensaje, discos_exentos
    except Exception as e:
        print(f"Error en toggle_payment_calculation: {e}")
        import traceback
        traceback.print_exc()
        return "Error al procesar el cálculo de precio", []

def reset_temporary_alias(widget):
    for item in widget.selectedItems():
        if (w := widget.itemWidget(item)) and hasattr(w, 'volume_label'):
            text = w.volume_label.text()
            drive = text.split(' ')[-1].strip('()')
            new_text = text.split('|')[1].strip() if '|' in text else text
            w.volume_label.setText(f"{new_text} ({drive})" if not new_text.endswith(f"({drive})") else new_text)
    getattr(widget, 'main_window', widget).update()

def create_registry(drive_letter):
    """Inicia la creación del registro en un hilo separado"""
    try:
        drive = f"{drive_letter.split(':')[0]}:\\" if ':' in drive_letter else f"{drive_letter.split('(')[-1].strip(')')}:\\"
        
        # Mostrar un tooltip de inicio
        from PyQt6.QtWidgets import QToolTip
        from PyQt6.QtCore import QTimer, QRect
        from PyQt6.QtGui import QCursor
        
        # Usar la sintaxis correcta para QToolTip.showText
        QToolTip.showText(QCursor.pos(), "Creando registro...", None)
        
        # Crear el registro en un hilo separado
        def create_registry_thread():
            try:
                registry_path = os.path.join(drive, 'Zetacopy_registro.txt')
                with open(registry_path, 'w', encoding='utf-8') as f:
                    volume_info = win32api.GetVolumeInformation(drive)
                    total_bytes, free_bytes = win32api.GetDiskFreeSpaceEx(drive)[:2]
                    f.write(f"ZETACOPY\n")
                    f.write(f"Fecha de creación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"Información del disco:\nNombre: {volume_info[0]}\nLetra: {drive}\n")
                    f.write(f"Espacio libre: {free_bytes/1024**3:.2f} GB\nEspacio total: {total_bytes/1024**3:.2f} GB\n")
                    f.write("=" * 50 + "\n\nLISTADO DE ARCHIVOS:\n" + "-" * 50 + "\n\n")
                    
                    # Recorrido del sistema de archivos (operación potencialmente lenta)
                    for root, _, files in os.walk(drive):
                        for file in files:
                            if file != 'Zetacopy_registro.txt':
                                try:
                                    path = os.path.join(root, file)
                                    size = os.path.getsize(path)
                                    f.write(f"{os.path.relpath(path, drive)} ({size/1024/1024:.2f} MB)\n")
                                except Exception as e:
                                    print(f"Error procesando {file}: {e}")
                
                # Usar QTimer.singleShot para mostrar el mensaje de éxito en el hilo principal
                # con la sintaxis correcta para QToolTip.showText
                QTimer.singleShot(0, lambda: QToolTip.showText(
                    QCursor.pos(), 
                    f"Registro creado en {registry_path}", 
                    None
                ))
                return True
            except Exception as e:
                print(f"Error creando registro: {e}")
                
                # Usar QTimer.singleShot para mostrar el mensaje de error en el hilo principal
                # con la sintaxis correcta para QToolTip.showText
                QTimer.singleShot(0, lambda: QToolTip.showText(
                    QCursor.pos(), 
                    f"Error al crear registro: {e}", 
                    None
                ))
                return False
        
        # Iniciar el hilo
        import threading
        thread = threading.Thread(target=create_registry_thread, daemon=True)
        thread.start()
        return True  # Devolver True inmediatamente para no bloquear la UI
    except Exception as e:
        print(f"Error iniciando creación de registro: {e}")
        return False

def hide_drive(widget, item):
    drive_letter = item.text().split(' ')[-1].strip('()')
    volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
    disk_info = {
        "letter": drive_letter,
        "name": volume_name
    }
    item.setHidden(True)
    try:
        # Siempre usar la ruta del ejecutable para escritura
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
        else:
            exe_dir = os.path.dirname(os.path.abspath(__file__))
        hidden_drives_file = os.path.join(exe_dir, 'hidden_drives.json')
        
        # Asegurarse de que el archivo existe
        if not os.path.exists(hidden_drives_file):
            with open(hidden_drives_file, 'w') as f:
                json.dump([], f)
        
        # Leer el archivo existente
        with open(hidden_drives_file, 'r') as f:
            try:
                hidden_drives = json.load(f)
            except json.JSONDecodeError:
                hidden_drives = []
        
        # Agregar el nuevo disco si no existe
        if not any(d.get('name') == volume_name for d in hidden_drives):
            hidden_drives.append(disk_info)
            
        # Escribir de vuelta al archivo
        with open(hidden_drives_file, 'w') as f:
            json.dump(hidden_drives, f)
        print(f"Disco oculto guardado en: {hidden_drives_file}")
    except Exception as e:
        print(f"Error guardando disco oculto: {e}")
        import traceback
        traceback.print_exc()
    widget.main_window.update()

def explore_drive(drive_letter):
    try: 
        # Explorer no necesita configuración especial ya que es una aplicación GUI normal
        # Pero agregamos startupinfo para consistencia
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        
        subprocess.run(['explorer', drive_letter], startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
    except Exception as e: print(f"Error al abrir el explorador: {e}")