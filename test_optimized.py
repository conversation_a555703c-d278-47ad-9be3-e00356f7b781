#!/usr/bin/env python3
"""
Script para probar la versión optimizada de mapeo de puertos USB
"""

import sys
import os
import time

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from MAPEO_PUERTO import get_port_info_for_drive, get_port_info_with_usbdeview_fast

def test_speed_comparison():
    """Comparar velocidad entre métodos"""
    print("=== COMPARACIÓN DE VELOCIDAD ===")
    print()
    
    test_drives = ['H', 'N', 'E']
    
    for drive in test_drives:
        print(f"Probando unidad {drive}:")
        
        # Probar USBDeview rápido
        try:
            start_time = time.time()
            port_info, hub_info = get_port_info_with_usbdeview_fast(drive)
            usbdeview_time = time.time() - start_time
            print(f"  USBDeview rápido ({usbdeview_time:.2f}s): {port_info} | {hub_info}")
        except Exception as e:
            print(f"  USBDeview error: {e}")
        
        # Probar método principal (optimizado)
        try:
            start_time = time.time()
            port_info, hub_info = get_port_info_for_drive(drive)
            main_time = time.time() - start_time
            print(f"  Método principal ({main_time:.2f}s): {port_info} | {hub_info}")
        except Exception as e:
            print(f"  Método principal error: {e}")
        
        print()

def test_multiple_calls():
    """Probar múltiples llamadas para ver consistencia"""
    print("=== PRUEBA DE MÚLTIPLES LLAMADAS ===")
    print()
    
    drive = 'H'  # Usar una unidad que sabemos que existe
    
    print(f"Probando unidad {drive} 5 veces:")
    
    for i in range(5):
        try:
            start_time = time.time()
            port_info, hub_info = get_port_info_for_drive(drive)
            elapsed = time.time() - start_time
            print(f"  Intento {i+1} ({elapsed:.2f}s): {port_info} | {hub_info}")
        except Exception as e:
            print(f"  Intento {i+1} error: {e}")

def test_during_copy_simulation():
    """Simular prueba durante actividad de disco"""
    print("=== PRUEBA DURANTE ACTIVIDAD (SIMULADA) ===")
    print()
    
    print("Nota: Para una prueba real, ejecuta este script mientras copias archivos grandes")
    print("a las unidades USB para ver el comportamiento bajo carga.")
    print()
    
    # Probar todas las unidades disponibles
    test_drives = ['H', 'N', 'E', 'O']
    
    for drive in test_drives:
        try:
            start_time = time.time()
            port_info, hub_info = get_port_info_for_drive(drive)
            elapsed = time.time() - start_time
            
            if elapsed > 2.0:
                status = "🐌 LENTO"
            elif elapsed > 1.0:
                status = "⚠️ MODERADO"
            else:
                status = "✅ RÁPIDO"
            
            print(f"  {drive}: ({elapsed:.2f}s) {status} -> {port_info} | {hub_info}")
            
        except Exception as e:
            print(f"  {drive}: ERROR -> {e}")

if __name__ == "__main__":
    print("Probando versión optimizada del mapeo de puertos USB...")
    print()
    
    test_speed_comparison()
    test_multiple_calls()
    test_during_copy_simulation()
    
    print("\nPruebas completadas.")
    print("\nMejoras implementadas:")
    print("✅ USBDeview como método primario (más rápido)")
    print("✅ PowerShell optimizado sin loops lentos")
    print("✅ Uso de CIM en lugar de WMI")
    print("✅ Eliminación de accesos múltiples al registro")
    print("✅ Timeout reducido para evitar bloqueos")
