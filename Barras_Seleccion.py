from PyQt6.QtWidgets import QAbstractItemView, QStyledItemDelegate, QStyleOptionViewItem, QStyle, QTreeWidget, QListWidget
from PyQt6.QtCore import Qt, QRect
from PyQt6.QtGui import QColor, QBrush, QPen, QPainter

class BarraSeleccionDiscos:
    """Clase para gestionar la barra de selección de discos"""
    @staticmethod
    def configurar_lista_discos(list_widget):
        list_widget.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        list_widget.setStyleSheet("""
            QListWidget {
                background: transparent;
                border-radius: 10px;
                outline: 0;  /* Elimina el contorno de foco del widget */
            }
            QListWidget::item {
                background: transparent;
                color: transparent;
                padding-left: 5px; /* Empuja el contenido (icono y texto) a la derecha */
                border-radius: 10px;  /* Bordes redondeados para los items */
                margin: 2px;
            }
            /* Elimina los estilos de selección y hover, ahora los maneja el delegado */
            QListWidget::item:focus {
                outline: none;  /* Elimina el contorno de foco del item */
                border: none;   /* Elimina cualquier borde que pueda aparecer al enfocar */
            }
        """)
        list_widget.setItemDelegate(ListWidgetRoundedRectDelegate(list_widget)) # Aplica el nuevo delegado
        return list_widget

class ListWidgetRoundedRectDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selection_color = QColor(128, 128, 128, 76)  # Gris semitransparente (rgba(128, 128, 128, 0.3))
        self.hover_color = QColor(128, 128, 128, 76)      # Mismo gris semitransparente para el hover
        self.radius = 10 # Radio de los bordes redondeados
        self.horizontal_padding = 2 # Nuevo padding horizontal para la barra de selección

    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index):
        painter.save()
        # Habilitar anti-aliasing para un dibujado suave de los bordes redondeados
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        list_widget = option.widget
        if not isinstance(list_widget, QListWidget): # Asegurarse de que sea un QListWidget
            super().paint(painter, option, index)
            painter.restore()
            return

        # Obtener el rectángulo base de la celda actual
        base_rect = option.rect

        # Definir cuánto se reducirá la altura verticalmente (total para arriba y abajo)
        vertical_reduction = 2 # Para que sea 1px más estrecho arriba y 1px abajo

        # Calcular la altura objetivo, asegurando que nunca sea menor que 2 * radio para un redondeo correcto
        target_height = max(self.radius * 2, base_rect.height() - vertical_reduction)
        
        # Calcular el desplazamiento vertical para centrar el rectángulo reducido
        vertical_offset = (base_rect.height() - target_height) // 2

        # Construir el nuevo rectángulo para dibujar, abarcando todo el ancho de la vista
        drawing_rect = QRect(
            self.horizontal_padding, # Iniciar con padding desde el borde izquierdo
            base_rect.y() + vertical_offset,
            list_widget.viewport().width() - (2 * self.horizontal_padding), # Reducir el ancho por el doble del padding
            target_height
        )

        is_selected = bool(option.state & QStyle.StateFlag.State_Selected)
        is_hovered = bool(option.state & QStyle.StateFlag.State_MouseOver)

        # Dibujar el fondo redondeado si está seleccionado o en hover
        if is_selected:
            painter.setBrush(QBrush(self.selection_color))
            painter.setPen(QPen(Qt.PenStyle.NoPen))
            painter.drawRoundedRect(drawing_rect, self.radius, self.radius)

            # Indicador azul vertical a la izquierda
            indicator_width = 3  # Ancho del indicador
            indicator_height = max(int(drawing_rect.height() * 0.55), 5) # Altura, ajustada al 55% de la selección, mínimo 5px
            indicator_x = drawing_rect.left() + 1 # 1px de padding a la izquierda del rectángulo dibujado
            indicator_y = drawing_rect.top() + (drawing_rect.height() - indicator_height) // 2 # Centrar verticalmente
            indicator_rect = QRect(indicator_x, indicator_y, indicator_width, indicator_height)
            painter.setBrush(QColor(0, 120, 215, 220)) # Azul intenso, semitransparente
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRoundedRect(indicator_rect, 2, 2) # Bordes ligeramente redondeados para el indicador

        elif is_hovered:
            painter.setBrush(QBrush(self.hover_color))
            painter.setPen(QPen(Qt.PenStyle.NoPen))
            painter.drawRoundedRect(drawing_rect, self.radius, self.radius)

        # Dibujar el contenido del ítem (icono y texto) sobre el fondo
        # Es crucial eliminar temporalmente los estados de selección/hover para que el delegado base no dibuje su propio fondo.
        original_state = option.state
        option.state &= ~(QStyle.StateFlag.State_Selected | QStyle.StateFlag.State_MouseOver)
        super().paint(painter, option, index)
        option.state = original_state
        painter.restore()

class RoundedRectDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selection_color = QColor(128, 128, 128, 76)  # Gris semitransparente (rgba(128, 128, 128, 0.3))
        self.hover_color = QColor(128, 128, 128, 76)      # Mismo gris semitransparente para el hover
        self.radius = 10 # Radio de los bordes redondeados
        self.horizontal_padding = 8 # Nuevo padding horizontal para la barra de selección

    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index):
        painter.save()
        # Habilitar anti-aliasing para un dibujado suave de los bordes redondeados
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        tree_view = option.widget
        if not isinstance(tree_view, QTreeWidget):
            super().paint(painter, option, index)
            painter.restore()
            return
        if index.column() == 0: # Solo pintar el fondo si es la primera columna (ahora Tamaño) para evitar repintado
            # Obtener el rectángulo base de la celda actual
            base_rect = option.rect

            # Definir cuánto se reducirá la altura verticalmente (total para arriba y abajo)
            vertical_reduction = 2 # Para que sea 1px más estrecho arriba y 1px abajo

            # Calcular la altura objetivo, asegurando que nunca sea menor que 2 * radio para un redondeo correcto
            target_height = max(self.radius * 2, base_rect.height() - vertical_reduction)
            
            # Calcular el desplazamiento vertical para centrar el rectángulo reducido
            vertical_offset = (base_rect.height() - target_height) // 2

            # Construir el nuevo rectángulo para dibujar, abarcando todo el ancho de la vista
            drawing_rect = QRect(
                self.horizontal_padding, # Iniciar con padding desde el borde izquierdo
                base_rect.y() + vertical_offset,
                tree_view.viewport().width() - (2 * self.horizontal_padding), # Reducir el ancho por el doble del padding
                target_height
            )
            is_selected = bool(option.state & QStyle.StateFlag.State_Selected)
            is_hovered = bool(option.state & QStyle.StateFlag.State_MouseOver)

            # Dibujar el fondo redondeado si está seleccionado o en hover
            if is_selected:
                painter.setBrush(QBrush(self.selection_color))
                painter.setPen(QPen(Qt.PenStyle.NoPen))
                painter.drawRoundedRect(drawing_rect, self.radius, self.radius)

                # Indicador azul vertical a la izquierda
                indicator_width = 3  # Ancho del indicador
                indicator_height = max(int(drawing_rect.height() * 0.55), 5) # Altura, ajustada al 55% de la selección, mínimo 5px
                indicator_x = drawing_rect.left() + 1 # 1px de padding a la izquierda del rectángulo dibujado
                indicator_y = drawing_rect.top() + (drawing_rect.height() - indicator_height) // 2 # Centrar verticalmente
                indicator_rect = QRect(indicator_x, indicator_y, indicator_width, indicator_height)
                painter.setBrush(QColor(0, 120, 215, 220)) # Azul intenso, semitransparente
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRoundedRect(indicator_rect, 2, 2) # Bordes ligeramente redondeados para el indicador

            elif is_hovered:
                painter.setBrush(QBrush(self.hover_color))
                painter.setPen(QPen(Qt.PenStyle.NoPen))
                painter.drawRoundedRect(drawing_rect, self.radius, self.radius)

        # Dibujar el contenido del ítem (icono y texto) sobre el fondo
        # Es crucial eliminar temporalmente los estados de selección/hover para que el delegado base no dibuje su propio fondo.
        original_state = option.state
        option.state &= ~(QStyle.StateFlag.State_Selected | QStyle.StateFlag.State_MouseOver)
        super().paint(painter, option, index)
        option.state = original_state
        painter.restore()

class BarraSeleccionArchivos:
    """Clase para gestionar la barra de selección de archivos en la vista de directorios"""
    @staticmethod
    def configurar_tree_widget(file_tree_widget):
        file_tree_widget.setIndentation(0)
        file_tree_widget.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        style = f"""
            QTreeWidget {{
                border: none !important; /* Asegura que no haya borde en el widget principal */
                outline: none !important; /* Elimina cualquier contorno de foco */
                background: transparent;
                font-size: 12px;
            }}
            QTreeWidget::item {{
                border: none !important; /* Elimina *todos* los bordes de los ítems */
                background: transparent;
                padding-left: 10px; /* Ajustado para el indicador, acerca el icono al indicador */
                padding-right: 10px; /* Se mantiene el padding a la derecha */
                margin: 0px; /* Elimina el margen de los ítems para que no haya espacio entre ellos */
            }}
            /* Los estilos de selección y hover ahora los maneja RoundedRectDelegate */
            QTreeWidget::branch {{
                image: none; /* Elimina la imagen de las ramas (las líneas del árbol) */
                border: none !important; /* Elimina cualquier borde de las ramas */
                background: transparent;
                outline: none !important; /* Elimina contorno de foco de las ramas */
            }}
            QTreeView::branch {{ /* También aplicamos a QTreeView::branch para mayor compatibilidad */
                image: none;
                border: none !important;
                background: transparent;
                outline: none !important;
            }}
        """
        file_tree_widget.setStyleSheet(style)
        file_tree_widget.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        file_tree_widget.setAttribute(Qt.WidgetAttribute.WA_MacShowFocusRect, False)
        file_tree_widget.setItemDelegate(RoundedRectDelegate(file_tree_widget))
        return file_tree_widget

# Nueva clase para la barra de selección en ZFind, basada en RoundedRectDelegate
class ZfindRoundedRectDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selection_color = QColor(128, 128, 128, 76)  # Gris semitransparente
        self.hover_color = QColor(128, 128, 128, 76)      # Mismo gris semitransparente para el hover
        self.radius = 6  # Radio de los bordes redondeados
        self.horizontal_padding = 10  # Valor original para mínima separación
        
    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index):
        painter.save()
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        tree_view = option.widget
        if not isinstance(tree_view, QTreeWidget):
            super().paint(painter, option, index)
            painter.restore()
            return
        
        # Dibujar el fondo de selección/hover solo para las columnas 2 y 3 (Nombre y Ruta)
        if index.column() == 2:  # Dibujamos el fondo de la fila desde la columna "Nombre"
            base_rect = option.rect
            vertical_reduction = 2  # Como lo ajustamos anteriormente
            target_height = max(self.radius * 2, base_rect.height() - vertical_reduction)
            vertical_offset = (base_rect.height() - target_height) // 2
            
            # Obtener el ancho total de las columnas
            header = tree_view.header()
            column0_width = header.sectionSize(0)  # Ancho de la columna 0 (Tamaño)
            column1_width = header.sectionSize(1)  # Ancho de la columna 1 (Ext)
            viewport_width = tree_view.viewport().width()
            
            # Calcular el rectángulo de dibujo para cubrir solo las columnas 2 y 3, con el nuevo padding
            drawing_rect = QRect(
                base_rect.x() - 5 + self.horizontal_padding,  # Ajuste para separación
                base_rect.y() + vertical_offset,
                viewport_width - column0_width - column1_width - (self.horizontal_padding * 2),  # Ajuste el ancho
                target_height
            )
            
            is_selected = bool(option.state & QStyle.StateFlag.State_Selected)
            is_hovered = bool(option.state & QStyle.StateFlag.State_MouseOver)
            
            if is_selected:
                painter.setBrush(QBrush(self.selection_color))
                painter.setPen(QPen(Qt.PenStyle.NoPen))
                painter.drawRoundedRect(drawing_rect, self.radius, self.radius)
                
                # Indicador azul vertical a la izquierda
                indicator_width = 3  # Ancho del indicador
                indicator_height = max(int(drawing_rect.height() * 0.55), 5)
                indicator_x = drawing_rect.left() + 1  # Pegado más al borde izquierdo, como solicitado
                indicator_y = drawing_rect.top() + (drawing_rect.height() - indicator_height) // 2
                indicator_rect = QRect(indicator_x, indicator_y, indicator_width, indicator_height)
                
                painter.setBrush(QColor(0, 120, 215, 220))  # Azul intenso, semitransparente
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRoundedRect(indicator_rect, 2, 2)
                
            elif is_hovered:
                painter.setBrush(QBrush(self.hover_color))
                painter.setPen(QPen(Qt.PenStyle.NoPen))
                painter.drawRoundedRect(drawing_rect, self.radius, self.radius)
        
        # Dibujar el contenido del ítem (icono y texto) sobre el fondo
        original_state = option.state
        option.state &= ~(QStyle.StateFlag.State_Selected | QStyle.StateFlag.State_MouseOver)
        super().paint(painter, option, index)
        option.state = original_state
        
        painter.restore()



