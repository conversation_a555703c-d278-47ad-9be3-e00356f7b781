from PyQt6.QtWidgets import (
    QApplication, QComboBox, QLineEdit, QVBoxLayout, QProgressBar, 
    QMessageBox, QHBoxLayout, QTextEdit, QListWidget, QStyleFactory, 
    QListWidgetItem, QColorDialog, QWidget, QSpacerItem, QSizePolicy, 
    QLabel, QPushButton, QGraphicsDropShadowEffect, QTabWidget, 
    QRadioButton, QTabBar, QSlider, QToolTip, QScrollArea, QLayout, QFileDialog,
    QCheckBox, QFrame, QDialog, QButtonGroup, QGroupBox
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QSize, QPropertyAnimation, 
    pyqtProperty, QPoint, QTimer, QDateTime, QLocale, QMimeData, 
    QEvent, QRect, QObject, QEasingCurve
)
from PyQt6.QtGui import (
    QPixmap, QIcon, QClipboard, QPainter, QPen, QFont, 
    QColor, QDragEnterEvent, QDropEvent, QCursor, QPainterPath, 
    QRadialGradient  # QCursor movido aquí
)
import random
import time, re
import sys
import os
import base64
import win32file
import win32api
import qrcode
import json
import pywintypes
import segno
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.fernet import Fernet
from datetime import datetime, timedelta
from io import BytesIO
from datetime import datetime
from ctypes import windll, byref, sizeof, c_int
from APARIENCIA import ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY, apply_acrylic_effect, apply_acrylic_and_rounded
from MONITOREO import generate_unique_code, get_motherboard_info, get_system_uuid, get_disk_serial_number, get_original_info # Importa la función desde MONITOREO.py
import ctypes
import hashlib
import wmi
import subprocess
from ESTILO_DIALOGO import MacLikeDialog
from PRESENTACION import IconShadowEffect, ModernZWidget  # Añadir esta importación
from CREAR import create_usb_icon, icono_TELEGRAM, icono_WhatsApp, create_external_hdd_icon, create_internal_hdd_icon, boton_mapear_puertos
from ACTIVATE import show_license_activation_dialog
import uuid
from MONITOREO import get_motherboard_info, get_disk_serial_number

class AnimatedButton(QPushButton):
    def __init__(self, icon_path, parent=None):
        super().__init__(parent)
        self.setIcon(QIcon(icon_path))
        self.setIconSize(QSize(25, 25))  # Initial icon size
        self.setStyleSheet("border: none; background: transparent;")
        self.setMinimumSize(35, 35)
        self.setMaximumSize(35, 35)
        AnimatedButton.add_shadow_effect(self, QColor(0, 0, 0, 160))  # Negro con 160 de opacidad
        self.setMouseTracking(True)
        self._icon_size = 25
    @staticmethod
    def add_shadow_effect(widget, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)
    @pyqtProperty(int)
    def icon_size(self):
        return self._icon_size
    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QSize(size, size))
    def enterEvent(self, event):
        self.animate_icon_size(30)  # Enlarged icon size
    def leaveEvent(self, event):
        self.animate_icon_size(25)  # Original icon size
    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(50)  # Duration of the animation in milliseconds
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()
def long_path(path):
    if path.startswith("\\\\"):
        return "\\\\?\\UNC\\" + path[2:]
    return "\\\\?\\" + path
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if hasattr(obj, 'isoformat'):
            return obj.isoformat()
        return super().default(obj)
class DiskListItem(QWidget):
    def __init__(self, disk_info, parent=None):
        super().__init__(parent)
        layout = QHBoxLayout(self)
        self.disk_label = QLabel(disk_info)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_label = QLabel()
        layout.addWidget(self.disk_label)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
class DropArea(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.layout = QVBoxLayout(self)
        self.label = QLabel("Arrastra y suelta discos aquí", self)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(self.label)
        self.disk_list = QListWidget(self)
        self.layout.addWidget(self.disk_list)
        self.indexing_threads = []
        self.load_indexed_disks()

    def load_indexed_disks(self):
        try:
            with open("INDEXADO.txt", "r", encoding='utf-8') as f:
                content = f.read()
            indexed_disks = []
            for line in content.split('\n'):
                if line.startswith("Disco:"):
                    drive_letter = line.split("(")[-1].strip(")")
                    indexed_disks.append(drive_letter)
            self.disk_list.clear()  # Limpia la lista antes de cargar
            for drive_letter in indexed_disks:
                try:
                    # Verificar si la unidad existe y está disponible
                    if not os.path.exists(f"{drive_letter}\\"):
                        continue  # Ignorar completamente esta unidad
                    
                    volume_name = win32api.GetVolumeInformation(drive_letter + "\\")[0]
                    disk_info = f"{volume_name} ({drive_letter})"
                    self.add_disk_to_list(disk_info, True)
                except:
                    # Si hay cualquier error con la unidad, simplemente la ignoramos
                    continue
            
            if self.disk_list.count() > 0:
                self.label.hide()
        except FileNotFoundError:
            print("No se encontró el archivo INDEXADO.txt.")
        except Exception as e:
            print(f"Error al cargar los discos indexados: {str(e)}")

    def add_disk_to_list(self, disk_info, is_indexed=False):
        for i in range(self.disk_list.count()): # Verifica si el disco ya está en la lista
            item = self.disk_list.item(i)
            existing_disk_widget = self.disk_list.itemWidget(item)
            if disk_info in existing_disk_widget.disk_label.text():
                return  # El disco ya está en la lista, no lo añadas
        item = QListWidgetItem(self.disk_list)
        disk_widget = DiskListItem(disk_info)
        item.setSizeHint(disk_widget.sizeHint())
        self.disk_list.addItem(item)
        self.disk_list.setItemWidget(item, disk_widget)
        if is_indexed:
            disk_widget.status_label.setText("Indexado")

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if os.path.exists(file_path):
                drive_letter = os.path.splitdrive(file_path)[0]
                try:
                    if os.path.exists(f"{drive_letter}\\"):
                        try:
                            volume_name = win32api.GetVolumeInformation(drive_letter + "\\")[0]
                        except:
                            volume_name = "Volumen sin nombre"
                        
                        disk_info = f"Disco: {volume_name} ({drive_letter})"
                        is_duplicate = False # Verificar si el disco ya está en la lista
                        for i in range(self.disk_list.count()):
                            item = self.disk_list.item(i)
                            existing_disk_widget = self.disk_list.itemWidget(item)
                            if drive_letter in existing_disk_widget.disk_label.text():
                                is_duplicate = True
                                break
                        if not is_duplicate:
                            self.add_disk_to_list(disk_info)
                        else:
                            pos = QCursor.pos()
                            QToolTip.showText(
                                pos,
                                f"El disco {volume_name} ({drive_letter}) ya está en la lista",
                                self,
                                QRect(),
                                3000  # Mostrar por 2 segundos
                            )
                except Exception as disk_error:
                    print(f"No se pudo cargar el disco {drive_letter}: {disk_error}")
                    continue
            
            if self.disk_list.count() > 0:
                self.label.hide()

    def index_disks(self):
        for i in range(self.disk_list.count()):
            item = self.disk_list.item(i)
            disk_widget = self.disk_list.itemWidget(item)
            disk_info = disk_widget.disk_label.text()
            drive_letter = disk_info.split("(")[-1].strip(")")
            existing_thread = next((t for t in self.indexing_threads if t.drive_letter == drive_letter), None) # Verificar si ya existe un hilo de indexación para este disco
            if existing_thread:
                existing_thread.stop()
                existing_thread.wait()
                self.indexing_threads.remove(existing_thread)
            indexing_thread = IndexingThread(drive_letter)
            indexing_thread.progress_update.connect(self.update_progress)
            indexing_thread.indexing_complete.connect(self.on_disk_indexed)
            self.indexing_threads.append(indexing_thread)
            indexing_thread.start()
            disk_widget.progress_bar.setValue(0)
            disk_widget.progress_bar.setVisible(True)
            disk_widget.status_label.setText("Indexando...")
        if not self.indexing_threads:
            pos = QCursor.pos()
            QToolTip.showText(
                pos,
                f"ARRASTRE DISCOS PARA INDEXAR",
                self,
                QRect(),
                3000  # Mostrar por 2 segundos
            )

    def update_progress(self, drive_letter, progress):
        for i in range(self.disk_list.count()):
            item = self.disk_list.item(i)
            disk_widget = self.disk_list.itemWidget(item)
            if drive_letter in disk_widget.disk_label.text():
                disk_widget.progress_bar.setValue(progress)
                disk_widget.progress_bar.setVisible(True)
                break

    def on_disk_indexed(self, drive_letter, output_file):
        for i in range(self.disk_list.count()):
            item = self.disk_list.item(i)
            disk_widget = self.disk_list.itemWidget(item)
            if drive_letter in disk_widget.disk_label.text():
                disk_widget.progress_bar.setVisible(False)
                disk_widget.status_label.setText("Indexado")
                break
        # Verificar si todos los hilos han terminado
        active_threads = [thread for thread in self.indexing_threads if thread.isRunning()]
        if not active_threads:
            pos = QCursor.pos()
            QToolTip.showText(
                pos,
                f"Todos los discos han sido indexados correctamente",
                self,
                QRect(),
                3000
            )
            self.finish_indexing()

    def finish_indexing(self):
        for thread in self.indexing_threads:
            thread.stop()
            thread.wait()
        self.indexing_threads.clear()

class IndexingThread(QThread):
    progress_update = pyqtSignal(str, int)
    indexing_complete = pyqtSignal(str, str)
    def __init__(self, drive_letter):
        super().__init__()
        self.drive_letter = drive_letter
        self.is_running = True
    def run(self):
        output_file = "INDEXADO.txt"
        total_items = sum([len(files) for r, d, files in os.walk(self.drive_letter)])
        processed_items = 0
        try:
            volume_name = win32api.GetVolumeInformation(self.drive_letter + "\\")[0]
        except:
            volume_name = "Volumen sin nombre"
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"Disco: {volume_name} ({self.drive_letter})\n")
            f.write("=" * 30 + "\n\n")
            for root, dirs, files in os.walk(self.drive_letter):
                if not self.is_running:
                    break
                for file in files:
                    if not self.is_running:
                        break
                    full_path = os.path.join(root, file)
                    relative_path = os.path.relpath(full_path, self.drive_letter)
                    f.write(f"{relative_path}\n")
                    processed_items += 1
                    self.progress_update.emit(self.drive_letter, int((processed_items / total_items) * 100))
            f.write("\n\n")  # Agregar espacio entre indexaciones
        if self.is_running:
            self.indexing_complete.emit(self.drive_letter, output_file)
    def stop(self):
        self.is_running = False
CONFIG_FILE = "config.json"

def save_config(config):
    try:
        if getattr(sys, 'frozen', False):
            config_path = os.path.join(os.path.dirname(sys.executable), 'config.json')
        else:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        with open(config_path, 'w') as f:
            json.dump(config, f)
    except Exception as e:
        print(f"Error guardando configuración: {e}")

def load_config():
    try:
        if getattr(sys, 'frozen', False):
            config_path = os.path.join(os.path.dirname(sys.executable), 'config.json')
        else:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error cargando configuración: {e}")
    return {}  # Retorna un diccionario vacío si hay error o no existe el archivo

from CREAR import CustomCloseButton, CustomMaximizeButton, CustomMinimizeButton

class TransparentWindow(QWidget):
    discos_exentos = []
    @classmethod
    def update_discos_exentos(cls, new_discos_exentos):
        cls.discos_exentos = new_discos_exentos
        print(f"Discos exentos actualizados en TransparentWindow: {cls.discos_exentos}")
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.discos_exentos = []
        
        # Inicialización básica de la ventana
        self.setWindowTitle("Configuración")
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)  # Quitamos Qt.WindowType.Tool
        self.question_animation_timer = QTimer(self)
        self.question_animation_timer.timeout.connect(self.animate_question_mark)
        self.question_y_offset = 0
        self.animation_direction = 1
        self.setFixedSize(600, 826)  # Usar el tamaño que Windows está forzando
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.windll = ctypes.windll
        self.dragging = False
        self.drag_position = None
        self.is_maximized = False
        self.qr_label = QLabel(self)
        self.qr_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.qr_label.setGeometry(300, 100, 200, 200)
        self.show_qr_code()
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        self.button_dock = QWidget()
        self.button_dock.setFixedWidth(150)
        button_dock_layout = QVBoxLayout(self.button_dock)
        button_dock_layout.setContentsMargins(5, 5, 5, 5)
        button_dock_layout.setSpacing(0)
        content_container = QWidget()
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(0, 0, 0, 0)
        self.background_widget = QWidget(self)
        self.background_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 0.01);
                border-radius: 10px;
            }
        """)
        self.background_widget.setGeometry(self.rect())
        self.background_widget.lower()
        self.dock_widget = QWidget(self)
        dock_layout = QHBoxLayout(self.dock_widget)
        dock_layout.setContentsMargins(0, 0, 0, 0)
        dock_layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        window_controls = QHBoxLayout()
        window_controls.setSpacing(2)  # Reducir el espaciado entre botones
        window_controls.setContentsMargins(8, 4, 8, 4)  # Añadir márgenes (izquierda, arriba, derecha, abajo)
        
        # Crear los botones personalizados
        minimize_button = CustomMinimizeButton(self.dock_widget)
        maximize_button = CustomMaximizeButton(self.dock_widget)
        close_button = CustomCloseButton(self.dock_widget)
        
        # Conectar las señales
        minimize_button.clicked.connect(self.showMinimized)
        maximize_button.clicked.connect(self.toggle_maximize_restore)
        close_button.clicked.connect(self.close)
        
        # Añadir los botones al layout
        window_controls.addWidget(minimize_button)
        window_controls.addWidget(maximize_button)
        window_controls.addWidget(close_button)
        
        # Crear un widget contenedor para los controles
        controls_container = QWidget()
        controls_container.setLayout(window_controls)
        
        # Añadir el contenedor al dock_layout con alineación
        dock_layout.addWidget(controls_container, 0, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        
        self.date_label = QLabel(self)
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # Alinear a la derecha
        self.date_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding-right: 10px;
                padding-bottom: 5px;
            }
        """)
        font = QFont()
        font.setBold(True)
        self.date_label.setFont(font)
        
        # Agregar el tab_widget antes de la fecha
        self.tab_widget = QTabWidget(self)
        self.tab_widget.setTabBar(QTabBar())
        self.tab_widget.tabBar().hide()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: transparent;
                margin: 0px;
            }
        """)
        content_layout.addWidget(self.tab_widget)
        content_layout.addWidget(self.date_label)  # Agregar la fecha después del tab_widget
        
        tab_names = ["LICENCIA", "INDEXAR", "PAGOS", "Configuración", "Mapeo de Puertos", "Acerca de"]
        self.tab_buttons = []
        for name in tab_names:
            # Crear el botón
            btn = QPushButton(name)
            btn.setFixedHeight(40)
            btn.setCheckable(True)
            btn.setCursor(Qt.CursorShape.PointingHandCursor)
            
            # Efecto de sombra similar al de CustomBaseButton
            shadow = QGraphicsDropShadowEffect(btn)
            shadow.setBlurRadius(8)
            shadow.setXOffset(0)
            shadow.setYOffset(0)
            shadow.setColor(QColor(0, 0, 0, 160))
            btn.setGraphicsEffect(shadow)
            
            btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 60),
                        stop:0.3 rgba(255, 255, 255, 45),
                        stop:0.5 rgba(255, 255, 255, 35),
                        stop:1 rgba(255, 255, 255, 25));
                    color: white;
                    border: none;
                    border-radius: 18px;
                    padding: 8px 20px;
                    text-align: left;
                    font-weight: bold;
                    margin: 1px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 80),
                        stop:0.3 rgba(255, 255, 255, 65),
                        stop:0.5 rgba(255, 255, 255, 55),
                        stop:1 rgba(255, 255, 255, 45));
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(0, 120, 215, 180),
                        stop:0.5 rgba(0, 120, 215, 160),
                        stop:1 rgba(0, 120, 215, 140));
                    border-left: 2px solid white;
                }
                QPushButton:disabled {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(128, 128, 128, 40),
                        stop:1 rgba(128, 128, 128, 30));
                    color: rgba(255, 255, 255, 0.4);
                }
            """)
            
            # Añadir el botón directamente al layout
            button_dock_layout.addWidget(btn)
            self.tab_buttons.append(btn)
            
            tab = QWidget()
            if name == "LICENCIA":
                self.setup_license_tab(tab)
            elif name == "INDEXAR":
                self.setup_indexar_tab(tab)
            elif name == "PAGOS":
                self.setup_pagos_tab(tab)
                self.cargar_precios()
            elif name == "Configuración":
                self.setup_config_tab(tab)
            elif name == "Mapeo de Puertos":
                self.setup_port_mapping_tab(tab)
            elif name == "Acerca de":
                self.setup_about_tab(tab)
            self.tab_widget.addTab(tab, name)
            btn.clicked.connect(lambda checked, index=self.tab_widget.count()-1: self.change_tab(index))
        button_dock_layout.addStretch()
        main_container = QWidget()
        main_container_layout = QHBoxLayout(main_container)
        main_container_layout.setContentsMargins(0, 0, 0, 0)
        main_container_layout.setSpacing(0)
        main_container_layout.addWidget(self.button_dock)
        main_container_layout.addWidget(content_container)
        final_layout = QVBoxLayout()
        final_layout.setContentsMargins(0, 0, 0, 0)
        final_layout.setSpacing(0)
        final_layout.addWidget(self.dock_widget)
        final_layout.addWidget(main_container)
        widget = QWidget()
        widget.setLayout(final_layout)
        main_layout.addWidget(widget)
        self.setLayout(main_layout)
        for i in range(self.tab_widget.count()):
            if tab_names[i] != "LICENCIA":
                # En lugar de deshabilitar por defecto, verificamos el estado de la licencia
                should_enable = self.main_window and self.main_window.license_active
                self.tab_buttons[i].setEnabled(should_enable)
                self.tab_widget.setTabEnabled(i, should_enable)
        license_index = tab_names.index("LICENCIA") # Seleccionar LICENCIA por defecto
        self.tab_buttons[license_index].setChecked(True)
        self.tab_widget.setCurrentIndex(license_index)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_date)
        self.timer.start(1000)
        self.update_date()
        self.progress_bar_color = self.main_window.progress_bar_color if hasattr(self.main_window, 'progress_bar_color') else "#0078d7"
        self.update_color_button()
        self.check_license()
        self.load_settings()
        self.installEventFilter(self)
        self.license_text = QTextEdit()
        self.license_text.setPlaceholderText("Código de licencia...")
        self.license_text.setFixedHeight(50)
        formatted_code = self.generate_unique_code_with_current_date()
        self.license_text.setText(formatted_code)
        self._close_button_size = 25  # Añadir esta variable

        # Cargar la opacidad guardada
        config = load_config()
        saved_opacity = config.get('background_opacity', 204)  # 204 es el valor por defecto (0xCC)
        
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(0, 255)
        self.opacity_slider.setValue(saved_opacity)  # Establecer el valor guardado
        
        # Actualizar el widget transparente con el valor guardado
        if self.main_window:
            self.main_window.transparent_widget.setStyleSheet(f"""
                QWidget {{
                    background-color: rgba(32, 32, 32, {saved_opacity});
                }}
            """)

        # Conectar el cambio de pestaña a un método personalizado
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        # Configurar un temporizador para verificar periódicamente que la pestaña "Acerca de" esté habilitada
        self.about_tab_timer = QTimer(self)
        self.about_tab_timer.timeout.connect(self.ensure_about_tab_enabled)
        self.about_tab_timer.start(1000)  # Verificar cada segundo
    def on_tab_changed(self, index):
        """Método para manejar el cambio de pestañas"""
        try:
            # Actualizar el estado de los botones
            for i, btn in enumerate(self.tab_buttons):
                btn.setChecked(i == index)
                if self.tab_widget.isTabEnabled(i):
                    btn.setEnabled(True)
                else:
                    btn.setEnabled(False)
            
            # Si es la pestaña de mapeo de puertos, actualizar el estado
            if self.tab_widget.tabText(index) == "Mapeo de Puertos":
                QTimer.singleShot(100, self.update_toggle_state)
        except Exception as e:
            print(f"Error en on_tab_changed: {e}")
    
    def moveEvent(self, event):
        """Manejar el evento de movimiento de la ventana"""
        super().moveEvent(event)
        # Remover la verificación de altura ya que ahora usamos el tamaño correcto
        self.update()
        
    def change_tab(self, index):
        """Mejorar el manejo del cambio de pestañas"""
        if not self.tab_widget.isEnabled():
            self.tab_widget.setEnabled(True)
        
        for i, btn in enumerate(self.tab_buttons):
            btn.setChecked(i == index)
            if self.tab_widget.isTabEnabled(i):
                btn.setEnabled(True)
            else:
                btn.setEnabled(False)
        self.tab_widget.setCurrentIndex(index)
        if self.tab_widget.tabText(index) == "Mapeo de Puertos":
            QTimer.singleShot(100, self.update_toggle_state)  # Pequeño retraso para asegurar que la UI esté lista

    def create_pagos_tab(self, tab):
        pagos_tab = QWidget()
        layout = QVBoxLayout(pagos_tab)
        self.tipo_pago_combo = QComboBox()
        self.tipo_pago_combo.addItem("Pago por Dispositivo")
        layout.addWidget(self.tipo_pago_combo)
        tamano_layout = QHBoxLayout()
        tamano_label = QLabel("Tamaño en GB:")
        self.tamano_input = QLineEdit()
        tamano_layout.addWidget(tamano_label)
        tamano_layout.addWidget(self.tamano_input)
        layout.addLayout(tamano_layout)
        precio_layout = QHBoxLayout()
        precio_label = QLabel("Precio:")
        self.precio_input = QLineEdit()
        precio_layout.addWidget(precio_label)
        precio_layout.addWidget(self.precio_input)
        layout.addLayout(precio_layout)
        self.agregar_button = QPushButton("Agregar")
        self.agregar_button.clicked.connect(self.agregar_precio)
        layout.addWidget(self.agregar_button)
        self.precios_list = QListWidget()
        layout.addWidget(self.precios_list)
        layout.addWidget(QLabel("Discos exentos de pago:"))
        self.discos_exentos_list = QListWidget()
        self.discos_exentos_list.itemChanged.connect(self.on_disk_checkbox_changed)
        layout.addWidget(self.discos_exentos_list)
        actualizar_button = QPushButton("Actualizar lista de discos")
        actualizar_button.clicked.connect(self.actualizar_lista_discos)
        layout.addWidget(actualizar_button)
        guardar_exentos_button = QPushButton("Guardar discos exentos")
        guardar_exentos_button.clicked.connect(self.guardar_discos_exentos)
        layout.addWidget(guardar_exentos_button)
        self.tab_widget.addTab(pagos_tab, "PAGOS")
        self.cargar_precios()
        self.actualizar_lista_discos()
        self.cargar_discos_exentos()
        self.actualizar_lista_discos()
    def on_disk_checkbox_changed(self, disk_info):
        """Manejador del cambio de estado del checkbox"""
        self.guardar_discos_exentos()
    def actualizar_lista_discos(self):
        self.discos_exentos_list.clear()
        for drive in win32api.GetLogicalDriveStrings().split('\000')[:-1]:
            try:
                volume_name = win32api.GetVolumeInformation(drive)[0]
                drive_letter = drive[0] + ":"
                disk_info = f"{volume_name} ({drive_letter})"
                
                # Crear el item con widget personalizado
                item = QListWidgetItem()
                widget = QWidget()
                layout = QHBoxLayout(widget)
                layout.setContentsMargins(5, 2, 5, 2)
                layout.setSpacing(8)
                
                # Obtener el tipo de unidad y su icono correspondiente
                drive_type = self.get_drive_type(drive_letter + "\\")
                icon_path = self.get_disk_icon_path(drive_letter, drive_type)
                
                # Contenedor para el icono con tamaño fijo
                icon_container = QWidget()
                icon_container.setFixedSize(24, 24)  # Tamaño más pequeño para el icono
                icon_layout = QHBoxLayout(icon_container)
                icon_layout.setContentsMargins(0, 0, 0, 0)
                
                # Añadir el icono
                icon_label = QLabel()
                icon_label.setFixedSize(20, 20)  # Tamaño del icono más pequeño
                icon_label.setPixmap(QIcon(icon_path).pixmap(20, 20))
                icon_label.setScaledContents(True)
                icon_layout.addWidget(icon_label, alignment=Qt.AlignmentFlag.AlignCenter)
                
                layout.addWidget(icon_container)
                
                # Añadir el texto
                text_label = QLabel(disk_info)
                text_label.setStyleSheet("""
                    QLabel {
                        color: white;
                        font-size: 12px;
                    }
                """)
                layout.addWidget(text_label)
                
                # Añadir espacio flexible
                layout.addStretch()
                
                # Checkbox a la derecha
                checkbox = QCheckBox()
                checkbox.setStyleSheet("""
                    QCheckBox {
                        spacing: 5px;
                    }
                    QCheckBox::indicator {
                        width: 16px;
                        height: 16px;
                        border: 2px solid rgba(255, 255, 255, 0.7);
                        border-radius: 3px;
                        background: transparent;
                    }
                    QCheckBox::indicator:checked {
                        background: #0078D7;
                        border: 2px solid #0078D7;
                        image: url(ICONOS/check.png);
                    }
                    QCheckBox::indicator:hover {
                        border: 2px solid white;
                    }
                """)
                checkbox.setChecked(disk_info in self.discos_exentos)
                checkbox.stateChanged.connect(lambda state, di=disk_info: self.on_disk_checkbox_changed(di))
                
                # Checkbox para exención de pago
                checkbox_exento = QCheckBox("Exento")
                checkbox_exento.setStyleSheet("""
                    QCheckBox {
                        spacing: 5px;
                        color: white;
                    }
                    QCheckBox::indicator {
                        width: 16px;
                        height: 16px;
                        border: 2px solid rgba(255, 255, 255, 0.7);
                        border-radius: 3px;
                        background: transparent;
                    }
                    QCheckBox::indicator:checked {
                        background: #0078D7;
                        border: 2px solid #0078D7;
                        image: url(ICONOS/check.png);
                    }
                    QCheckBox::indicator:hover {
                        border: 2px solid white;
                    }
                """)
                checkbox_exento.setChecked(disk_info in self.discos_exentos)
                checkbox_exento.stateChanged.connect(lambda state, di=disk_info: self.on_disk_checkbox_changed(di))
                
                # Checkbox para ocultar
                checkbox_ocultar = QCheckBox("Ocultar")
                checkbox_ocultar.setStyleSheet(checkbox_exento.styleSheet())  # Mismo estilo que el otro checkbox
                
                # Checkbox para no expulsar
                checkbox_no_expulsar = QCheckBox("No expulsar")
                checkbox_no_expulsar.setStyleSheet(checkbox_exento.styleSheet())  # Mismo estilo que el otro checkbox
                
                # Cargar el estado desde config.json
                config = load_config()
                discos_ocultos = config.get('discos_ocultos', [])
                discos_no_expulsar = config.get('discos_no_expulsar', [])
                checkbox_ocultar.setChecked(disk_info in discos_ocultos)
                checkbox_no_expulsar.setChecked(disk_info in discos_no_expulsar)
                checkbox_ocultar.stateChanged.connect(lambda state, di=disk_info: self.on_hide_checkbox_changed(di))
                checkbox_no_expulsar.stateChanged.connect(lambda state, di=disk_info: self.on_no_expulsar_checkbox_changed(di))
                
                layout.addWidget(checkbox_exento)
                layout.addWidget(checkbox_ocultar)
                layout.addWidget(checkbox_no_expulsar)
                
                # Establecer el widget y añadir el item
                widget.setFixedHeight(32)  # Altura más compacta
                item.setSizeHint(widget.sizeHint())
                
                # Establecer fondo para el item
                widget.setStyleSheet("""
                    QWidget {
                        background-color: transparent;
                        border-radius: 3px;
                    }
                """)
                self.discos_exentos_list.addItem(item)
                self.discos_exentos_list.setItemWidget(item, widget)
            except Exception as e:
                print(f"Error procesando disco {drive}: {e}")
                continue

    def get_drive_type(self, drive_path):
        """Determina el tipo de unidad usando la misma lógica que ZETACOPY"""
        try:
            drive_type = win32file.GetDriveType(drive_path)
            if drive_path.upper().startswith("C:\\"):
                return "Disco duro interno"
            if drive_type == win32file.DRIVE_REMOVABLE:
                return "Unidad extraíble (USB)"
            if drive_type == win32file.DRIVE_FIXED:
                try:
                    c = wmi.WMI()
                    drive_letter = drive_path[0].upper() + ":"
                    
                    # Buscar el disco físico correspondiente
                    for disk in c.Win32_LogicalDisk(DeviceID=drive_letter):
                        for partition in disk.associators("Win32_LogicalDiskToPartition"):
                            for physical_disk in partition.associators("Win32_DiskDriveToDiskPartition"):
                                # Verificar si es un disco externo
                                if ('usb' in physical_disk.PNPDeviceID.lower() or
                                    'USBSTOR' in physical_disk.PNPDeviceID.upper() or
                                    (hasattr(physical_disk, 'MediaType') and 'External' in str(physical_disk.MediaType)) or
                                    (hasattr(physical_disk, 'InterfaceType') and 'USB' in physical_disk.InterfaceType.upper())):
                                    return "Disco duro externo"
                                
                                # Verificar si es un disco interno
                                if (hasattr(physical_disk, 'InterfaceType') and 
                                    physical_disk.InterfaceType.upper() in ['SCSI', 'IDE', 'SATA', 'NVME']):
                                    return "Disco duro interno"
                                
                                # Si no podemos determinar claramente, asumimos que es externo
                                return "Disco duro externo"
                    return "Disco duro interno"  # Por defecto si no encontramos el disco
                except Exception as e:
                    print(f"Error en WMI: {e}")
                    return "Disco duro interno"
            return "Disco duro interno"
        except Exception as e:
            print(f"Error determinando tipo de unidad: {e}")
            return "desconocido"

    def get_disk_icon_path(self, drive_letter, drive_type):
        """Obtiene el icono basado en el tipo de unidad usando las funciones de CREAR.py"""
        try:
            if drive_letter.upper() == "C:":
                # Usar la nueva función create_windows_icon
                from CREAR import create_windows_icon
                return create_windows_icon(size=24)
            elif drive_type == "Unidad extraíble (USB)":
                # Usar la función create_usb_icon
                from CREAR import create_usb_icon
                return create_usb_icon(size=24)
            elif drive_type == "Disco duro externo":
                # Usar la función create_external_hdd_icon
                from CREAR import create_external_hdd_icon
                return create_external_hdd_icon(size=24)
            elif drive_type == "Disco duro interno":
                # Usar la función create_internal_hdd_icon
                from CREAR import create_internal_hdd_icon
                return create_internal_hdd_icon(size=24)
            else:
                # Para otros tipos de dispositivos, usamos el PNG
                if getattr(sys, 'frozen', False):
                    base_path = sys._MEIPASS
                else:
                    base_path = os.path.dirname(os.path.abspath(__file__))
                return os.path.join(base_path, 'ICONOS', 'OTRAS.png')
        except Exception as e:
            print(f"Error obteniendo icono del disco: {e}")
            # En caso de error, volvemos al PNG
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            return os.path.join(base_path, 'ICONOS', 'OTRAS.png')

    def guardar_discos_exentos(self):
        try:
            discos_exentos = []
            for i in range(self.discos_exentos_list.count()):
                item = self.discos_exentos_list.item(i)
                widget = self.discos_exentos_list.itemWidget(item)
                
                # Buscar el QLabel que contiene el texto del disco
                text_label = None
                for child in widget.children():
                    if isinstance(child, QLabel):
                        text = child.text()
                        if "(" in text and ")" in text:  # Verificar que es el label del disco
                            text_label = child
                            break
                
                # Buscar el checkbox
                checkbox = None
                for child in widget.children():
                    if isinstance(child, QCheckBox):
                        checkbox = child
                        break
                if text_label and checkbox and checkbox.isChecked():
                    disco_info = text_label.text()
                    discos_exentos.append(disco_info)
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(os.path.abspath(__file__))
            exentos_path = os.path.join(exe_path, "DISCOS_EXENTOS.txt")
            with open(exentos_path, "w", encoding='utf-8') as f:
                json.dump(discos_exentos, f, ensure_ascii=False)
            print(f"Discos exentos guardados en: {exentos_path}")
            print(f"Discos exentos guardados: {discos_exentos}")
            self.discos_exentos = discos_exentos
            TransparentWindow.discos_exentos = discos_exentos
            if self.main_window:
                self.main_window.discos_exentos = discos_exentos
            pos = QCursor.pos()
            QToolTip.showText(
                pos,
                f"Lista de discos exentos actualizada: {discos_exentos}",
                self,
                QRect(),
                3000
            )
        except Exception as e:
            print(f"Error guardando discos exentos: {e}")
            import traceback
            traceback.print_exc()

    def cargar_discos_exentos(self):
        try:
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(os.path.abspath(__file__))
            exentos_path = os.path.join(exe_path, "DISCOS_EXENTOS.txt")
            with open(exentos_path, "r") as f:
                self.discos_exentos = json.load(f)
            TransparentWindow.discos_exentos = self.discos_exentos
            print(f"Discos exentos cargados desde: {exentos_path}")
            print(f"Discos exentos cargados: {self.discos_exentos}")
        except FileNotFoundError:
            print("No se encontró el archivo de discos exentos.")
            self.discos_exentos = []
        except json.JSONDecodeError:
            print("El archivo de discos exentos está mal formateado.")
            self.discos_exentos = []
    
    def showEvent(self, event):
        # Asegurar que el botón tenga el tamaño correcto al mostrar
        if hasattr(self, 'close_button'):
            self.close_button.icon_size = self._close_button_size
            self.close_button.setIconSize(QSize(self._close_button_size, self._close_button_size))
        super().showEvent(event)
        # Solo aplicamos el efecto una vez aquí
        apply_acrylic_and_rounded(int(self.winId()))
        self.installEventFilter(self)
        self.cargar_discos_exentos()
        self.actualizar_lista_discos()
        
        # Establecer la ventana como siempre visible
        self.setWindowFlags(self.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
        self.show()  # Necesario para aplicar los nuevos flags

    def hideEvent(self, event):
        # Cuando la ventana se oculta, restauramos los flags normales
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowStaysOnTopHint)
        super().hideEvent(event)

    def agregar_precio(self):
        modo_pago = self.tipo_pago_combo.currentIndex()
        
        if modo_pago == 2:  # Ficheros
            # Método de pago por ficheros (disco + directorio + precio)
            disco = self.disco_combo.currentData()
            directorio = self.directorio_input.text().strip()
            precio_str = self.precio_ficheros_input.text().strip()
            
            # Validar los datos
            if not disco:
                QMessageBox.warning(self, "Error", "Por favor, seleccione un disco.")
                return
                
            if not precio_str:
                QMessageBox.warning(self, "Error", "Por favor, ingrese un precio.")
                return
                
            try:
                precio = float(precio_str)
                if precio < 0:
                    QMessageBox.warning(self, "Error", "El precio debe ser un número positivo.")
                    return
            except ValueError:
                QMessageBox.warning(self, "Error", "Por favor, ingrese un precio válido.")
                return
            
            # Crear la clave única para este mapeo
            clave = f"{disco}|{directorio}" if directorio else disco
            
            # Cargar y actualizar el archivo de precios
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            precios_path = os.path.join(base_path, "PRECIOS.txt")
            try:
                with open(precios_path, "r") as f:
                    precios = json.load(f)
            except:
                precios = {"por_gb": {}, "por_duracion": {}, "por_ficheros": {}}
            
            # Guardar como un objeto con información detallada
            precios["por_ficheros"][clave] = {
                "disco": disco,
                "directorio": directorio,
                "precio": precio
            }
            
            with open(precios_path, "w") as f:
                json.dump(precios, f, indent=4)
            
            # Obtener el nombre del volumen para mostrar en la lista
            try:
                volume_name = win32api.GetVolumeInformation(f"{disco}\\")[0] or "Sin nombre"
                if directorio:
                    item_text = f"{volume_name} ({disco}) - {directorio} --- ${precio:.2f}"
                else:
                    item_text = f"{volume_name} ({disco}) (Todo) --- ${precio:.2f}"
            except:
                if directorio:
                    item_text = f"{disco} - {directorio} --- ${precio:.2f}"
                else:
                    item_text = f"{disco} (Todo) --- ${precio:.2f}"
                
            self.precios_list.addItem(item_text)
            
            # Limpiar los campos
            self.directorio_input.clear()
            self.precio_ficheros_input.clear()
            
            # Actualizar los precios en la ventana principal si existe
            if hasattr(self, 'main_window') and self.main_window is not None:
                self.main_window.precios = self.main_window.cargar_precios()
                print("Precios actualizados en la ventana principal (ficheros)")
            
        else:  # Duración o Dispositivo
            precio = self.precio_input.text()
            if not precio:
                QMessageBox.warning(self, "Error", "Por favor, ingrese un precio.")
                return
            try:
                precio = float(precio)
                tamano = self.tamano_input.text()
                if not tamano:
                    mensaje = "Por favor, ingrese la duración en minutos." if modo_pago == 1 else "Por favor, ingrese el tamaño en GB."
                    QMessageBox.warning(self, "Error", mensaje)
                    return
                tamano = float(tamano)
                if getattr(sys, 'frozen', False):
                    base_path = os.path.dirname(sys.executable)
                else:
                    base_path = os.path.dirname(os.path.abspath(__file__))
                precios_path = os.path.join(base_path, "PRECIOS.txt")
                try:
                    with open(precios_path, "r") as f:
                        precios = json.load(f)
                except:
                    precios = {"por_gb": {}, "por_duracion": {}, "por_ficheros": {}}
                if modo_pago == 1:  # Duración
                    precios["por_duracion"][str(int(tamano))] = precio
                    item_text = f"{int(tamano)} min --- ${precio:.2f}"
                else:  # Dispositivo
                    precios["por_gb"][str(tamano)] = precio
                    item_text = f"{tamano:.2f} GB --- ${precio:.2f}"
                with open(precios_path, "w") as f:
                    json.dump(precios, f, indent=4)
                self.precios_list.addItem(item_text)
                self.tamano_input.clear()
                self.precio_input.clear()
                
                # Actualizar los precios en la ventana principal si existe
                if hasattr(self, 'main_window') and self.main_window is not None:
                    self.main_window.precios = self.main_window.cargar_precios()
                    # Precios actualizados (optimizado)
                
            except ValueError:
                QMessageBox.warning(self, "Error", "Por favor, ingrese valores numéricos válidos.")
    
    def cargar_precios(self):
        try:
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            precios_path = os.path.join(base_path, "PRECIOS.txt")
            with open(precios_path, "r") as f:
                precios = json.load(f)
            self.precios_list.clear()
            modo_actual = self.tipo_pago_combo.currentIndex()
            if modo_actual == 1:  # Duración
                for tamano, precio in precios.get("por_duracion", {}).items():
                    item_text = f"{tamano} min --- ${precio:.2f}"
                    self.precios_list.addItem(item_text)
            elif modo_actual == 2:  # Ficheros
                for clave, data in precios.get("por_ficheros", {}).items():
                    # Verificar si es un objeto (método nuevo) o un valor (método antiguo)
                    if isinstance(data, dict):
                        # Método avanzado
                        precio = data.get("precio", 0)
                        disco = data.get("disco", "")
                        directorio = data.get("directorio", "")
                        
                        # Obtener el nombre del volumen para mostrar en la lista
                        try:
                            volume_name = win32api.GetVolumeInformation(f"{disco}\\")[0] or "Sin nombre"
                            if directorio:
                                item_text = f"{volume_name} ({disco}) - {directorio} --- ${precio:.2f}"
                            else:
                                item_text = f"{volume_name} ({disco}) (Todo) --- ${precio:.2f}"
                        except:
                            if directorio:
                                item_text = f"{disco} - {directorio} --- ${precio:.2f}"
                            else:
                                item_text = f"{disco} (Todo) --- ${precio:.2f}"
                        
                        self.precios_list.addItem(item_text)
                    else:
                        # Convertir el formato antiguo al nuevo
                        nombre_carpeta = clave
                        precio = data
                        
                        # Crear un nuevo registro con el formato avanzado
                        precios["por_ficheros"][f"Migrado|{nombre_carpeta}"] = {
                            "disco": "Migrado",
                            "directorio": nombre_carpeta,
                            "precio": precio
                        }
                        
                        # Eliminar el registro antiguo
                        del precios["por_ficheros"][clave]
                        
                        # Mostrar en la lista con formato especial para datos migrados
                        item_text = f"[Migrado] {nombre_carpeta} --- ${precio:.2f}"
                        self.precios_list.addItem(item_text)
                
                # Guardar los cambios si hubo conversiones
                if any(k.startswith("Migrado|") for k in precios.get("por_ficheros", {})):
                    with open(precios_path, "w") as f:
                        json.dump(precios, f, indent=4)
                
            else:  # Dispositivo (GB)
                for tamano, precio in precios.get("por_gb", {}).items():
                    item_text = f"{float(tamano):.2f} GB --- ${precio:.2f}"
                    self.precios_list.addItem(item_text)
        except FileNotFoundError:
            # PRECIOS.txt no encontrado (mensaje suprimido para evitar spam)
            pass
        except json.JSONDecodeError:
            print("El archivo PRECIOS.txt está vacío o mal formateado")
    
    def borrar_precio_seleccionado(self):
        item_seleccionado = self.precios_list.currentItem()
        if not item_seleccionado:
            return
        try:
            texto_item = item_seleccionado.text()
            modo_actual = self.tipo_pago_combo.currentIndex()
            
            # Cargar precios actuales
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            precios_path = os.path.join(base_path, "PRECIOS.txt")
            with open(precios_path, "r") as f:
                precios = json.load(f)
            
            # Identificar y eliminar el precio según el tipo
            if modo_actual == 1:  # Duración
                match = re.match(r'^(\d+) min --- \$', texto_item)
                if match:
                    tamano = match.group(1)
                    if tamano in precios.get("por_duracion", {}):
                        del precios["por_duracion"][tamano]
            elif modo_actual == 2:  # Ficheros
                # Identificar si es un elemento migrado
                if "[Migrado]" in texto_item:
                    match = re.match(r'^\[Migrado\] (.+) --- \$', texto_item)
                    if match:
                        nombre_carpeta = match.group(1)
                        clave_a_borrar = f"Migrado|{nombre_carpeta}"
                        if clave_a_borrar in precios.get("por_ficheros", {}):
                            del precios["por_ficheros"][clave_a_borrar]
                else:
                    # Para el método avanzado, extraer disco y directorio
                    match_disco_dir = re.search(r'\(([A-Z]:)\)(?: - (.+))? ---', texto_item)
                    if match_disco_dir:
                        disco = match_disco_dir.group(1)
                        directorio = match_disco_dir.group(2) if match_disco_dir.group(2) else ""
                        clave = f"{disco}|{directorio}" if directorio else disco
                        
                        # Buscar y eliminar la clave
                        if clave in precios.get("por_ficheros", {}):
                            del precios["por_ficheros"][clave]
            else:  # Dispositivo (GB)
                match = re.match(r'^([\d.]+) GB --- \$', texto_item)
                if match:
                    tamano = match.group(1)
                    # Buscar la clave que coincida con el valor (puede haber diferencias por redondeo)
                    for key in list(precios.get("por_gb", {}).keys()):
                        if abs(float(key) - float(tamano)) < 0.01:
                            del precios["por_gb"][key]
                            break
            
            # Guardar cambios
            with open(precios_path, "w") as f:
                json.dump(precios, f, indent=4)
            
            # Eliminar de la lista visual
            self.precios_list.takeItem(self.precios_list.row(item_seleccionado))
            
            # Actualizar los precios en la ventana principal si existe
            if hasattr(self, 'main_window') and self.main_window is not None:
                self.main_window.precios = self.main_window.cargar_precios()
                print(f"Precios actualizados en la ventana principal después de borrar")
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"No se pudo eliminar el precio: {str(e)}")
    
    def guardar_precios(self):
        """Guarda los precios configurados en la interfaz"""
        try:
            # Obtener los precios actuales
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            precios_path = os.path.join(base_path, "PRECIOS.txt")
            try:
                with open(precios_path, "r") as f:
                    precios = json.load(f)
            except:
                precios = {"por_gb": {}, "por_duracion": {}, "por_ficheros": {}}
            
            # Guardar los precios
            with open(precios_path, "w") as f:
                json.dump(precios, f, indent=4)
            
            # Actualizar los precios en la ventana principal si existe
            if hasattr(self, 'main_window') and self.main_window is not None:
                self.main_window.precios = self.main_window.cargar_precios()
                print("Precios actualizados en la ventana principal")
            
        except Exception as e:
            print(f"Error al guardar precios: {e}")

    def generate_and_display_code(self):
        try:
            # Generar código usando la función actualizada
            full_code = generate_unique_code()
            if not full_code:
                raise ValueError("Error generando código")
            
            # Definir la contraseña y la sal
            password = "Prado12@@Delgado12@@"
            salt = b"ZetaSalt2024"

            # Derivar la clave
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=480000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))

            # Cifrar el código generado
            fernet = Fernet(key)
            encrypted_code = fernet.encrypt(full_code.encode())

            # Codificar la información cifrada en Base64
            encoded_code = base64.urlsafe_b64encode(encrypted_code).decode('utf-8')

            # Formatear en grupos de 4 para mejor legibilidad
            formatted_code = '-'.join([encoded_code[i:i+4] for i in range(0, len(encoded_code), 4)])
            print(f"Código generado y cifrado: {formatted_code}")
            if hasattr(self, 'code_input'):
                print("Campo de texto encontrado, actualizando...")
                self.code_input.clear()
                self.code_input.setPlainText(formatted_code)
                print(f"Texto establecido en el campo: {self.code_input.toPlainText()}")
            
            # Mostrar el código QR del código generado
            self.show_qr_code(formatted_code)
        except Exception as e:
            print(f"Error generando y mostrando el código: {e}")
    
    def copy_to_clipboard(self, text):
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
        pos = QCursor.pos()
        QToolTip.showText(
            pos,
            "Código copiado al portapapeles",
            self,
            QRect(),
            3000
        )

    def show_qr_code(self, code_text=None):
        base_size = 200
        border_width = 2
        corner_radius = 10
        base_pixmap = QPixmap(base_size, base_size)
        base_pixmap.fill(Qt.GlobalColor.transparent)
        painter = QPainter(base_pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Dibujar el contorno redondeado
        painter.setPen(QPen(QColor(255, 255, 255, 80), 1))
        painter.setBrush(QColor(224, 224, 224, 50))
        painter.drawRoundedRect(0, 0, base_size, base_size, corner_radius, corner_radius)
        if code_text:
            qr = segno.make(code_text, error='L')
            buffer = BytesIO()
            qr.save(buffer, kind='png', scale=10, border=0, dark='black', light=None)
            qr_pixmap = QPixmap()
            qr_pixmap.loadFromData(buffer.getvalue())
            qr_size = base_size - 2*border_width - 10
            qr_pos = (base_size - qr_size) // 2
            painter.drawPixmap(qr_pos, qr_pos, qr_size, qr_size, qr_pixmap)
        painter.end()
        scaled_pixmap = base_pixmap.scaled(self.qr_label.size(), 
                                       Qt.AspectRatioMode.KeepAspectRatio, 
                                       Qt.TransformationMode.SmoothTransformation)
        self.qr_label.setFixedSize(base_size, base_size)
        self.qr_label.setPixmap(scaled_pixmap)
        self.qr_label.setVisible(True)

    def update_date(self):
        """Mantener el mtodo original de actualización de fecha"""
        current_datetime = QDateTime.currentDateTime()
        formatted_date = current_datetime.toString("dd/MM/yyyy hh:mm:ss")
        self.date_label.setText(formatted_date)
        if hasattr(self, 'expiration_date') and self.expiration_date is not None and datetime.now() >= self.expiration_date:
            self.unlock_fields()
            for i in range(self.tab_widget.count()):  # Bloquear todas las pestañas excepto la de "LICENCIA"
                if self.tab_widget.tabText(i) != "LICENCIA":
                    self.tab_widget.setTabEnabled(i, False)
                    self.tab_buttons[i].setEnabled(False)
    
    def toggle_maximize_restore(self):
        if self.is_maximized:
            self.showNormal()
        else:
            self.showMaximized()
        self.is_maximized = not self.is_maximized
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton and self.dock_widget.underMouse():
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    def mouseMoveEvent(self, event):
        if hasattr(self, 'dragging') and self.dragging:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            event.accept()
    def set_rounded_corners(self, hwnd):  # PARA LOS BORDES REDONDEADOS DE LA VENTANA
        DWMWA_WINDOW_CORNER_PREFERENCE = 33
        DWMWCP_ROUND = 2
        windll.dwmapi.DwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, byref(c_int(DWMWCP_ROUND)), sizeof(c_int))
        self.windll.dwmapi.DwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, byref(c_int(DWMWCP_ROUND)), sizeof(c_int))

    def show_dialog(self, title, message, is_error=False):
        """
        Muestra un diálogo MacLike con o sin efecto de sombra roja
        Args:
            title: Título del diálogo
            message: Mensaje a mostrar
            is_error: Si es True, muestra efecto de sombra roja
        """
        dialog = MacLikeDialog(self, title, message)
        if is_error:
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(20)
            shadow.setColor(QColor(255, 0, 0, 180))
            shadow.setOffset(0, 0)
            dialog.setGraphicsEffect(shadow)
        
        # Si es un mensaje de licencia activada, desbloquear pestañas al cerrar el diálogo
        if title == "Licencia Activada" and not is_error:
            dialog.accepted.connect(self.enable_all_tabs)
        
        return dialog.exec()

    def accept_license(self):
        try:
            if not hasattr(self, 'code_input') or not self.code_input.toPlainText().strip():
                self.show_dialog(
                    "Error de Validación",
                    "DEBE GENERAR EL CODIGO DE SU HARDWARE",
                    is_error=True
                )
                return
            new_code = self.new_code_input.toPlainText().strip()
            print(f"Código ingresado: '{new_code}'")
            try:
                # Primero intentar desencriptar el código AES
                formatted_code = new_code.replace('-', '')
                encrypted_code = base64.urlsafe_b64decode(formatted_code)
                
                # Definir la contraseña y la sal para AES
                password = "Prado12@@Delgado12@@"
                salt = b"ZetaSalt2024"
                
                # Derivar la clave
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=480000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
                
                # Desencriptar el código AES a base64
                fernet = Fernet(key)
                decrypted_code = fernet.decrypt(encrypted_code)
                base64_code = decrypted_code.decode('utf-8')
                print(f"Código base64 desencriptado: {base64_code}")
                
                # Ahora validar el código base64
                try:
                    decrypted_info = get_original_info(base64_code)
                    parts = decrypted_info.split('|')
                    
                    # Verificar que tenga las partes necesarias
                    if len(parts) < 3:
                        raise ValueError("Formato de código inválido")
                    
                    # Obtener modelo y fabricante del código
                    stored_model = parts[0]
                    stored_manufacturer = parts[1]
                    
                    # Obtener información actual del hardware
                    motherboard_info = get_motherboard_info()
                    current_model = motherboard_info[1]  # Modelo
                    current_manufacturer = motherboard_info[0]  # Fabricante
                    
                    print("Hardware almacenado:", [stored_model, stored_manufacturer])
                    print("Hardware actual:", [current_model, current_manufacturer])
                    
                    # Verificar si coincide el hardware (independiente del orden)
                    hardware_match = False
                    
                    # Verificar coincidencia exacta
                    if current_model == stored_model and current_manufacturer == stored_manufacturer:
                        hardware_match = True
                    # Verificar coincidencia parcial (al menos uno coincide)
                    elif current_model == stored_model or current_manufacturer == stored_manufacturer:
                        hardware_match = True
                    
                    if not hardware_match:
                        self.show_dialog(
                            "Error de Hardware",
                            "El código de licencia no corresponde a este equipo",
                            is_error=True
                        )
                        return
                    
                    # Obtener la fecha del código
                    date_str = parts[2]  # El tercer elemento es la fecha
                    license_date = datetime.strptime(date_str, "%Y-%m-%d")
                    
                    if license_date > datetime.now():
                        self.expiration_date = license_date
                        self.save_encrypted_license(base64_code)
                        self.show_dialog(
                            "Licencia Activada",
                            f"Licencia válida hasta: {date_str}",
                            is_error=False
                        )
                        if self.main_window:
                            self.main_window.update_license_status(True)
                    else:
                        self.show_dialog(
                            "Licencia Expirada",
                            "La fecha de la licencia es anterior a la fecha actual",
                            is_error=True
                        )
                except Exception as e:
                    print(f"Error validando código base64: {e}")
                    self.show_dialog(
                        "Error de Validación",
                        "Error al validar el código base64",
                        is_error=True
                    )
            except Exception as e:
                print(f"Error desencriptando código AES: {e}")
                # Si falla la desencriptación AES, intentar como licencia regular base64
                self.validate_regular_license(new_code)
        except Exception as e:
            print(f"Error general en accept_license: {e}")
            self.show_dialog(
                "Error",
                "Error al validar la licencia. Verifique el código.",
                is_error=True
            )

    def validate_regular_license(self, new_code):
        try:
            decrypted_info = get_original_info(new_code)
            *system_info, license_date = decrypted_info.split('|')
            license_date = datetime.strptime(license_date, "%Y-%m-%d")
            if license_date > datetime.now():
                # Guardar la fecha de expiración
                self.expiration_date = license_date
                self.save_encrypted_license(license_date)
                
                # Habilitar todas las pestañas (esto también actualizará la pestaña de licencia)
                self.enable_all_tabs()
                
                # Mostrar mensaje de éxito con el nuevo estilo
                dialog = MacLikeDialog(
                    self,
                    "Licencia Activada",
                    f"Licencia válida hasta: {license_date.strftime('%Y-%m-%d')}"
                )
                dialog.exec()
                
                if self.main_window:
                    self.main_window.update_license_status(True)
                    self.main_window.enable_main_window()
            else:
                QMessageBox.warning(self, "Licencia Expirada", 
                    "La fecha de la licencia es anterior a la fecha actual.")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                "Error al validar la licencia. Verifique el código.")

    def save_encrypted_license(self, base64_code):
        try:
            # Si el código es AES, primero desencriptarlo a base64
            if '-' in base64_code:
                try:
                    # Decodificar el código AES
                    formatted_code = base64_code.replace('-', '')
                    encrypted_code = base64.urlsafe_b64decode(formatted_code)
                    
                    # Definir la contraseña y la sal
                    password = "Prado12@@Delgado12@@"
                    salt = b"ZetaSalt2024"
                    
                    # Derivar la clave
                    kdf = PBKDF2HMAC(
                        algorithm=hashes.SHA256(),
                        length=32,
                        salt=salt,
                        iterations=480000,
                    )
                    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
                    
                    # Desencriptar el código AES a base64
                    fernet = Fernet(key)
                    decrypted_code = fernet.decrypt(encrypted_code)
                    base64_code = decrypted_code.decode('utf-8')
                except Exception as e:
                    print(f"Error desencriptando código AES: {e}")
                    return False

            # Obtener la información del hardware y la fecha del código base64
            decrypted_info = get_original_info(base64_code)
            parts = decrypted_info.split('|')
            
            # Formato: modelo|serial_disco|fecha
            model = parts[0]
            disk_serial = parts[1]
            date_str = parts[2]
            
            # Obtener hardware actual
            current_hw = self.get_current_hardware()
            stored_hw = [model, disk_serial]
            
            print(f"Hardware almacenado: {stored_hw}")
            print(f"Hardware actual: {current_hw}")
            
            # Verificar coincidencia de hardware
            if not self.verify_hardware_match(stored_hw, current_hw):
                QMessageBox.critical(self, "Error", "La licencia no es válida para este equipo")
                return False
            
            # Crear la información a encriptar
            hw_info = f"{model}|{disk_serial}"
            expiration_date = datetime.strptime(date_str, "%Y-%m-%d").isoformat()
            full_info = f"{hw_info}||{expiration_date}"
            
            # Generar nueva clave para el almacenamiento
            key = Fernet.generate_key()
            fernet = Fernet(key)
            encrypted = fernet.encrypt(full_info.encode())
            combined_data = key + b'|' + encrypted
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(os.path.abspath(__file__))
            license_path = os.path.join(exe_path, "license.dat")
            print(f"Guardando licencia en: {license_path}")
            with open(license_path, "wb") as license_file:
                license_file.write(combined_data)
            print(f"Licencia guardada exitosamente en {license_path}")
            return True
        except Exception as e:
            print(f"Error guardando licencia: {e}")
            self.show_dialog(
                "Error",
                f"No se pudo guardar la licencia: {str(e)}",
                is_error=True
            )
            return False

    def lock_license_buttons(self):
        generate_button = self.tab_widget.widget(3).findChild(QPushButton, "GENERAR")
        copy_button = self.tab_widget.widget(3).findChild(QPushButton, "COPIAR")
        accept_button = self.tab_widget.widget(3).findChild(QPushButton, "ACEPTAR LICENCIA")
        if generate_button:
            generate_button.setDisabled(True)
        if copy_button:
            copy_button.setDisabled(True)
        if accept_button:
            accept_button.setDisabled(True)

    def unlock_fields(self):
        self.new_code_input.setReadOnly(False)
        self.new_code_input.setDisabled(False)
        self.tab_widget.setDisabled(False)
        self.new_code_input.clear()
        new_unique_code = self.generate_unique_code_with_current_date()
        license_tab = self.tab_widget.widget(3)  # Asumiendo que la pestaña de licencia es la índice 3
        if license_tab:
            license_text = license_tab.findChild(QTextEdit)
            if license_text:
                license_text.setText(new_unique_code)
        self.expiration_date = None
        for i in range(self.tab_widget.count()):
            tab_name = self.tab_widget.tabText(i)
            if tab_name in ["LICENCIA", "Acerca de"]:
                self.tab_widget.setTabEnabled(i, True)
                self.tab_buttons[i].setEnabled(True)
            else:
                self.tab_widget.setTabEnabled(i, False)
                self.tab_buttons[i].setEnabled(False)
        license_tab = self.tab_widget.widget(3)
        if license_tab:
            generate_button = license_tab.findChild(QPushButton, "GENERAR")
            copy_button = license_tab.findChild(QPushButton, "COPIAR") 
            accept_button = license_tab.findChild(QPushButton, "ACEPTAR LICENCIA")
            if generate_button:
                generate_button.setDisabled(False)
            if copy_button:
                copy_button.setDisabled(False)
            if accept_button:
                accept_button.setDisabled(False)
            
    def generate_unique_code_with_current_date(self):
        try:
            # Obtener información del sistema
            motherboard_info = get_motherboard_info()
            model = motherboard_info[1]  # Modelo
            manufacturer = motherboard_info[0]  # Fabricante
            disk_serial = get_disk_serial_number("C:")
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            # Generar el código con modelo, fabricante, serial del disco y fecha
            combined_info = f"{model}|{manufacturer}|{disk_serial}|{current_date}"
            
            # Codificar en base64 para que sea texto
            encoded = base64.urlsafe_b64encode(combined_info.encode('utf-8')).decode('utf-8')
            
            # Formatear en grupos de 4 para mejor legibilidad
            formatted = '-'.join([encoded[i:i+4] for i in range(0, len(encoded), 4)])
            return formatted
            
        except Exception as e:
            print(f"Error generando código: {str(e)}")
            return "Error al generar código"

    def change_progress_bar_color(self):
        current_pos = self.mapToGlobal(self.rect().topRight())
        color_dialog = QColorDialog(QColor(self.progress_bar_color), self)
        color_dialog.setOption(QColorDialog.ColorDialogOption.DontUseNativeDialog, False)
        color_dialog.setStyle(QStyleFactory.create("Fusion"))
        color_dialog.setStyleSheet("""QColorDialog {
                background-color: #f0f0f0;
                color: #000000;
            }
            QColorDialog QWidget {
                background-color: #f0f0f0;
                color: #000000;
            }
        """)
        if color_dialog.exec() == QColorDialog.DialogCode.Accepted:
            color = color_dialog.currentColor()
        if color.isValid():
            self.progress_bar_color = color.name()
            self.color_button.setStyleSheet(f"background-color: {self.progress_bar_color};")
            if hasattr(self, 'main_window') and self.main_window:
                self.main_window.update_progress_bar_color(self.progress_bar_color)
                self.save_settings()
        self.raise_()
        self.activateWindow()

    def update_color_button(self):
        self.color_button.setStyleSheet(f"background-color: {self.progress_bar_color};")

    def save_settings(self):
        config = {
            "mode": "dark",  # Siempre modo oscuro
            "progress_bar_color": self.color_button.styleSheet().split(": ")[-1][:-1],
            "show_disk_lines": self.main_window.config.get('show_disk_lines', True),
            "disk_scale": self.size_modes[self.size_mode_combo.currentText()]["scale"],
            "size_mode": self.size_mode_combo.currentText(),
            "background_opacity": self.opacity_slider.value()  # Guardar la opacidad
        }
        save_config(config)
        if hasattr(self, 'main_window'):
            self.main_window.config.update(config)
            if hasattr(self, 'progress_bar_color'):
                try:
                    self.main_window.update_progress_bar_color(self.progress_bar_color)
                except AttributeError as e:
                    print(f"Error al actualizar el color: {e}")

    def change_mode(self):
        if self.radio_dark.isChecked():
            mode = 'dark'
            self.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    color: white;
                }
                QTabWidget::pane {
                    border: none;
                    background: transparent;
                    margin-top: -1px;
                }
                QTabBar::tab {
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    padding: 8px 20px;
                    margin: 0px 2px;
                    border: none;
                    border-radius: 15px;
                    min-width: 80px;
                    font-weight: bold;
                }
                QTabBar::tab:hover {
                    background: rgba(255, 255, 255, 0.2);
                }
                QTabBar::tab:selected {
                    background: rgba(0, 120, 215, 0.6);
                    color: white;
                }
                QTabBar::tab:disabled {
                    background: rgba(128, 128, 128, 0.1);
                    color: rgba(255, 255, 255, 0.3);
                }
                QLineEdit, QTextEdit, QComboBox {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                }
                QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                    background-color: rgba(255, 255, 255, 0.15);
                }
                QPushButton {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                }
                QLabel {
                    color: white;
                }
            """)
        else:
            mode = 'light'
            self.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    color: black;
                }
                QTabWidget::pane {
                    border: none;
                    background: transparent;
                    margin-top: -1px;
                }
                QTabBar::tab {
                    background: rgba(0, 0, 0, 0.1);
                    color: black;
                    padding: 8px 20px;
                    margin: 0px 2px;
                    border: none;
                    border-radius: 15px;
                    min-width: 80px;
                    font-weight: bold;
                }
                QTabBar::tab:hover {
                    background: rgba(0, 0, 0, 0.15);
                }
                QTabBar::tab:selected {
                    background: rgba(0, 120, 215, 0.4);
                    color: black;
                }
                QTabBar::tab:disabled {
                    background: rgba(128, 128, 128, 0.1);
                    color: rgba(0, 0, 0, 0.3);
                }
                QLineEdit, QTextEdit, QComboBox {
                    background-color: rgba(255, 255, 255, 0.5);
                    color: black;
                    border: 1px solid rgba(0, 0, 0, 0.1);
                    border-radius: 5px;
                    padding: 5px;
                }
                QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                    border: 1px solid rgba(0, 120, 215, 0.5);
                }
                QPushButton {
                    background-color: rgba(0, 0, 0, 0.1);
                    color: black;
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: rgba(0, 0, 0, 0.15);
                }
                QLabel {
                    color: black;
                }
            """)
        apply_acrylic_and_rounded(int(self.main_window.winId()))
        self.main_window.update_mode(mode)
        self.save_settings()

    def load_settings(self):
        config = load_config()
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
                color: white;
            }
            QTabWidget::pane {
                border: none;
                background: transparent;
                margin-top: -1px;
            }
            QTabBar::tab {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                padding: 8px 20px;
                margin: 0px 2px;
                border: none;
                border-radius: 15px;
                min-width: 80px;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            QTabBar::tab:selected {
                background: rgba(0, 120, 215, 0.6);
                color: white;
            }
            QTabBar::tab:disabled {
                background: rgba(128, 128, 128, 0.1);
                color: rgba(255, 255, 255, 0.3);
            }
            QLineEdit, QTextEdit, QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                background-color: rgba(255, 255, 255, 0.15);
            }
            QPushButton {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QLabel {
                color: white;
            }
        """)
        progress_bar_color = config.get("progress_bar_color", "#0078d7")
        self.color_button.setStyleSheet(f"background-color: {progress_bar_color};")
        self.main_window.update_progress_bar_color(progress_bar_color)
        show_lines = config.get("show_disk_lines", True)
        self.show_lines_checkbox.setChecked(show_lines)
        if hasattr(self.main_window, 'list_widget'):
            self.main_window.list_widget.show_disk_lines = show_lines
            self.main_window.list_widget.update()
        saved_mode = config.get('size_mode', 'Normal')
        self.size_mode_combo.setCurrentText(saved_mode)

    def check_license(self):
        """
        Verificación rápida de licencia - solo comprueba la fecha de expiración
        """
        try:
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(os.path.abspath(__file__))
            license_path = os.path.join(exe_path, "license.dat")
            if not os.path.exists(license_path):
                print("No se encontró archivo de licencia")
                self.unlock_fields()
                # Asegurarse de que la pestaña "Acerca de" permanezca habilitada
                for i in range(self.tab_widget.count()):
                    tab_name = self.tab_widget.tabText(i)
                    if tab_name == "Acerca de":
                        self.tab_widget.setTabEnabled(i, True)
                        self.tab_buttons[i].setEnabled(True)
                    else:
                        self.tab_widget.setTabEnabled(i, False)
                        self.tab_buttons[i].setEnabled(False)
                return False
            with open(license_path, "rb") as license_file:
                combined_data = license_file.read()
                key, encrypted = combined_data.split(b'|', 1)
                fernet = Fernet(key)
                decrypted = fernet.decrypt(encrypted).decode()
                
                # Solo extraer y verificar la fecha
                date_str = decrypted.split('||')[-1]
                expiration_date = datetime.fromisoformat(date_str)
                if datetime.now() < expiration_date:
                    # Habilitar pestañas si la licencia es válida
                    self.enable_all_tabs()
                    return True
                else:
                    print("La licencia ha expirado")
                    return False
        except Exception as e:
            print("Error al verificar la licencia:", str(e))
            # Asegurarse de que la pestaña "Acerca de" permanezca habilitada incluso en caso de error
            for i in range(self.tab_widget.count()):
                tab_name = self.tab_widget.tabText(i)
                if tab_name == "Acerca de":
                    self.tab_widget.setTabEnabled(i, True)
                    self.tab_buttons[i].setEnabled(True)
                else:
                    self.tab_widget.setTabEnabled(i, False)
                    self.tab_buttons[i].setEnabled(False)
            return False

    def verify_full_license(self):
        try:
            if getattr(sys, 'frozen', False):
                exe_path = os.path.dirname(sys.executable)
            else:
                exe_path = os.path.dirname(os.path.abspath(__file__))
            license_path = os.path.join(exe_path, "license.dat")
            if not os.path.exists(license_path):
                print("No se encontró archivo de licencia")
                self.unlock_fields()
                return False
            with open(license_path, "rb") as license_file:
                combined_data = license_file.read()
                key, encrypted = combined_data.split(b'|', 1)
                fernet = Fernet(key)
                decrypted = fernet.decrypt(encrypted).decode()
                
                # Separar información de hardware y fecha
                hw_info, date_str = decrypted.split('||')
                parts = hw_info.split('|')
                
                # Obtener modelo y fabricante almacenados
                stored_model = parts[0]
                stored_manufacturer = parts[1]
                
                # Obtener información actual del hardware
                motherboard_info = get_motherboard_info()
                current_model = motherboard_info[1]  # Modelo
                current_manufacturer = motherboard_info[0]  # Fabricante
                
                print("\nVerificación de Hardware:")
                print(f"Hardware almacenado: ['{stored_model}', '{stored_manufacturer}']")
                print(f"Hardware actual: ['{current_model}', '{current_manufacturer}']")
                
                # Verificar si coincide el hardware (independiente del orden)
                hardware_match = False
                
                # Verificar coincidencia exacta
                if current_model == stored_model and current_manufacturer == stored_manufacturer:
                    hardware_match = True
                # Verificar coincidencia parcial (al menos uno coincide)
                elif current_model == stored_model or current_manufacturer == stored_manufacturer:
                    hardware_match = True
                
                if hardware_match:
                    expiration_date = datetime.fromisoformat(date_str)
                    print(f"Licencia válida hasta: {expiration_date}")
                    if datetime.now() < expiration_date:
                        # Guardar la fecha de expiración para mostrarla en la pantalla de bienvenida
                        self.expiration_date = expiration_date
                        
                        # Habilitar todas las pestañas cuando la licencia es válida
                        self.enable_all_tabs()
                        
                        if self.main_window:
                            self.main_window.update_license_status(True)
                            self.main_window.enable_main_window()
                        return True
                    else:
                        print("La licencia ha expirado. Eliminando archivo.")
                        os.remove(license_path)
                        self.unlock_fields()
                        if self.main_window:
                            self.main_window.update_license_status(False)
                            self.main_window.disable_main_window()
                        return False
                else:
                    print("Error: Hardware no coincide")
                    os.remove(license_path)
                    self.unlock_fields()
                    if self.main_window:
                        self.main_window.update_license_status(False)
                        self.main_window.disable_main_window()
                    return False
        except Exception as e:
            print("Error al verificar la licencia:", str(e))
            if os.path.exists(license_path):
                os.remove(license_path)
            self.unlock_fields()
            if self.main_window:
                self.main_window.update_license_status(False)
                self.main_window.disable_main_window()
            return False

    def eventFilter(self, obj, event):
        # Solo aplicamos el efecto cuando la ventana pierde el foco y se vuelve a activar
        if event.type() == QEvent.Type.WindowActivate:
            apply_acrylic_and_rounded(int(self.winId()))
        return super().eventFilter(obj, event)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.background_widget.setGeometry(self.rect())

    def setup_license_tab(self, tab):
        license_layout = QVBoxLayout(tab)
        license_layout.setSpacing(10)
        license_layout.addSpacing(20)
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setSpacing(10)
        container_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.qr_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(self.qr_label, 0, Qt.AlignmentFlag.AlignCenter)
        
        # Mantenemos el campo de texto pero lo ocultamos
        self.code_input = QTextEdit()
        self.code_input.setPlaceholderText("Código generado")
        self.code_input.setFixedHeight(35)
        self.code_input.setFixedWidth(200)
        self.code_input.setStyleSheet("""
            QTextEdit {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
                font-family: 'Segoe UI';
                font-size: 12px;
            }
        """)
        # Ocultamos el campo de texto
        self.code_input.setVisible(False)
        container_layout.addWidget(self.code_input, 0, Qt.AlignmentFlag.AlignCenter)

        # Botones
        button_layout = QHBoxLayout()
        button_layout.setSpacing(5)
        button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        generate_button = QPushButton("GENERAR")
        generate_button.setObjectName("GENERAR")
        generate_button.clicked.connect(self.generate_and_display_code)
        generate_button.setStyleSheet("""
            QPushButton {
                background-color: #0078D7;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0066B5;
            }
            QPushButton:pressed {
                background-color: #005499;
            }
        """)
        generate_button.setFixedSize(100, 30)
        generate_button.setCursor(Qt.CursorShape.PointingHandCursor)

        copy_button = QPushButton("COPIAR")
        copy_button.setObjectName("COPIAR")
        # Modificamos para que copie el texto del campo oculto
        copy_button.clicked.connect(lambda: self.copy_to_clipboard(self.code_input.toPlainText()))
        copy_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:pressed {
                background-color: #1B5E20;
            }
        """)
        copy_button.setFixedSize(100, 30)
        copy_button.setCursor(Qt.CursorShape.PointingHandCursor)

        button_layout.addWidget(generate_button)
        button_layout.addWidget(copy_button)
        container_layout.addLayout(button_layout)

        # Input para nuevo código
        self.new_code_input = QTextEdit()
        self.new_code_input.setPlaceholderText("Pegue aquí el código de licencia...")
        self.new_code_input.setAcceptRichText(False)
        self.new_code_input.setMinimumHeight(100)
        
        # Hacer que el campo sea editable inicialmente pero se bloquee después de pegar
        self.new_code_input.textChanged.connect(self.on_code_input_changed)
        
        # Botón para limpiar el campo
        self.clear_code_button = QPushButton("Limpiar código")
        self.clear_code_button.clicked.connect(self.clear_code_input)
        self.clear_code_button.setVisible(False)  # Inicialmente oculto
        
        container_layout.addWidget(self.new_code_input, 0, Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(self.clear_code_button, 0, Qt.AlignmentFlag.AlignCenter)

        accept_button = QPushButton("ACEPTAR LICENCIA")
        accept_button.setObjectName("ACEPTAR LICENCIA")
        accept_button.clicked.connect(self.accept_license)
        accept_button.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
            QPushButton:pressed {
                background-color: #BF360C;
            }
        """)
        accept_button.setFixedSize(150, 30)
        accept_button.setCursor(Qt.CursorShape.PointingHandCursor)
        container_layout.addWidget(accept_button, 0, Qt.AlignmentFlag.AlignCenter)
        license_layout.addWidget(container)
        license_layout.addStretch(1)

    def on_code_input_changed(self):
        """Se llama cuando cambia el contenido del campo de código"""
        if self.new_code_input.toPlainText().strip():
            # Si hay texto, hacer el campo de solo lectura
            self.new_code_input.setReadOnly(True)
            # Mostrar el botón de limpiar
            self.clear_code_button.setVisible(True)

    def clear_code_input(self):
        """Limpia el campo de código y lo hace editable de nuevo"""
        self.new_code_input.setReadOnly(False)
        self.new_code_input.clear()
        self.clear_code_button.setVisible(False)

    def setup_config_tab(self, tab):
        config_layout = QVBoxLayout(tab)
        config_layout.setSpacing(10)  # Espaciado uniforme entre elementos
        config_layout.setContentsMargins(10, 10, 10, 10)

        # 1. Control de voz
        voice_layout = QHBoxLayout()
        voice_label = QLabel("Anuncios de voz:")
        voice_label.setStyleSheet("color: white; font-weight: bold;")
        self.voice_enabled = QRadioButton("Activado")
        self.voice_disabled = QRadioButton("Desactivado")
        
        # Radio buttons style
        radio_style = """
            QRadioButton {
                color: white;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid rgba(255, 255, 255, 0.7);
                border-radius: 10px;
                background: transparent;
            }
            QRadioButton::indicator:checked {
                background: qradialgradient(
                    cx: 0.5, cy: 0.5, radius: 0.4, fx: 0.5, fy: 0.5,
                    stop: 0 #0078D7,
                    stop: 1 #0078D7
                );
                border: 2px solid #0078D7;
            }
            QRadioButton::indicator:hover {
                border: 2px solid white;
            }
        """
        self.voice_enabled.setStyleSheet(radio_style)
        self.voice_disabled.setStyleSheet(radio_style)
        
        # Configuración de voz
        voice_group = QButtonGroup(self)
        voice_group.addButton(self.voice_enabled)
        voice_group.addButton(self.voice_disabled)
        config = load_config()
        voice_active = config.get('voice_enabled', True)
        if voice_active:
            self.voice_enabled.setChecked(True)
        else:
            self.voice_disabled.setChecked(True)
        voice_group.buttonClicked.connect(self.on_voice_changed)
        
        voice_layout.addWidget(voice_label)
        voice_layout.addWidget(self.voice_enabled)
        voice_layout.addWidget(self.voice_disabled)
        config_layout.addLayout(voice_layout)

        # 2. Configuración de RAM
        ram_group = QGroupBox("Configuración de RAM")
        ram_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        ram_layout = QVBoxLayout(ram_group)

        # RAM para USB
        usb_ram_layout = QHBoxLayout()
        usb_ram_label = QLabel("RAM para USB (MB):")
        usb_ram_label.setStyleSheet("color: white;")
        self.usb_ram_slider = QSlider(Qt.Orientation.Horizontal)
        self.usb_ram_slider.setRange(50, 500)  # 50MB a 500MB
        self.usb_ram_slider.setValue(config.get('usb_ram', 100))
        self.usb_ram_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                background: rgba(255, 255, 255, 0.1);
                height: 8px;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #0078d7;
                width: 18px;
                height: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #1e90ff;
            }
        """)
        self.usb_ram_value = QLabel(f"{self.usb_ram_slider.value()} MB")
        self.usb_ram_value.setStyleSheet("color: white; min-width: 50px;")
        
        def update_usb_ram_label(value):
            self.usb_ram_value.setText(f"{value} MB")
            config = load_config()
            config['usb_ram'] = value
            save_config(config)

        self.usb_ram_slider.valueChanged.connect(update_usb_ram_label)
        usb_ram_layout.addWidget(usb_ram_label)
        usb_ram_layout.addWidget(self.usb_ram_slider)
        usb_ram_layout.addWidget(self.usb_ram_value)
        ram_layout.addLayout(usb_ram_layout)

        # RAM para Discos
        disk_ram_layout = QHBoxLayout()
        disk_ram_label = QLabel("RAM para Discos (MB):")
        disk_ram_label.setStyleSheet("color: white;")
        self.disk_ram_slider = QSlider(Qt.Orientation.Horizontal)
        self.disk_ram_slider.setRange(100, 1000)  # 100MB a 1GB
        self.disk_ram_slider.setValue(config.get('disk_ram', 300))
        self.disk_ram_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                background: rgba(255, 255, 255, 0.1);
                height: 8px;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #0078d7;
                width: 18px;
                height: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #1e90ff;
            }
        """)
        self.disk_ram_value = QLabel(f"{self.disk_ram_slider.value()} MB")
        self.disk_ram_value.setStyleSheet("color: white; min-width: 50px;")
        
        def update_disk_ram_label(value):
            self.disk_ram_value.setText(f"{value} MB")
            config = load_config()
            config['disk_ram'] = value
            save_config(config)

        self.disk_ram_slider.valueChanged.connect(update_disk_ram_label)
        disk_ram_layout.addWidget(disk_ram_label)
        disk_ram_layout.addWidget(self.disk_ram_slider)
        disk_ram_layout.addWidget(self.disk_ram_value)
        ram_layout.addLayout(disk_ram_layout)

        config_layout.addWidget(ram_group)

        # 3. Color de barra de progreso
        color_save_layout = QHBoxLayout()
        color_label = QLabel("Color de la barra de progreso:")
        color_label.setStyleSheet("color: white; font-weight: bold;")
        self.color_button = QPushButton()
        self.color_button.setFixedSize(50, 25)
        self.color_button.clicked.connect(self.change_progress_bar_color)
        self.save_button = QPushButton("Guardar")
        self.save_button.clicked.connect(self.save_settings)
        color_save_layout.addWidget(color_label)
        color_save_layout.addWidget(self.color_button)
        color_save_layout.addWidget(self.save_button)
        config_layout.addLayout(color_save_layout)

        # 4. Líneas divisorias
        lines_layout = QHBoxLayout()
        self.show_lines_checkbox = QCheckBox("Mostrar líneas divisorias")
        self.show_lines_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
            QCheckBox::indicator:checked {
                background-color: rgba(0, 120, 215, 0.6);
                image: url(iconos/CHECK.png);
            }
            QCheckBox::indicator:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        self.show_lines_checkbox.setChecked(self.main_window.config.get('show_disk_lines', True))
        self.show_lines_checkbox.stateChanged.connect(self.toggle_disk_lines)
        lines_layout.addWidget(self.show_lines_checkbox)
        config_layout.addLayout(lines_layout)

        # 5. Método de cálculo de tamaño de carpetas
        folder_size_layout = QHBoxLayout()
        self.use_du_checkbox = QCheckBox("El Calculo de Tamaño de carpetas es (más rápido)")
        self.use_du_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
            QCheckBox::indicator:checked {
                background-color: rgba(0, 120, 215, 0.6);
                image: url(iconos/CHECK.png);
            }
            QCheckBox::indicator:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        # Cargar configuración
        config = load_config()
        self.use_du_checkbox.setChecked(config.get('use_du64', False))
        self.use_du_checkbox.stateChanged.connect(self.toggle_du64_usage)
        folder_size_layout.addWidget(self.use_du_checkbox)
        config_layout.addLayout(folder_size_layout)

        # 6. Opacidad del fondo
        opacity_layout = QHBoxLayout()
        opacity_label = QLabel("Opacidad del fondo:")
        opacity_label.setStyleSheet("color: white; font-weight: bold;")
        
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(4, 255)
        saved_opacity = max(4, config.get('background_opacity', 204))
        self.opacity_slider.setValue(saved_opacity)
        self.opacity_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                background: rgba(255, 255, 255, 0.1);
                height: 8px;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #0078d7;
                width: 18px;
                height: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #1e90ff;
            }
        """)
        self.opacity_value = QLabel()
        self.opacity_value.setStyleSheet("color: white; min-width: 30px;")
        
        def update_label(value):
            mapped_value = int(((value - 4) / (255 - 4)) * 100)
            self.opacity_value.setText(str(mapped_value))

        self.opacity_slider.valueChanged.connect(update_label)
        self.opacity_slider.valueChanged.connect(self.update_background_opacity)

        opacity_layout.addWidget(opacity_label)
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_value)
        config_layout.addLayout(opacity_layout)

        # 6. Tamaño de discos
        size_layout = QHBoxLayout()
        size_label = QLabel("TAMAÑO DE DISCOS:")
        size_label.setStyleSheet("color: white; font-weight: bold;")
        self.size_mode_combo = QComboBox()
        self.size_mode_combo.setFixedWidth(150)
        self.size_mode_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        self.size_modes = {
            "Normal": {"scale": 100, "height": 40, "font": 12, "icon": 30, "progress": 18, "file": 4, "file_offset": 28},
            "Grande": {"scale": 100, "height": 50, "font": 13, "icon": 35, "progress": 18, "file": 4, "file_offset": 40}
        }
        self.size_mode_combo.addItems(self.size_modes.keys())
        saved_mode = config.get('size_mode', 'Normal')
        self.size_mode_combo.setCurrentText(saved_mode)
        self.size_mode_combo.currentTextChanged.connect(self.change_size_mode)
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.size_mode_combo)
        config_layout.addLayout(size_layout)

        # Añadir espacio flexible al final
        config_layout.addStretch()

        # Inicializar el valor del slider
        update_label(self.opacity_slider.value())
    
    def on_voice_changed(self, button):
        """Maneja el cambio en los radio buttons de voz"""
        try:
            config = load_config()
            voice_enabled = button == self.voice_enabled
            config['voice_enabled'] = voice_enabled
            save_config(config)
            
            # Actualizar la configuración en la ventana principal
            if hasattr(self, 'main_window') and self.main_window:
                self.main_window.config['voice_enabled'] = voice_enabled
            
            # Mostrar tooltip de confirmación
            estado = "activados" if voice_enabled else "desactivados"
            pos = QCursor.pos()
            QToolTip.showText(
                pos,
                f"Anuncios de voz {estado}",
                self,
                QRect(),
                3000
            )
            print(f"Estado de voz cambiado a: {estado}")
            
        except Exception as e:
            print(f"Error actualizando configuración de voz: {e}")
    
    def toggle_du64_usage(self, state):
        """Maneja el cambio en el checkbox de uso de du64.exe"""
        try:
            config = load_config()
            use_du64 = state == Qt.CheckState.Checked.value
            config['use_du64'] = use_du64
            save_config(config)
            
            # Actualizar la configuración en la ventana principal
            if hasattr(self, 'main_window') and self.main_window:
                self.main_window.config['use_du64'] = use_du64
            
            # Mostrar tooltip de confirmación
            method = "(Muy Rápido)" if use_du64 else "(Más compatible)"
            pos = QCursor.pos()
            QToolTip.showText(
                pos,
                f"Método de cálculo cambiado a: {method}",
                self,
                QRect(),
                3000
            )
            print(f"Método de cálculo de carpetas cambiado a: {method}")
            
        except Exception as e:
            print(f"Error actualizando configuración de du64: {e}")
    
    def change_size_mode(self, mode_name):
        if hasattr(self, 'main_window') and self.main_window:
            mode = self.size_modes[mode_name]
            for i in range(self.main_window.list_widget.count()):
                item = self.main_window.list_widget.item(i)
                widget = self.main_window.list_widget.itemWidget(item)
                if widget:
                    # Actualizar las propiedades de altura en el widget
                    if hasattr(widget, 'progress_height'):
                        widget.progress_height = int(mode["progress"])
                    if hasattr(widget, 'file_progress_height'):
                        widget.file_progress_height = int(mode["file"])
                    
                    # Aplicar nueva altura al widget
                    widget.setFixedHeight(mode["height"])
                    item.setSizeHint(QSize(item.sizeHint().width(), mode["height"]))
                    self.main_window.list_widget.setIconSize(QSize(mode["icon"], mode["icon"]))
                    
                    # Obtener la letra de unidad para verificar si está copiando
                    drive_letter = None
                    if hasattr(widget, 'volume_label'):
                        text = widget.volume_label.text()
                        if '(' in text and ')' in text:
                            drive_letter = text.split('(')[-1].strip(')')
                    
                    is_copying = False
                    if drive_letter and hasattr(self.main_window, 'is_copying'):
                        is_copying = self.main_window.is_copying(drive_letter)
                    
                    # Calcular posiciones para las barras
                    width = widget.width()
                    volume_width = widget.volume_label.sizeHint().width() + 10
                    progress_width = int(width * 0.2)
                    progress_x = max((width - progress_width) // 2, volume_width + 10)
                    progress_y = mode["file_offset"] - mode["progress"] - 1
                    
                    # Actualizar barra de progreso principal
                    if hasattr(widget, 'progress_bar'):
                        progress_bar = widget.progress_bar
                        
                        # Establecer altura de la barra principal
                        progress_bar.setFixedHeight(mode["progress"])
                        progress_bar.setGeometry(progress_x, progress_y, progress_width, mode["progress"])
                        
                        # Guardar geometría
                        if not hasattr(widget, 'progress_bar_geometry'):
                            widget.progress_bar_geometry = {}
                        widget.progress_bar_geometry = {
                            'x': progress_x,
                            'y': progress_y,
                            'width': progress_width,
                            'height': mode["progress"]
                        }
                        
                        # Forzar actualización de la barra principal
                        if progress_bar.isVisible():
                            current_value = progress_bar.value()
                            progress_bar.setValue(0)  # Reset temporal
                            progress_bar.setValue(current_value)  # Restaurar valor
                            progress_bar.update()
                    
                    # Actualizar barra de progreso de archivos
                    if hasattr(widget, 'file_progress_bar'):
                        file_bar = widget.file_progress_bar
                        
                        # Calcular posición de la barra de archivos
                        file_width = int(width * 0.1)
                        file_x_offset = (progress_width - file_width) // 2
                        file_x = progress_x + file_x_offset
                        file_y = mode["file_offset"]
                        
                        # Establecer altura y posición de la barra de archivos
                        file_bar.setFixedHeight(mode["file"])
                        file_bar.setGeometry(file_x, file_y, file_width, mode["file"])
                        
                        # Guardar geometría
                        if not hasattr(widget, 'file_progress_bar_geometry'):
                            widget.file_progress_bar_geometry = {}
                        widget.file_progress_bar_geometry = {
                            'x': file_x,
                            'y': file_y,
                            'width': file_width,
                            'height': mode["file"]
                        }
                        
                        # Forzar actualización de la barra de archivos
                        if file_bar.isVisible():
                            current_value = file_bar.value()
                            file_bar.setValue(0)  # Reset temporal
                            file_bar.setValue(current_value)  # Restaurar valor
                        
                        # Forzar actualización adicional si está copiando
                        if is_copying:
                            file_bar.show()  # Asegurar que sea visible
                            file_bar.update()
                            file_bar.repaint()
                    
                    # Actualizar fuentes y estilos
                    font = QFont()
                    font.setBold(True)
                    font.setPointSize(mode["font"])
                    style = f"color: white; font-size: {mode['font']}px;"
                    for label in [widget.volume_label, widget.size_label, widget.speed_label]:
                        label.setFont(font)
                        label.setStyleSheet(style)
                    
                    # Forzar actualización del widget completo
                    widget.update()
                    widget.repaint()
            
            # Guardar configuración
            self.main_window.config['disk_scale'] = mode["scale"]
            self.main_window.config['size_mode'] = mode_name
            save_config(self.main_window.config)
            
            # Procesar eventos pendientes y actualizar la lista
            QApplication.processEvents()
            self.main_window.list_widget.update()

    def setup_indexar_tab(self, tab):
        monitoreo_layout = QVBoxLayout(tab)
        index_button = QPushButton("Indexar")
        index_button.setFixedSize(100, 40)  # Tamaño fijo más pequeño
        index_button.setCursor(Qt.CursorShape.PointingHandCursor)
        index_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
                border: none;
                border-radius: 18px;
                padding: 8px 20px;
                text-align: center;
                font-weight: bold;
                margin: 1px 0px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QPushButton:pressed {
                background-color: rgba(0, 120, 215, 0.6);
            }
        """)
        monitoreo_layout.addWidget(index_button, 0, Qt.AlignmentFlag.AlignLeft)
        self.drop_area = DropArea()
        index_button.clicked.connect(self.drop_area.index_disks)
        monitoreo_layout.addWidget(self.drop_area)

    def setup_pagos_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # Combo box para tipo de pago
        tipo_pago_layout = QHBoxLayout()
        tipo_pago_label = QLabel("Tipo de pago:")
        self.tipo_pago_combo = QComboBox()
        self.tipo_pago_combo.addItem("Pago por Dispositivo")
        self.tipo_pago_combo.addItem("Pago por Duración")
        self.tipo_pago_combo.addItem("Pago por Ficheros")
        self.tipo_pago_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.tipo_pago_combo.setMinimumWidth(200)
        
        tipo_pago_layout.addWidget(tipo_pago_label)
        tipo_pago_layout.addWidget(self.tipo_pago_combo)
        tipo_pago_layout.addStretch(1)
        layout.addLayout(tipo_pago_layout)
        
        # Cargar configuración
        config = load_config()
        modo_pago = config.get('modo_pago', 'dispositivo')  # Cambiado el valor por defecto a 'dispositivo'
        
        # Desconectar temporalmente la señal para evitar actualizaciones duplicadas
        self.tipo_pago_combo.blockSignals(True)
        
        if modo_pago == "duracion":
            self.tipo_pago_combo.setCurrentIndex(1)
        elif modo_pago == "ficheros":
            self.tipo_pago_combo.setCurrentIndex(2)
        else:
            self.tipo_pago_combo.setCurrentIndex(0)  # Por defecto selecciona "Pago por Dispositivo" (GB)
        
        # Reconectar la señal
        self.tipo_pago_combo.blockSignals(False)
        
        # Layout para tamaño/duración
        tamano_layout = QHBoxLayout()
        self.tamano_label = QLabel("Tamaño en GB:")
        self.tamano_input = QLineEdit()
        self.tamano_input.setFixedWidth(80)
        self.tamano_input.setPlaceholderText("GB")  # Establecer placeholder inicial
        tamano_layout.addWidget(self.tamano_label)
        tamano_layout.addWidget(self.tamano_input)
        tamano_layout.addStretch(1)
        layout.addLayout(tamano_layout)
        
        # Layout para precio (dispositivo/duración)
        precio_layout = QHBoxLayout()
        self.precio_label = QLabel("Precio ($):")
        self.precio_input = QLineEdit()
        self.precio_input.setFixedWidth(80)
        precio_layout.addWidget(self.precio_label)
        precio_layout.addWidget(self.precio_input)
        precio_layout.addStretch(1)
        layout.addLayout(precio_layout)

        # Campos para método de pago por ficheros (disco + directorio)
        ficheros_group = QGroupBox("Configuración de Pago por Ficheros")
        ficheros_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        ficheros_layout = QVBoxLayout(ficheros_group)
        
        # Layout para selección de disco
        disco_layout = QHBoxLayout()
        self.disco_label = QLabel("Disco:")
        self.disco_combo = QComboBox()
        self.disco_combo.setMinimumWidth(250)  # Ampliado para mostrar mejor los nombres
        disco_layout.addWidget(self.disco_label)
        disco_layout.addWidget(self.disco_combo)
        disco_layout.addStretch(1)
        ficheros_layout.addLayout(disco_layout)
        
        # Layout para selección de directorio
        directorio_layout = QHBoxLayout()
        self.directorio_label = QLabel("Directorio:")
        self.directorio_input = QLineEdit()
        self.directorio_input.setMinimumWidth(200)
        self.seleccionar_directorio_btn = QPushButton("...")
        self.seleccionar_directorio_btn.setFixedWidth(30)
        self.seleccionar_directorio_btn.clicked.connect(self.seleccionar_directorio)
        directorio_layout.addWidget(self.directorio_label)
        directorio_layout.addWidget(self.directorio_input)
        directorio_layout.addWidget(self.seleccionar_directorio_btn)
        ficheros_layout.addLayout(directorio_layout)
        
        # Layout para precio de ficheros
        precio_ficheros_layout = QHBoxLayout()
        self.precio_ficheros_label = QLabel("Precio ($):")
        self.precio_ficheros_input = QLineEdit()
        self.precio_ficheros_input.setFixedWidth(80)
        precio_ficheros_layout.addWidget(self.precio_ficheros_label)
        precio_ficheros_layout.addWidget(self.precio_ficheros_input)
        precio_ficheros_layout.addStretch(1)
        ficheros_layout.addLayout(precio_ficheros_layout)
        
        # Ocultar inicialmente el grupo de ficheros
        ficheros_group.hide()
        layout.addWidget(ficheros_group)
        
        # Separador visual
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)

        # Lista de precios
        precios_label = QLabel("Lista de precios configurados:")
        precios_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(precios_label)
        
        self.precios_list = QListWidget()
        layout.addWidget(self.precios_list)

        # Botones para gestionar precios
        buttons_layout = QHBoxLayout()
        
        agregar_button = QPushButton("Agregar")
        agregar_button.clicked.connect(self.agregar_precio)
        buttons_layout.addWidget(agregar_button)
        
        borrar_button = QPushButton("Borrar")
        borrar_button.clicked.connect(self.borrar_precio_seleccionado)
        buttons_layout.addWidget(borrar_button)
        
        buttons_layout.addStretch(1)
        layout.addLayout(buttons_layout)
        
        # Separador visual
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.HLine)
        separator2.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator2)
        
        # Sección de discos exentos
        exentos_label = QLabel("Discos exentos de pago:")
        exentos_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(exentos_label)
        
        self.discos_exentos_list = QListWidget()
        self.discos_exentos_list.itemChanged.connect(self.on_disk_checkbox_changed)
        layout.addWidget(self.discos_exentos_list)
        
        # Botones para gestionar discos exentos
        exentos_layout = QHBoxLayout()
        actualizar_button = QPushButton("Actualizar lista de discos")
        actualizar_button.clicked.connect(self.actualizar_lista_discos)
        
        guardar_exentos_button = QPushButton("Guardar discos exentos")
        guardar_exentos_button.clicked.connect(self.guardar_discos_exentos)
        
        exentos_layout.addWidget(actualizar_button)
        exentos_layout.addWidget(guardar_exentos_button)
        exentos_layout.addStretch(1)
        layout.addLayout(exentos_layout)

        # Conectar señal de cambio de tipo de pago
        self.tipo_pago_combo.currentIndexChanged.connect(self.on_tipo_pago_changed)
        
        # Inicializar discos disponibles
        self.actualizar_discos_disponibles()
        
        # Aplicar configuración inicial explícitamente
        QTimer.singleShot(0, lambda: self.on_tipo_pago_changed(self.tipo_pago_combo.currentIndex()))
    
    def seleccionar_carpeta(self):
        """Abre el diálogo para seleccionar una carpeta"""
        ruta = QFileDialog.getExistingDirectory(
            self,
            "Seleccionar Carpeta",
            "",
            QFileDialog.Option.ShowDirsOnly | QFileDialog.Option.DontResolveSymlinks
        )
        if ruta:
            self.ruta_input.setText(ruta)
            # Guardar la ruta en la configuración
            config = load_config()
            config['ruta_ficheros'] = ruta
            save_config(config)
    
    def on_tipo_pago_changed(self, index):
        """Actualiza la interfaz según el tipo de pago seleccionado"""
        try:
            # Cargar configuración actual
            config = load_config()
            
            # Verificar que los widgets existen antes de usarlos
            if hasattr(self, 'tamano_label'):
                if index == 0:  # Dispositivo
                    config['modo_pago'] = "dispositivo"
                    self.tamano_label.setText("Tamaño en GB:")
                    self.tamano_label.show()
                    self.tamano_input.setPlaceholderText("GB")  # Cambiado a GB para modo dispositivo
                    self.tamano_input.show()
                    self.precio_label.show()
                    self.precio_input.show()
                    
                    # Ocultar el grupo de ficheros
                    for widget in [w for w in self.findChildren(QGroupBox) if "Configuración de Pago por Ficheros" in w.title()]:
                        widget.hide()
                    
                elif index == 1:  # Duración
                    config['modo_pago'] = "duracion"
                    self.tamano_label.setText("Duración (min):")
                    self.tamano_label.show()
                    self.tamano_input.setPlaceholderText("Minutos")  # Mantenido Minutos para modo duración
                    self.tamano_input.show()
                    self.precio_label.show()
                    self.precio_input.show()
                    
                    # Ocultar el grupo de ficheros
                    for widget in [w for w in self.findChildren(QGroupBox) if "Configuración de Pago por Ficheros" in w.title()]:
                        widget.hide()
                    
                elif index == 2:  # Ficheros
                    config['modo_pago'] = "ficheros"
                    
                    # Ocultar campos de duración/dispositivo
                    self.tamano_label.hide()
                    self.tamano_input.hide()
                    self.precio_label.hide()
                    self.precio_input.hide()
                    
                    # Mostrar el grupo de ficheros
                    for widget in [w for w in self.findChildren(QGroupBox) if "Configuración de Pago por Ficheros" in w.title()]:
                        widget.show()
                    
                    # Actualizar la lista de discos disponibles
                    self.actualizar_discos_disponibles()
                
                # Guardar la configuración
                save_config(config)
                
                # Actualizar la lista de precios
                self.cargar_precios()
                
                # Actualizar la configuración en la ventana principal si existe
                if hasattr(self, 'main_window') and self.main_window is not None:
                    # Actualizar la configuración en la ventana principal
                    self.main_window.config = load_config()  # Recargar la configuración
                    # Configuración actualizada (optimizado)
                    
                    # Forzar recálculo de precios si hay copias pendientes
                    if hasattr(self.main_window, 'total_sizes'):
                        for drive_letter, total_size in self.main_window.total_sizes.items():
                            if total_size > 0:
                                # Limpiar caché de precios procesados para forzar recálculo
                                if hasattr(self.main_window, 'precios_procesados'):
                                    self.main_window.precios_procesados.pop(drive_letter, None)
                                
                                # Recalcular precio según el nuevo modo
                                if config['modo_pago'] == "duracion":
                                    duracion_total = 0
                                    if drive_letter in self.main_window.queues:
                                        for source, _ in list(self.main_window.queues[drive_letter].queue):
                                            if os.path.exists(source):
                                                duracion = self.main_window.get_video_duration(source)
                                                if duracion is not None:
                                                    duracion_total += duracion
                                    self.main_window.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
                                elif config['modo_pago'] == "ficheros":
                                    total_files = 0
                                    if drive_letter in self.main_window.queues:
                                        total_files = self.main_window.queues[drive_letter].qsize()
                                    self.main_window.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files)
                                else:  # modo dispositivo
                                    total_size_gb = total_size / (1024 ** 3)
                                    self.main_window.calcular_y_mostrar_precio(drive_letter, total_size_gb)
                                
                                # Precio recalculado (optimizado)
        except Exception as e:
            print(f"Error al cambiar tipo de pago: {e}")
            import traceback
            traceback.print_exc()

    def get_button_style(self, background_color, hover_color=None, pressed_color=None):
        if not hover_color:
            hover_color = self.adjust_color(background_color, 1.1)
        if not pressed_color:
            pressed_color = self.adjust_color(background_color, 0.9)
        return f"""
            QPushButton {{
                background-color: {background_color};
                color: white;
                border: none;
                border-radius: 18px;
                padding: 8px 20px;
                text-align: center;
                font-weight: bold;
                margin: 1px 0px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
        """

    def adjust_color(self, color, factor):
        color = color.lstrip('#')
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        r = min(255, int(r * factor))
        g = min(255, int(g * factor))
        b = min(255, int(b * factor))
        return f"#{r:02x}{g:02x}{b:02x}"

    def showEvent(self, event):
        # Asegurar que el botón tenga el tamaño correcto al mostrar
        if hasattr(self, 'close_button'):
            self.close_button.icon_size = self._close_button_size
            self.close_button.setIconSize(QSize(self._close_button_size, self._close_button_size))
        super().showEvent(event)
        # Solo aplicamos el efecto una vez aquí
        apply_acrylic_and_rounded(int(self.winId()))
        self.installEventFilter(self)
        
        # Evitar recargar datos si ya están cargados
        if not hasattr(self, '_data_loaded') or not self._data_loaded:
            # Diferir la carga de datos para después de mostrar la ventana
            QTimer.singleShot(10, self._load_data_after_show)
            self._data_loaded = True

    def _load_data_after_show(self):
        """Carga los datos después de mostrar la ventana"""
        # Solo actualizar si es necesario
        if not hasattr(self, 'discos_exentos') or not self.discos_exentos:
            self.cargar_discos_exentos()
        
        # Verificar si la lista de discos está vacía antes de actualizarla
        if self.discos_exentos_list.count() == 0:
            self.actualizar_lista_discos()

    def change_progress_bar_color(self):
        current_pos = self.mapToGlobal(self.rect().topRight())
        color_dialog = QColorDialog(QColor(self.progress_bar_color), self)
        color_dialog.setOption(QColorDialog.ColorDialogOption.DontUseNativeDialog, False)
        color_dialog.setStyle(QStyleFactory.create("Fusion"))
        color_dialog.setStyleSheet("""QColorDialog {
                background-color: #f0f0f0;
                color: #000000;
            }
            QColorDialog QWidget {
                background-color: #f0f0f0;
                color: #000000;
            }
        """)
        if color_dialog.exec() == QColorDialog.DialogCode.Accepted:
            color = color_dialog.currentColor()
            if color.isValid():
                self.progress_bar_color = color.name()
                self.color_button.setStyleSheet(f"background-color: {self.progress_bar_color};")
                if hasattr(self, 'main_window') and self.main_window:
                    self.main_window.update_progress_bar_color(self.progress_bar_color)
                self.save_settings()
        self.raise_()
        self.activateWindow()

    def animate_question_mark(self):
        if not hasattr(self, 'question_y_offset'):
            self.question_y_offset = 0
            self.animation_direction = 1
        self.question_y_offset += self.animation_direction * 2
        if abs(self.question_y_offset) > 10:
            self.animation_direction *= -1
        self.show_qr_code(None)

    def setup_port_mapping_tab(self, tab):
        """Crea la pestaña de mapeo de puertos usando la nueva lógica"""
        layout = QVBoxLayout(tab)
        
        # Añadir botón de actualización en la parte superior
        refresh_button_container = QWidget()
        refresh_button_layout = QHBoxLayout(refresh_button_container)
        refresh_button_layout.setContentsMargins(10, 5, 10, 5)
        
        # Crear botón de actualización con texto
        refresh_button = QPushButton("ACTUALIZAR")
        refresh_button.setCursor(Qt.CursorShape.PointingHandCursor)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #0088cc;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0077b5;
            }
            QPushButton:pressed {
                background-color: #006699;
            }
        """)
        refresh_button.clicked.connect(self.refresh_port_status_new)
        refresh_button_layout.addStretch()
        refresh_button_layout.addWidget(refresh_button)
        refresh_button_layout.addStretch()
        layout.addWidget(refresh_button_container)
        
        # Contenedor para la lista de puertos
        self.ports_list_widget = QWidget()
        ports_layout = QVBoxLayout(self.ports_list_widget)
        layout.addWidget(self.ports_list_widget)

        # Cargar puertos desde PUERTOS_USB.txt
        self.load_ports_from_puertos_usb()
    
    def on_mapeo_changed(self, checked):
        if checked:  # Solo procesamos cuando se activa un radio button
            is_active = self.mapeo_activo.isChecked()
            config = load_config()
            config['show_port_mapping'] = is_active
            save_config(config)
            print(f"Estado del mapeo guardado en config.json: {is_active}")
            if self.main_window:
                # Actualizar la vista con la nueva configuración de mapeo
                self.main_window.refresh_ports()  # Actualiza la información de puertos
                volumes = self.main_window.worker.get_connected_volumes()
                self.main_window.update_volume_list(volumes)
                self.main_window.actualizar_vista_discos()

    def update_toggle_state(self):
        """Actualiza el estado de los radio buttons según la configuración guardada"""
        try:
            if hasattr(self, 'mapeo_activo') and hasattr(self, 'mapeo_inactivo'):
                config = load_config()
                is_mapeo_active = config.get('show_port_mapping', False)
                print(f"Estado leído de config: {is_mapeo_active}")
                self.mapeo_activo.blockSignals(True)
                self.mapeo_inactivo.blockSignals(True)
                if is_mapeo_active:
                    self.mapeo_activo.setChecked(True)
                else:
                    self.mapeo_inactivo.setChecked(True)
                self.mapeo_activo.blockSignals(False)
                self.mapeo_inactivo.blockSignals(False)
        except Exception as e:
            print(f"Error actualizando estado del mapeo: {e}")



    
    def load_ports_from_txt_new(self):
        """Carga la información de puertos usando la nueva lógica de MAPEO_PUERTO"""
        # Evitar ejecuciones múltiples durante la inicialización
        if hasattr(self, '_ports_new_loaded') and self._ports_new_loaded:
            return
        self._ports_new_loaded = True
        try:
            if self.ports_list_widget.layout():
                while self.ports_list_widget.layout().count():
                    child = self.ports_list_widget.layout().takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

            puerto_icon = boton_mapear_puertos(size=20).pixmap(20, 20)

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(3)
            scroll_layout.setContentsMargins(5, 5, 5, 5)

            # Usar la nueva lógica de MAPEO_PUERTO para obtener dispositivos USB
            try:
                from MAPEO_PUERTO import USBPortMapper
                port_mapper = USBPortMapper()
                usb_devices = port_mapper.get_usb_devices()
                
                print(f"Dispositivos USB encontrados: {len(usb_devices)}")
                
                # Crear widgets para cada dispositivo USB encontrado
                for device in usb_devices:
                    port_info = device.get('PortInfo', 'No disponible')
                    if port_info == 'No disponible':
                        continue
                        
                    # Obtener nombre personalizado si existe
                    custom_name = port_mapper.port_names.get(port_info, '')
                    
                    container = QWidget()
                    container_layout = QHBoxLayout(container)
                    container_layout.setContentsMargins(5, 2, 5, 2)
                    container_layout.setSpacing(5)
                    
                    # Color de fondo
                    bg_color = "rgba(50, 50, 50, 0.5)"
                    container.setStyleSheet(f"""
                        QWidget {{
                            background-color: {bg_color};
                            border-radius: 5px;
                        }}
                    """)
                    
                    # Icono del puerto
                    puerto_label = QLabel()
                    puerto_label.setPixmap(puerto_icon.scaled(20, 20))
                    puerto_label.setFixedSize(20, 20)
                    container_layout.addWidget(puerto_label)
                    
                    # Información del dispositivo
                    device_info = f"{port_info} - {device.get('Model', 'N/A')} ({device.get('DriveLetters', 'Sin unidad')})"
                    port_label = QLabel(device_info)
                    port_label.setStyleSheet("""
                        color: white;
                        font-family: Consolas, monospace;
                        font-size: 12px;
                    """)
                    container_layout.addWidget(port_label)
                    
                    # Campo de entrada para el nombre personalizado
                    name_edit = QLineEdit(custom_name)
                    name_edit.setPlaceholderText("Nombre personalizado...")
                    name_edit.setStyleSheet("""
                        QLineEdit {
                            background-color: rgba(255, 255, 255, 0.1);
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 2px 4px;
                            font-family: Consolas, monospace;
                            font-size: 12px;
                        }
                        QLineEdit:focus {
                            background-color: rgba(255, 255, 255, 0.15);
                        }
                    """)
                    name_edit.setFixedWidth(150)
                    name_edit.textChanged.connect(lambda text, port=port_info: self.save_port_name_new(port, text, port_mapper))
                    container_layout.addWidget(name_edit)
                    
                    # Botón para renombrar usando el diálogo moderno
                    rename_button = QPushButton("✏️")
                    rename_button.setStyleSheet("""
                        QPushButton {
                            color: #4CAF50;
                            background-color: transparent;
                            border: none;
                            font-size: 16px;
                            padding: 2px;
                            min-width: 25px;
                            max-width: 25px;
                            min-height: 25px;
                            max-height: 25px;
                        }
                        QPushButton:hover {
                            background-color: rgba(76, 175, 80, 0.2);
                            border-radius: 12px;
                        }
                    """)
                    rename_button.setCursor(Qt.CursorShape.PointingHandCursor)
                    rename_button.setToolTip("Renombrar puerto con diálogo avanzado")
                    rename_button.clicked.connect(lambda checked=False, dev=device: self.show_modern_rename_dialog(dev))
                    container_layout.addWidget(rename_button)
                    
                    container_layout.addStretch(1)
                    
                    # Botón eliminar
                    delete_button = QPushButton("×")
                    delete_button.setStyleSheet("""
                        QPushButton {
                            color: #ff5555;
                            background-color: transparent;
                            border: none;
                            font-weight: bold;
                            font-size: 18px;
                            padding: 0px;
                            margin: 0px;
                            min-width: 20px;
                            max-width: 20px;
                            min-height: 20px;
                            max-height: 20px;
                        }
                        QPushButton:hover {
                            color: #ff0000;
                            background-color: rgba(255, 0, 0, 0.1);
                            border-radius: 10px;
                        }
                        QPushButton:pressed {
                            color: white;
                            background-color: #ff0000;
                        }
                    """)
                    delete_button.setCursor(Qt.CursorShape.PointingHandCursor)
                    delete_button.setToolTip(f"Eliminar puerto {port_info}")
                    delete_button.clicked.connect(lambda checked=False, port=port_info: self.delete_port_new(port, port_mapper))
                    container_layout.addWidget(delete_button)
                    
                    container.setFixedHeight(35)
                    scroll_layout.addWidget(container)
                
                if not usb_devices:
                    # Mostrar mensaje si no hay dispositivos
                    no_devices_label = QLabel("No se encontraron dispositivos USB conectados")
                    no_devices_label.setStyleSheet("""
                        color: #888888;
                        font-style: italic;
                        padding: 20px;
                        text-align: center;
                    """)
                    no_devices_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    scroll_layout.addWidget(no_devices_label)
                
            except Exception as e:
                print(f"Error usando nueva lógica de mapeo: {e}")
                # Fallback a método anterior si falla
                self.load_ports_from_txt_fallback()
                return
            
            scroll_layout.addStretch()
            
            scroll_area = QScrollArea()
            scroll_area.setWidget(scroll_widget)
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: none;
                    background: transparent;
                }
                QScrollBar:vertical {
                    width: 10px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 5px;
                }
                QScrollBar::handle:vertical {
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 4px;
                    min-height: 20px;
                }
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
            """)
            
            self.ports_list_widget.layout().addWidget(scroll_area)
            # Pestaña de mapeo de puertos actualizada (optimizado)
            
        except Exception as e:
            print(f"Error cargando puertos con nueva lógica: {e}")
            import traceback
            traceback.print_exc()

    def delete_port_new(self, port_id, port_mapper):
        """Elimina un puerto usando la nueva lógica"""
        try:
            # Mostrar diálogo de confirmación
            confirm = QMessageBox()
            confirm.setWindowTitle("Confirmar eliminación")
            confirm.setText(f"¿Estás seguro de que deseas eliminar el puerto {port_id}?")
            confirm.setIcon(QMessageBox.Icon.Question)
            confirm.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            confirm.setDefaultButton(QMessageBox.StandardButton.No)
            
            # Aplicar estilo moderno al diálogo
            confirm.setStyleSheet("""
                QMessageBox {
                    background-color: #2d2d2d;
                    color: white;
                }
                QLabel {
                    color: white;
                }
                QPushButton {
                    background-color: #3a3a3a;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #454545;
                }
                QPushButton:pressed {
                    background-color: #555555;
                }
            """)
            
            # Si el usuario confirma, eliminar el puerto
            if confirm.exec() == QMessageBox.StandardButton.Yes:
                # Eliminar usando la nueva lógica
                if port_id in port_mapper.port_names:
                    del port_mapper.port_names[port_id]
                    port_mapper.save_port_names()
                
                # Mostrar notificación de éxito
                pos = QCursor.pos()
                QToolTip.showText(
                    pos,
                    f"Puerto {port_id} eliminado correctamente",
                    self,
                    QRect(),
                    3000
                )
                
                # Actualizar la vista
                self.load_ports_from_txt_new()
                
                # Limpiar caché si es posible
                try:
                    from MAPEO_PUERTO import clear_usb_port_cache
                    clear_usb_port_cache()
                except Exception as e:
                    print(f"Error limpiando caché: {e}")
                
                # Actualizar vista principal si existe
                if self.main_window:
                    try:
                        if hasattr(self.main_window, 'refresh_ports'):
                            self.main_window.refresh_ports()
                        if hasattr(self.main_window, 'actualizar_vista_discos'):
                            self.main_window.actualizar_vista_discos()
                    except Exception as e:
                        print(f"Error actualizando vista principal: {e}")
        
        except Exception as e:
            print(f"Error eliminando puerto con nueva lógica: {e}")

    def delete_port(self, port_id):
        """Método de compatibilidad - usa la nueva lógica"""
        try:
            from MAPEO_PUERTO import USBPortMapper
            port_mapper = USBPortMapper()
            self.delete_port_new(port_id, port_mapper)
        except Exception as e:
            print(f"Error en delete_port: {e}")

    def show_modern_rename_dialog(self, device_info):
        """Muestra el diálogo moderno de renombrado de puertos"""
        try:
            from MAPEO_PUERTO import ModernPortRenameDialog
            
            port_info = device_info.get('PortInfo', '')
            hub_info = device_info.get('HubInfo', '')
            drive_letters = device_info.get('DriveLetters', '')
            
            dialog = ModernPortRenameDialog(
                parent=self,
                drive_letter=drive_letters,
                port_info=port_info,
                hub_info=hub_info
            )
            
            result = dialog.exec()
            if result == QDialog.DialogCode.Accepted:
                # Actualizar la vista después del diálogo
                self.load_ports_from_txt_new()
                
        except Exception as e:
            print(f"Error mostrando diálogo moderno: {e}")
            import traceback
            traceback.print_exc()

    def load_ports_from_txt_fallback(self):
        """Método de respaldo usando la lógica anterior"""
        try:
            # Implementación básica de respaldo
            if self.ports_list_widget.layout():
                while self.ports_list_widget.layout().count():
                    child = self.ports_list_widget.layout().takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            
            fallback_label = QLabel("Error cargando nueva lógica - usando método de respaldo")
            fallback_label.setStyleSheet("color: #ff9999; font-style: italic; padding: 20px;")
            fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            scroll_layout.addWidget(fallback_label)
            
            scroll_area = QScrollArea()
            scroll_area.setWidget(scroll_widget)
            scroll_area.setWidgetResizable(True)
            
            self.ports_list_widget.layout().addWidget(scroll_area)
            
        except Exception as e:
            print(f"Error en método de respaldo: {e}")
    
    def save_port_name_new(self, port_id, new_name, port_mapper):
        """Guarda el nombre del puerto usando la nueva lógica"""
        try:
            if new_name.strip():
                port_mapper.port_names[port_id] = new_name.strip()
            else:
                port_mapper.port_names.pop(port_id, None)
            
            port_mapper.save_port_names()
            print(f"Nombre del puerto {port_id} actualizado usando nueva lógica")
            
        except Exception as e:
            print(f"Error guardando nombre del puerto con nueva lógica: {e}")

    def save_port_name(self, port_id, new_name):
        """Método de compatibilidad - usa la nueva lógica"""
        try:
            from MAPEO_PUERTO import USBPortMapper
            port_mapper = USBPortMapper()
            self.save_port_name_new(port_id, new_name, port_mapper)
        except Exception as e:
            print(f"Error en save_port_name: {e}")

    def refresh_port_status_new(self):
        """Actualiza el estado de los puertos desde los archivos"""
        try:
            # Desactivar el botón mientras se actualiza
            sender = self.sender()
            if isinstance(sender, QPushButton):
                sender.setEnabled(False)
                original_text = sender.text()
                sender.setText("ACTUALIZANDO...")
                QApplication.processEvents()
            
            # Recargar desde los archivos usando la nueva función
            self.load_ports_from_puertos_usb()
                
            # Restaurar el estado del botón
            if isinstance(sender, QPushButton):
                sender.setText(original_text)
                sender.setEnabled(True)

        except Exception as e:
            print(f"Error actualizando puertos desde PUERTOS_USB.txt: {e}")
            if isinstance(sender, QPushButton):
                sender.setText("ACTUALIZAR")
                sender.setEnabled(True)

    def refresh_port_status(self):
        """Método de compatibilidad - redirige a la nueva lógica"""
        self.refresh_port_status_new()

    def refresh_ports_new(self):
        """Actualiza la lista de puertos usando la nueva lógica de MAPEO_PUERTO"""
        try:
            from MAPEO_PUERTO import USBPortMapper
            port_mapper = USBPortMapper()
            usb_devices = port_mapper.get_usb_devices()
            
            # Crear array de puertos con la nueva estructura
            port_array = []
            for device in usb_devices:
                port_data = {
                    'id': device.get('PortInfo', 'No disponible'),
                    'device': device.get('Model', 'N/A'),
                    'interface': 'USB',
                    'size': device.get('Size', 0),
                    'status': 'active',
                    'brand': device.get('Brand', 'Unknown'),
                    'drive_letters': device.get('DriveLetters', 'Sin asignar'),
                    'hub_info': device.get('HubInfo', 'No disponible'),
                    'custom_name': port_mapper.port_names.get(device.get('PortInfo', ''), '')
                }
                port_array.append(port_data)
            
            # Ordenar por ID de puerto
            port_array.sort(key=lambda x: x['id'])
            
            # Guardar el array actualizado
            self.port_array = port_array
            
            # Actualizar la interfaz
            self.load_ports_from_txt_new()
            
            return port_array
            
        except Exception as e:
            print(f"Error actualizando puertos con nueva lógica: {e}")
            return []

    def refresh_ports(self):
        """Método de compatibilidad - redirige a la nueva lógica"""
        return self.refresh_ports_new()

    def toggle_disk_lines(self, state):
        """Maneja el cambio de estado del checkbox de líneas divisorias"""
        show_lines = bool(state)
        self.main_window.config['show_disk_lines'] = show_lines
        save_config(self.main_window.config)
        if hasattr(self.main_window, 'list_widget'):
            self.main_window.list_widget.show_disk_lines = show_lines
            self.main_window.list_widget.update()

    def setup_about_tab(self, tab):
        about_layout = QVBoxLayout(tab)
        about_layout.setContentsMargins(20, 20, 20, 20)
        
        # Contenedor principal con fondo
        content_widget = QWidget()
        content_widget.setObjectName("aboutContainer")
        content_widget.setStyleSheet("""
            QWidget#aboutContainer {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(20)

        # Logo de ZETACOPY
        icon_label = QLabel()
        icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'ZETACOPY.ico')
        icon_pixmap = QPixmap(icon_path).scaled(
            128, 128, 
            Qt.AspectRatioMode.KeepAspectRatio, 
            Qt.TransformationMode.SmoothTransformation
        )
        
        effect = QGraphicsDropShadowEffect()
        effect.setBlurRadius(30)
        effect.setColor(QColor(0, 0, 0))
        effect.setOffset(0, 0)
        
        icon_label.setPixmap(icon_pixmap)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setGraphicsEffect(effect)
        content_layout.addWidget(icon_label)

        # Texto informativo
        about_text = QLabel("""
            <div style='text-align: center;'>
                <p style='font-size: 24px; font-weight: bold; color: #00a2ff; margin-bottom: 20px;'>ZETACOPY</p>
                <p style='font-size: 16px; color: white; margin: 15px 0;'>Gestor de Copias Avanzado</p>
                <p style='font-size: 14px; color: white; margin: 10px 0;'>Desarrollado por Jorge Yadiel Prado</p>
                <p style='font-size: 14px; color: white; margin: 10px 0;'>Contacto: +58668183</p>
            </div>
        """)
        about_text.setWordWrap(True)
        about_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        about_text.setStyleSheet("color: white; font-size: 14px;")
        content_layout.addWidget(about_text)

        # Contenedor de redes sociales con QRs
        social_container = QWidget()
        social_layout = QHBoxLayout(social_container)
        social_layout.setSpacing(40)
        social_layout.setContentsMargins(0, 0, 0, 0)
        social_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Telegram con QR
        telegram_container = self._create_social_block(
            icono_TELEGRAM(size=40), 
            "Unirse al Canal",
            "https://t.me/Zetacopy"
        )
        
        # WhatsApp con QR
        whatsapp_container = self._create_social_block(
            icono_WhatsApp(size=40), 
            "Contacto por WhatsApp",
            "https://chat.whatsapp.com/C72nEwWoDOy5ly4QmdXyQa"  # Reemplazar con número real
        )

        social_layout.addWidget(telegram_container)
        social_layout.addWidget(whatsapp_container)
        content_layout.addWidget(social_container)

        # Pie de página
        footer_label = QLabel("© 2024 Todos los derechos reservados")
        footer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        footer_label.setStyleSheet("color: white; font-size: 12px;")
        content_layout.addWidget(footer_label)

        about_layout.addWidget(content_widget)
        about_layout.addStretch(1)

    # Métodos auxiliares
    def _create_social_block(self, icon, text, url):
        """Crea un bloque vertical con ícono, texto y QR"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setSpacing(10)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Botón con ícono y texto
        button_container = self._create_social_button(icon, text, url)
        layout.addWidget(button_container)

        # QR asociado
        qr_label = self._generate_qr(url)
        layout.addWidget(qr_label)

        return container

    def _create_social_button(self, icon, text, url):
        """Crea un botón con ícono y texto"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setSpacing(5)
        layout.setContentsMargins(0, 0, 0, 0)
        
        button = QPushButton()
        button.setIcon(icon)
        button.setIconSize(QSize(40, 40))
        button.setFixedSize(50, 50)
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 25px;
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        
        label = QLabel(text)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: white; font-size: 13px;")
        
        import webbrowser
        button.clicked.connect(lambda: webbrowser.open(url))
        
        layout.addWidget(button, 0, Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label, 0, Qt.AlignmentFlag.AlignCenter)
        
        return container

    def _generate_qr(self, url):
        """Genera un código QR para una URL"""
        qr = segno.make(url, error='L')
        buffer = BytesIO()
        qr.save(buffer, kind='png', scale=6, dark='black', light=None)
        qr_pixmap = QPixmap()
        qr_pixmap.loadFromData(buffer.getvalue())
        
        qr_label = QLabel()
        qr_label.setPixmap(qr_pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio))
        qr_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        qr_label.setCursor(Qt.CursorShape.PointingHandCursor)
        qr_label.setToolTip(f"Escanea para abrir {url}")
        
        return qr_label

    def update_glow_effect(self):
        """Actualiza el color del efecto de brillo"""
        self.hue = (self.hue + 2) % 360
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == "Acerca de":
                tab = self.tab_widget.widget(i)
                if tab:
                    tab.update()
                    break

    def paint_glow_effect(self, event, widget):
        """Pinta el efecto de brillo alrededor del icono"""
        painter = QPainter(widget)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        center = widget.rect().center()
        cx = center.x()
        cy = center.y()
        gradient = QRadialGradient(float(cx), float(cy), 60.0)
        color = QColor()
        color.setHsv(self.hue, 255, 255, 100)
        gradient.setColorAt(0, color)
        gradient.setColorAt(0.5, QColor(color.red(), color.green(), color.blue(), 50))
        gradient.setColorAt(1, QColor(0, 0, 0, 0))
        painter.setBrush(gradient)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(center, 60, 60)

    

    def closeEvent(self, event):
        """Sobrescribir el evento de cierre para prevenir cierres no deseados"""
        try:
            # Guardar el modo de pago actual
            if hasattr(self, 'tipo_pago_combo'):
                config = load_config()
                index = self.tipo_pago_combo.currentIndex()
                
                # Determinar el modo de pago según el índice
                if index == 1:
                    modo_pago = "duracion"
                elif index == 2:
                    modo_pago = "ficheros"
                else:
                    modo_pago = "dispositivo"  # Valor por defecto para pago por GB
                    
                config['modo_pago'] = modo_pago
                save_config(config)
                # Modo de pago guardado (optimizado)

            # Comportamiento normal de cierre
            if hasattr(self, 'prevent_close') and self.prevent_close:
                event.ignore()
            else:
                if hasattr(self, 'close_button'):
                    self.close_button.icon_size = self._close_button_size
                    self.close_button.setIconSize(QSize(self._close_button_size, self._close_button_size))
                self.hide()
                event.ignore()
        except Exception as e:
            print(f"Error al cerrar ventana de ajustes: {e}")
            event.ignore()

    def update_background_opacity(self, value):
        """Actualiza la opacidad del widget de fondo"""
        try:
            actual_opacity = max(4, value)
            mapped_value = int(((actual_opacity - 4) / (255 - 4)) * 100)
            self.opacity_value.setText(str(mapped_value))
            if self.main_window:
                config = load_config()
                config['background_opacity'] = actual_opacity
                save_config(config)
                self.main_window.transparent_widget.setStyleSheet(f"""
                    QWidget {{
                        background-color: rgba(32, 32, 32, {actual_opacity});
                    }}
                """)
                self.main_window.config = config
        except Exception as e:
            print(f"Error al actualizar opacidad: {e}")

    def enable_all_tabs(self):
        """Habilita todas las pestañas después de activar la licencia"""
        # Evitar ejecuciones múltiples
        if hasattr(self, '_tabs_enabled') and self._tabs_enabled:
            return
        
        # Habilitando todas las pestañas (optimizado)
        self._tabs_enabled = True
        for i in range(self.tab_widget.count()):
            self.tab_widget.setTabEnabled(i, True)
            if i < len(self.tab_buttons):
                self.tab_buttons[i].setEnabled(True)
    
        # Actualizar la interfaz de la pestaña de licencia para mostrar la bienvenida
        try:
            # Buscar la pestaña de licencia
            license_tab_index = -1
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "LICENCIA":
                    license_tab_index = i
                    break
            
            if license_tab_index != -1:
                # Crear la pestaña de bienvenida premium
                old_widget = self.tab_widget.widget(license_tab_index)
                if old_widget:
                    old_widget.deleteLater()
                
                empty_tab = QWidget()
                empty_layout = QVBoxLayout(empty_tab)
                empty_layout.setContentsMargins(20, 20, 20, 20)
                
                content_widget = QWidget()
                content_widget.setObjectName("welcomeContainer")
                content_widget.setStyleSheet("""
                    QWidget#welcomeContainer {
                        background-color: rgba(0, 0, 0, 0.2);
                        border-radius: 15px;
                        border: 1px solid rgba(255, 255, 255, 0.1);
                    }
                """)
                content_layout = QVBoxLayout(content_widget)
                content_layout.setContentsMargins(30, 30, 30, 30)
                content_layout.setSpacing(20)

                # Contenedor para el ícono
                icon_container = QWidget()
                icon_container.setFixedSize(150, 150)
                container_layout = QVBoxLayout(icon_container)
                container_layout.setContentsMargins(0, 0, 0, 0)

                icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'ZETACOPY.ico')
                icon_pixmap = QPixmap(icon_path)
                icon_pixmap = icon_pixmap.scaled(128, 128, Qt.AspectRatioMode.KeepAspectRatio, 
                                            Qt.TransformationMode.SmoothTransformation)

                # Usar el efecto de sombra importado
                try:
                    from PRESENTACION import IconShadowEffect
                    shadow_widget = IconShadowEffect(icon_pixmap)
                    container_layout.addWidget(shadow_widget, alignment=Qt.AlignmentFlag.AlignCenter)
                except Exception as e:
                    print(f"Error al cargar efecto de sombra: {e}")
                    # Alternativa si no se puede cargar el efecto
                    icon_label = QLabel()
                    icon_label.setPixmap(icon_pixmap)
                    container_layout.addWidget(icon_label, alignment=Qt.AlignmentFlag.AlignCenter)
                
                content_layout.addWidget(icon_container, alignment=Qt.AlignmentFlag.AlignCenter)

                # Resto del contenido
                welcome_label = QLabel()
                welcome_label.setObjectName("welcomeLabel")
                welcome_label.setStyleSheet("""
                    QLabel#welcomeLabel {
                        background: transparent;
                    }
                """)
                
                try:
                    # Obtener la fecha del archivo de licencia o usar la almacenada en self.expiration_date
                    exp_date_str = "Fecha no disponible"
                    
                    if hasattr(self, 'expiration_date') and self.expiration_date:
                        exp_date_str = self.expiration_date.strftime("%d/%m/%Y")
                    else:
                        # Intentar leer del archivo de licencia
                        if getattr(sys, 'frozen', False):
                            base_path = os.path.dirname(sys.executable)
                        else:
                            base_path = os.path.dirname(os.path.abspath(__file__))
                        license_path = os.path.join(base_path, "license.dat")
                        
                        if os.path.exists(license_path):
                            with open(license_path, "rb") as license_file:
                                combined_data = license_file.read()
                                key, encrypted = combined_data.split(b'|', 1)
                                fernet = Fernet(key)
                                decrypted = fernet.decrypt(encrypted).decode()
                                date_str = decrypted.split('||')[1]
                                expiration_date = datetime.fromisoformat(date_str)
                                exp_date_str = expiration_date.strftime("%d/%m/%Y")
                except Exception as e:
                    print(f"Error al obtener fecha de licencia: {e}")
                    exp_date_str = "Error al obtener fecha"
                
                welcome_text = f"""
                    <div style='text-align: center;'>
                        <p style='font-size: 24px; font-weight: bold; color: #00a2ff; margin-bottom: 20px;'>
                            ¡ZETACOPY Premium!
                        </p>
                        <p style='font-size: 16px; color: white; margin: 15px 0;'>
                            Gracias por ser parte de la familia ZETACOPY.
                        </p>
                        <p style='font-size: 15px; color: #00a2ff; margin: 15px 0;'>
                            Has desbloqueado todas las características premium:
                        </p>
                        <p style='font-size: 14px; color: white; margin: 10px 0;'>
                            ✓ Transferencia de archivos ilimitada<br>
                            ✓ Monitoreo en tiempo real<br>
                            ✓ Gestión avanzada de colas<br>
                            ✓ Soporte para múltiples dispositivos<br>
                            ✓ Todas las funciones premium desbloqueadas
                        </p>
                        <p style='font-size: 14px; color: #00a2ff; margin-top: 20px;'>
                            ¡Disfruta de la experiencia completa de ZETACOPY!
                        </p>
                        <p style='font-size: 14px; color: #00ff00; margin-top: 15px;'>
                            Tu licencia es válida hasta: {exp_date_str}
                        </p>
                        <p style='font-weight: bold; color: #00a2ff; margin-top: 20px;'>
                            Creado por Jorge Yadiel Prado
                        </p>
                        <p style='color: white; margin-top: 5px;'>
                            Contacto: +58668183
                        </p>
                        <p style='font-size: 12px; color: white;'>
                            © 2024 Todos los derechos reservados
                        </p>
                    </div>
                """
                welcome_label.setText(welcome_text)
                welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                content_layout.addWidget(welcome_label)
                empty_layout.addWidget(content_widget)
                
                # Reemplazar la pestaña actual con la nueva
                self.tab_widget.removeTab(license_tab_index)
                self.tab_widget.insertTab(license_tab_index, empty_tab, "LICENCIA")
                
                # Bloquear los botones de licencia si existen
                if hasattr(self, 'lock_license_buttons'):
                    self.lock_license_buttons()
        except Exception as e:
            print(f"Error al actualizar pestaña de licencia: {e}")
        
        self.tab_widget.update()
        self.update()
        if self.main_window:
            self.main_window.update_license_status(True)
            self.main_window.enable_main_window()

    def on_hide_checkbox_changed(self, disk_info):
        """Manejador del cambio de estado del checkbox de ocultar"""
        try:
            config = load_config()
            discos_ocultos = config.get('discos_ocultos', [])
            checkbox = None
            for i in range(self.discos_exentos_list.count()):
                item = self.discos_exentos_list.item(i)
                widget = self.discos_exentos_list.itemWidget(item)
                for child in widget.children():
                    if isinstance(child, QCheckBox) and child.text() == "Ocultar":
                        if disk_info in widget.findChild(QLabel).text():
                            checkbox = child
                            break
            if checkbox and checkbox.isChecked():
                if disk_info not in discos_ocultos:
                    discos_ocultos.append(disk_info)
            else:
                if disk_info in discos_ocultos:
                    discos_ocultos.remove(disk_info)
            config['discos_ocultos'] = discos_ocultos
            save_config(config)
            
            # Notificar a la ventana principal para actualizar la vista
            if self.main_window:
                # Guardar información de discos que están copiando antes de actualizar
                discos_copiando = {}
                for i in range(self.main_window.list_widget.count()):
                    item = self.main_window.list_widget.item(i)
                    if not item or item.isHidden():
                        continue
                    widget = self.main_window.list_widget.itemWidget(item)
                    if not widget:
                        continue
                    
                    # Obtener etiquetas relevantes
                    labels = widget.findChildren(QLabel)
                    if len(labels) < 3:
                        continue
                    volume_label = labels[0]
                    speed_label = labels[2] if len(labels) > 2 else None
                    
                    # Extraer letra de unidad
                    text = volume_label.text()
                    if '(' in text and ')' in text:
                        drive_letter = text.split('(')[-1].strip(')')
                        
                        # Verificar si está copiando
                        is_copying = False
                        if hasattr(self.main_window, 'threads') and drive_letter in self.main_window.threads:
                            is_copying = self.main_window.threads[drive_letter].is_alive()
                        if not is_copying and hasattr(self.main_window, 'queues') and drive_letter in self.main_window.queues:
                            is_copying = not self.main_window.queues[drive_letter].empty()
                        
                        if is_copying:
                            # Guardar todos los datos relevantes
                            progress_bars = widget.findChildren(QProgressBar)
                            total_progress = None
                            file_progress = None
                            for bar in progress_bars:
                                if hasattr(bar, 'objectName'):
                                    if bar.objectName() == "total_progress":
                                        total_progress = bar
                                    elif bar.objectName() == "file_progress":
                                        file_progress = bar
                            discos_copiando[drive_letter] = {
                                'volume_text': volume_label.text(),
                                'speed_text': speed_label.text() if speed_label else "",
                                'total_progress': total_progress.value() if total_progress else 0,
                                'file_progress': file_progress.value() if file_progress else 0,
                                'total_visible': total_progress.isVisible() if total_progress else False,
                                'file_visible': file_progress.isVisible() if file_progress else False
                            }
                
                # Actualizar la vista de discos ocultos
                self.main_window.update_hidden_drives(discos_ocultos)
                
                # Restaurar información de discos que estaban copiando
                for i in range(self.main_window.list_widget.count()):
                    item = self.main_window.list_widget.item(i)
                    if not item or item.isHidden():
                        continue
                    widget = self.main_window.list_widget.itemWidget(item)
                    if not widget:
                        continue
                    
                    # Obtener etiquetas relevantes
                    labels = widget.findChildren(QLabel)
                    if len(labels) < 1:
                        continue
                    volume_label = labels[0]
                    speed_label = labels[2] if len(labels) > 2 else None
                    
                    # Extraer letra de unidad
                    text = volume_label.text()
                    if '(' in text and ')' in text:
                        drive_letter = text.split('(')[-1].strip(')')
                        
                        if drive_letter in discos_copiando:
                            # Restaurar datos de copia
                            data = discos_copiando[drive_letter]
                            
                            # Preservar alias si existe
                            if '|' in text and '|' in data['volume_text']:
                                alias = text.split('|')[0].strip()
                                resto = data['volume_text'].split('|', 1)[1]
                                volume_label.setText(f"{alias} | {resto}")
                            else:
                                volume_label.setText(data['volume_text'])
                            if speed_label:
                                speed_label.setText(data['speed_text'])
                            progress_bars = widget.findChildren(QProgressBar)
                            for bar in progress_bars:
                                if hasattr(bar, 'objectName'):
                                    if bar.objectName() == "total_progress":
                                        bar.setValue(data['total_progress'])
                                        bar.setVisible(data['total_visible'])
                                    elif bar.objectName() == "file_progress":
                                        bar.setValue(data['file_progress'])
                                        bar.setVisible(data['file_visible'])
            print(f"Estado de ocultación actualizado para {disk_info}")
            print(f"Discos ocultos actuales: {discos_ocultos}")
        except Exception as e:
            print(f"Error actualizando estado de ocultación: {e}")
            import traceback
            traceback.print_exc()

    def show_license_dialog(self):
        """Muestra el diálogo de licencia de manera segura"""
        try:
            dialog = QDialog(self)
            dialog.setWindowTitle("Licencia Requerida")
            dialog.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
            dialog.setFixedSize(400, 400)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: rgba(30, 30, 30, 0.95);
                    border: 1px solid rgba(100, 100, 100, 0.5);
                    border-radius: 15px;
                }
                QLabel {
                    color: white;
                }
                QPushButton {
                    background-color: #00a2ff;
                    color: white;
                    border-radius: 10px;
                    padding: 10px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0088d1;
                }
                QPushButton:pressed {
                    background-color: #006699;
                }
            """)
            main_layout = QVBoxLayout(dialog)
            main_layout.setContentsMargins(30, 30, 30, 30)
            main_layout.setSpacing(20)
            
            # Título
            title_label = QLabel("Se requiere licencia")
            title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #00a2ff;")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            main_layout.addWidget(title_label)
            
            # Icono
            icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'ZETACOPY.ico')
            icon_label = QLabel()
            icon_pixmap = QPixmap(icon_path)
            icon_pixmap = icon_pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, 
                                           Qt.TransformationMode.SmoothTransformation)
            icon_label.setPixmap(icon_pixmap)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            main_layout.addWidget(icon_label)
            message_label = QLabel(
                "Para acceder a esta función, necesita activar ZETACOPY Premium.\n\n"
                "La licencia no se encuentra o ha sido modificada.\n"
                "Por favor, active su licencia para continuar."
            )
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setWordWrap(True)
            main_layout.addWidget(message_label)
            buttons_layout = QHBoxLayout()
            activate_button = QPushButton("Activar Ahora")
            activate_button.setCursor(Qt.CursorShape.PointingHandCursor)
            def close_dialog():
                dialog.accept()
            activate_button.clicked.connect(close_dialog)
            buttons_layout.addWidget(activate_button)
            main_layout.addLayout(buttons_layout)
            center_pos = self.rect().center() - dialog.rect().center()
            dialog.move(self.mapToGlobal(center_pos))
            def on_dialog_finished():
                self.prevent_close = False
            dialog.finished.connect(on_dialog_finished)
            dialog.exec()
        except Exception as e:
            print(f"Error mostrando diálogo de licencia: {e}")

    def verify_hardware_match(self, stored_hw, current_hw):
        """Verifica si el hardware almacenado coincide con el actual"""
        try:
            # Extraer componentes
            stored_model, stored_disk = stored_hw
            current_model, current_disk = current_hw
            
            # Verificar coincidencia de modelo de placa base (obligatorio)
            if stored_model.strip() != current_model.strip():
                print(f"Modelo de placa base no coincide: {stored_model} vs {current_model}")
                return False
            
            # Verificar coincidencia de disco (opcional si es "To be filled by O.E.M.")
            if current_disk == "To be filled by O.E.M.":
                print("Disco actual no tiene número de serie, pero se permite la licencia")
                return True
            
            # Si el disco tiene un número de serie real, debe coincidir
            if stored_disk.strip() != current_disk.strip():
                print(f"Número de serie de disco no coincide: {stored_disk} vs {current_disk}")
                # Podemos ser más estrictos aquí y devolver False si queremos
                # Por ahora, permitimos la licencia si al menos la placa base coincide
                return True
            
            # Todo coincide
            return True
        except Exception as e:
            print(f"Error verificando coincidencia de hardware: {e}")
            # En caso de error, permitimos la licencia si al menos la placa base coincide
            return True

    def get_current_hardware(self):
        """Obtiene la información del hardware actual del sistema"""
        try:
            # Obtener información de la placa base
            motherboard_info = get_motherboard_info()
            model = motherboard_info[1]  # Modelo
            
            # Obtener número de serie del disco principal
            disk_serial = get_disk_serial_number("C:")
            
            return [model, disk_serial]
        except Exception as e:
            print(f"Error obteniendo información de hardware actual: {e}")
            return ["Unknown", "Unknown"]

    def ensure_about_tab_enabled(self):
        """Asegura que la pestaña Acerca de esté siempre habilitada"""
        try:
            tab_names = ["LICENCIA", "INDEXAR", "PAGOS", "Configuración", "Mapeo de Puertos", "Acerca de"]
            about_index = tab_names.index("Acerca de")
            
            # Verificar si la pestaña está deshabilitada
            if about_index < len(self.tab_buttons) and not self.tab_buttons[about_index].isEnabled():
                # Habilitar la pestaña sin imprimir mensaje
                self.tab_buttons[about_index].setEnabled(True)
                self.tab_widget.setTabEnabled(about_index, True)
                
                # Desactivar el temporizador después de habilitar la pestaña
                if hasattr(self, 'about_tab_timer') and self.about_tab_timer.isActive():
                    # Solo imprimir una vez que se ha habilitado
                    print("Pestaña Acerca de habilitada correctamente")
                    
                    # Reducir la frecuencia del temporizador a una vez cada 10 segundos
                    self.about_tab_timer.stop()
                    self.about_tab_timer.setInterval(10000)  # 10 segundos en lugar de 1
                    self.about_tab_timer.start()
        except Exception as e:
            # Solo registrar errores reales, no el funcionamiento normal
            print(f"Error al asegurar que la pestaña Acerca de esté habilitada: {e}")

    def showMinimized(self):
        """Sobrescribe el método showMinimized para manejar la minimización correctamente"""
        try:
            # Obtener el handle de la ventana
            hwnd = int(self.winId())
            
            # Importar las funciones necesarias de Windows
            from ctypes import windll
            from ctypes.wintypes import HWND
            
            # Definir las constantes de Windows
            SW_MINIMIZE = 6
            
            # Usar la API de Windows directamente para minimizar
            windll.user32.ShowWindow(HWND(hwnd), SW_MINIMIZE)
            
            # Deshabilitar temporalmente las actualizaciones de la ventana
            self.setUpdatesEnabled(False)
            
            # Restaurar las actualizaciones después de un breve retraso
            def restore_updates():
                if self.windowState() == Qt.WindowState.WindowMinimized:
                    self.setUpdatesEnabled(True)
            
            QTimer.singleShot(500, restore_updates)
            
        except Exception as e:
            print(f"Error en minimización: {str(e)}")
            # Fallback al método estándar si hay error
            super().showMinimized()

    def changeEvent(self, event):
        if event.type() == QEvent.Type.WindowStateChange:
            if self.windowState() == Qt.WindowState.WindowMinimized:
                event.accept()
            else:
                super().changeEvent(event)
        else:
            super().changeEvent(event)

    # La función cambiar_metodo_pago ha sido eliminada ya que solo se utiliza el método avanzado

    def actualizar_discos_disponibles(self):
        """Actualiza la lista de discos disponibles en el combo box"""
        if not hasattr(self, 'disco_combo'):
            return
            
        self.disco_combo.clear()
        
        # Obtener todas las letras de unidad disponibles
        bitmask = windll.kernel32.GetLogicalDrives()
        for letter in range(ord('A'), ord('Z')+1):
            if bitmask & (1 << (letter - ord('A'))):
                drive_letter = f"{chr(letter)}:"
                try:
                    if os.path.exists(f"{drive_letter}\\"):
                        drive_type = win32file.GetDriveType(f"{drive_letter}\\")
                        # Solo mostrar unidades locales y extraíbles
                        if drive_type in [win32file.DRIVE_FIXED, win32file.DRIVE_REMOVABLE]:
                            try:
                                volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0] or "Sin nombre"
                                self.disco_combo.addItem(f"{drive_letter} ({volume_name})", drive_letter)
                            except:
                                self.disco_combo.addItem(drive_letter, drive_letter)
                except:
                    pass

    def seleccionar_directorio(self):
        """Abre un diálogo para seleccionar un directorio en el disco seleccionado"""
        if self.disco_combo.currentData():
            drive_letter = self.disco_combo.currentData()
            inicio = f"{drive_letter}\\"
            
            directorio = QFileDialog.getExistingDirectory(
                self,
                "Seleccionar Directorio",
                inicio,
                QFileDialog.Option.ShowDirsOnly | QFileDialog.Option.DontResolveSymlinks
            )
            
            if directorio:
                # Obtener la ruta relativa al disco seleccionado
                if directorio.startswith(drive_letter):
                    ruta_relativa = directorio[len(drive_letter)+1:]  # +1 para quitar la barra
                    self.directorio_input.setText(ruta_relativa)
                else:
                    self.directorio_input.setText(directorio)

    def on_no_expulsar_checkbox_changed(self, disk_info):
        """Manejador del cambio de estado del checkbox de no expulsar"""
        try:
            config = load_config()
            discos_no_expulsar = config.get('discos_no_expulsar', [])
            checkbox = None
            for i in range(self.discos_exentos_list.count()):
                item = self.discos_exentos_list.item(i)
                widget = self.discos_exentos_list.itemWidget(item)
                for child in widget.children():
                    if isinstance(child, QCheckBox) and child.text() == "No expulsar":
                        if disk_info in widget.findChild(QLabel).text():
                            checkbox = child
                            break
            if checkbox and checkbox.isChecked():
                if disk_info not in discos_no_expulsar:
                    discos_no_expulsar.append(disk_info)
            else:
                if disk_info in discos_no_expulsar:
                    discos_no_expulsar.remove(disk_info)
            config['discos_no_expulsar'] = discos_no_expulsar
            save_config(config)
        except Exception as e:
            print(f"Error al actualizar estado de no expulsar: {e}")

    # Función load_ports_from_puertos_usb eliminada - ya no se usa

    def save_port_name_to_json(self, port_id, new_name):
        """Guarda el nombre personalizado del puerto en usb_port_names.json"""
        try:
            # Leer nombres existentes
            custom_names = {}
            if os.path.exists('usb_port_names.json'):
                with open('usb_port_names.json', 'r', encoding='utf-8') as f:
                    custom_names = json.load(f)
            
            # Actualizar o eliminar el nombre
            if new_name.strip():
                custom_names[port_id] = new_name.strip()
            else:
                custom_names.pop(port_id, None)
            
            # Guardar de vuelta al archivo
            with open('usb_port_names.json', 'w', encoding='utf-8') as f:
                json.dump(custom_names, f, indent=2, ensure_ascii=False)
            
            print(f"Nombre del puerto {port_id} actualizado: '{new_name}'")
            
        except Exception as e:
            print(f"Error guardando nombre del puerto: {e}")

    def delete_port_from_files(self, port_id, drive_letter):
        """Elimina un puerto de ambos archivos: PUERTOS_USB.txt y usb_port_names.json"""
        try:
            # Mostrar diálogo de confirmación
            confirm = QMessageBox()
            confirm.setWindowTitle("Confirmar eliminación")
            confirm.setText(f"¿Estás seguro de que deseas eliminar el mapeo del puerto {port_id}?\n\nEsto eliminará:\n- El mapeo del disco {drive_letter}: en PUERTOS_USB.txt\n- El nombre personalizado en usb_port_names.json")
            confirm.setIcon(QMessageBox.Icon.Question)
            confirm.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            confirm.setDefaultButton(QMessageBox.StandardButton.No)
            
            # Aplicar estilo moderno al diálogo
            confirm.setStyleSheet("""
                QMessageBox {
                    background-color: #2d2d2d;
                    color: white;
                }
                QLabel {
                    color: white;
                }
                QPushButton {
                    background-color: #3a3a3a;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #454545;
                }
                QPushButton:pressed {
                    background-color: #555555;
                }
            """)
            
            # Si el usuario confirma, eliminar el puerto
            if confirm.exec() == QMessageBox.StandardButton.Yes:
                # 1. Eliminar de PUERTOS_USB.txt
                if os.path.exists('PUERTOS_USB.txt'):
                    lines = []
                    with open('PUERTOS_USB.txt', 'r', encoding='utf-8') as f:
                        for line in f:
                            if not (line.strip().startswith(f"{drive_letter}::=") and port_id in line):
                                lines.append(line)
                    
                    with open('PUERTOS_USB.txt', 'w', encoding='utf-8') as f:
                        f.writelines(lines)
                
                # 2. Eliminar de usb_port_names.json
                if os.path.exists('usb_port_names.json'):
                    with open('usb_port_names.json', 'r', encoding='utf-8') as f:
                        custom_names = json.load(f)
                    
                    custom_names.pop(port_id, None)
                    
                    with open('usb_port_names.json', 'w', encoding='utf-8') as f:
                        json.dump(custom_names, f, indent=2, ensure_ascii=False)
                
                # Mostrar notificación de éxito
                pos = QCursor.pos()
                QToolTip.showText(
                    pos,
                    f"Puerto {port_id} eliminado correctamente",
                    self,
                    QRect(),
                    3000
                )
                
                # Actualizar la vista
                self.load_ports_from_puertos_usb()
                
                # Limpiar caché si es posible
                try:
                    from MAPEO_PUERTO import clear_drive_from_cache
                    clear_drive_from_cache(f"{drive_letter}:")
                except Exception as e:
                    print(f"Error limpiando caché: {e}")
                
                # Actualizar vista principal si existe
                if self.main_window:
                    try:
                        if hasattr(self.main_window, 'refresh_ports'):
                            self.main_window.refresh_ports()
                        if hasattr(self.main_window, 'actualizar_vista_discos'):
                            self.main_window.actualizar_vista_discos()
                    except Exception as e:
                        print(f"Error actualizando vista principal: {e}")
        
        except Exception as e:
            print(f"Error eliminando puerto: {e}")
            import traceback
            traceback.print_exc()

    def load_ports_from_puertos_usb(self):
        """Carga TODOS los puertos desde usb_port_names.json para gestión"""
        # Evitar ejecuciones múltiples
        if hasattr(self, '_ports_loaded') and self._ports_loaded:
            return
        self._ports_loaded = True
        try:
            if self.ports_list_widget.layout():
                while self.ports_list_widget.layout().count():
                    child = self.ports_list_widget.layout().takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

            puerto_icon = boton_mapear_puertos(size=20).pixmap(20, 20)

            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setSpacing(3)
            scroll_layout.setContentsMargins(5, 5, 5, 5)

            # Leer TODOS los nombres personalizados desde usb_port_names.json
            custom_names = {}
            if os.path.exists('usb_port_names.json'):
                try:
                    with open('usb_port_names.json', 'r', encoding='utf-8') as f:
                        custom_names = json.load(f)
                except Exception as e:
                    print(f"Error leyendo usb_port_names.json: {e}")

            # Leer información de conexión actual desde PUERTOS_USB.txt (opcional)
            connected_drives = {}
            if os.path.exists('PUERTOS_USB.txt'):
                try:
                    with open('PUERTOS_USB.txt', 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if '::=' in line:
                                # Formato: "O::=Port_#0021.Hub_#0003|Hub 0003"
                                drive_part, port_info = line.split('::=', 1)
                                if '|' in port_info:
                                    port_id, hub_info = port_info.split('|', 1)
                                    connected_drives[port_id] = {
                                        'drive': drive_part,
                                        'hub_info': hub_info
                                    }
                except Exception as e:
                    print(f"Error leyendo PUERTOS_USB.txt: {e}")
            
            # Si no hay puertos en el JSON, mostrar mensaje
            if not custom_names:
                no_ports_label = QLabel("No hay puertos USB con nombres personalizados")
                no_ports_label.setStyleSheet("""
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 14px;
                    padding: 20px;
                """)
                no_ports_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                scroll_layout.addWidget(no_ports_label)
            else:
                # Crear widgets para TODOS los puertos en usb_port_names.json
                for port_id, custom_name in custom_names.items():
                    container = QWidget()
                    container_layout = QHBoxLayout(container)
                    container_layout.setContentsMargins(5, 2, 5, 2)
                    container_layout.setSpacing(5)
                    
                    # Color de fondo neutro (sin resaltar en verde si está conectado)
                    is_connected = port_id in connected_drives
                    bg_color = "rgba(50, 50, 50, 0.5)"
                    container.setStyleSheet(f"""
                        QWidget {{
                            background-color: {bg_color};
                            border-radius: 5px;
                        }}
                    """)
                    
                    # Icono del puerto
                    puerto_label = QLabel()
                    puerto_label.setPixmap(puerto_icon.scaled(20, 20))
                    puerto_label.setFixedSize(20, 20)
                    container_layout.addWidget(puerto_label)
                    
                    # Etiqueta del puerto (ID técnico)
                    port_label = QLabel(f"{port_id}")
                    port_label.setStyleSheet("""
                        color: white;
                        font-family: Consolas, monospace;
                        font-size: 11px;
                        min-width: 140px;
                        max-width: 140px;
                    """)
                    port_label.setWordWrap(True)
                    container_layout.addWidget(port_label)
                    
                    # Campo de entrada para nombre personalizado - MOSTRAR EL NOMBRE REAL
                    name_edit = QLineEdit(custom_name)  # Usar directamente el nombre del JSON
                    name_edit.setStyleSheet("""
                        QLineEdit {
                            background-color: rgba(255, 255, 255, 0.1);
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 4px 8px;
                            font-family: Segoe UI;
                            font-size: 12px;
                        }
                        QLineEdit:focus {
                            background-color: rgba(255, 255, 255, 0.15);
                            border: 1px solid rgba(0, 120, 215, 0.8);
                        }
                    """)
                    name_edit.setFixedWidth(120)
                    name_edit.setPlaceholderText("Nombre personalizado...")
                    name_edit.textChanged.connect(lambda text, pid=port_id: self.save_port_name_to_json(pid, text))
                    container_layout.addWidget(name_edit)
                    
                    # Si está conectado, no mostrar hub_info para evitar "Hub 003"
                    if is_connected:
                        pass
                    
                    # Espacio flexible
                    container_layout.addStretch(1)
                    
                    # Botón eliminar
                    delete_button = QPushButton("×")
                    delete_button.setStyleSheet("""
                        QPushButton {
                            color: #ff5555;
                            background-color: transparent;
                            border: none;
                            font-weight: bold;
                            font-size: 18px;
                            padding: 0px;
                            margin: 0px;
                            min-width: 20px;
                            max-width: 20px;
                            min-height: 20px;
                            max-height: 20px;
                        }
                        QPushButton:hover {
                            color: #ff0000;
                            background-color: rgba(255, 0, 0, 0.1);
                            border-radius: 10px;
                        }
                        QPushButton:pressed {
                            color: white;
                            background-color: #ff0000;
                        }
                    """)
                    delete_button.setCursor(Qt.CursorShape.PointingHandCursor)
                    delete_button.setToolTip(f"Eliminar puerto {port_id}")
                    delete_button.clicked.connect(lambda checked=False, pid=port_id: self.delete_port_from_json(pid))
                    container_layout.addWidget(delete_button)
                    
                    container.setFixedHeight(35)
                    scroll_layout.addWidget(container)
            
            scroll_layout.addStretch()
            
            scroll_area = QScrollArea()
            scroll_area.setWidget(scroll_widget)
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: none;
                    background: transparent;
                }
                QScrollBar:vertical {
                    width: 10px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 5px;
                }
                QScrollBar::handle:vertical {
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 4px;
                    min-height: 20px;
                }
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
            """)
            
            self.ports_list_widget.layout().addWidget(scroll_area)
            # Pestaña de mapeo de puertos cargada (optimizado)
            
        except Exception as e:
            print(f"Error cargando puertos desde usb_port_names.json: {e}")
            import traceback
            traceback.print_exc()

    def save_port_name_to_json(self, port_id, new_name):
        """Guarda el nombre personalizado del puerto en un archivo JSON"""
        try:
            json_file = "usb_port_names.json"
            port_names = {}
            
            # Cargar nombres existentes
            if os.path.exists(json_file):
                with open(json_file, 'r', encoding='utf-8') as f:
                    port_names = json.load(f)
            
            # Actualizar o eliminar el nombre
            if new_name.strip():
                port_names[port_id] = new_name.strip()
            else:
                port_names.pop(port_id, None)
            
            # Guardar de vuelta
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(port_names, f, indent=2, ensure_ascii=False)
            
            print(f"Nombre del puerto {port_id} actualizado: '{new_name}'")
            
        except Exception as e:
            print(f"Error guardando nombre del puerto: {e}")

    def delete_port_from_puertos_usb(self, port_id):
        """Elimina un puerto del archivo PUERTOS_USB.txt"""
        try:
            # Mostrar diálogo de confirmación
            confirm = QMessageBox()
            confirm.setWindowTitle("Confirmar eliminación")
            confirm.setText(f"¿Estás seguro de que deseas eliminar el mapeo del puerto {port_id}?")
            confirm.setIcon(QMessageBox.Icon.Question)
            confirm.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            confirm.setDefaultButton(QMessageBox.StandardButton.No)
            
            # Aplicar estilo moderno al diálogo
            confirm.setStyleSheet("""
                QMessageBox {
                    background-color: #2d2d2d;
                    color: white;
                }
                QLabel {
                    color: white;
                }
                QPushButton {
                    background-color: #3a3a3a;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #454545;
                }
                QPushButton:pressed {
                    background-color: #555555;
                }
            """)
            
            # Si el usuario confirma, eliminar el puerto
            if confirm.exec() == QMessageBox.StandardButton.Yes:
                # Leer todas las líneas del archivo
                lines = []
                if os.path.exists('PUERTOS_USB.txt'):
                    with open('PUERTOS_USB.txt', 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                
                # Filtrar las líneas que no contengan el port_id a eliminar
                filtered_lines = []
                for line in lines:
                    if '::=' in line:
                        _, port_info = line.split('::=', 1)
                        if '|' in port_info:
                            current_port_id, _ = port_info.split('|', 1)
                            if current_port_id.strip() != port_id:
                                filtered_lines.append(line)
                    else:
                        filtered_lines.append(line)
                
                # Escribir de vuelta al archivo
                with open('PUERTOS_USB.txt', 'w', encoding='utf-8') as f:
                    f.writelines(filtered_lines)
                
                # También eliminar del archivo JSON de nombres personalizados
                try:
                    json_file = "usb_port_names.json"
                    if os.path.exists(json_file):
                        with open(json_file, 'r', encoding='utf-8') as f:
                            port_names = json.load(f)
                        
                        if port_id in port_names:
                            del port_names[port_id]
                            with open(json_file, 'w', encoding='utf-8') as f:
                                json.dump(port_names, f, indent=2, ensure_ascii=False)
                except Exception as e:
                    print(f"Error eliminando nombre personalizado: {e}")
                
                # Mostrar notificación de éxito
                pos = QCursor.pos()
                QToolTip.showText(
                    pos,
                    f"Mapeo del puerto {port_id} eliminado correctamente",
                    self,
                    QRect(),
                    3000
                )
                
                # Actualizar la vista
                self.load_ports_from_puertos_usb()
                
                print(f"Puerto {port_id} eliminado de PUERTOS_USB.txt")
        
        except Exception as e:
            print(f"Error eliminando puerto: {e}")
            import traceback
            traceback.print_exc()

    def delete_port_from_json(self, port_id):
        """Elimina un puerto solo del archivo usb_port_names.json"""
        try:
            # Mostrar diálogo de confirmación
            confirm = QMessageBox()
            confirm.setWindowTitle("Confirmar eliminación")
            confirm.setText(f"¿Estás seguro de que deseas eliminar el nombre personalizado del puerto {port_id}?\n\nEsto solo eliminará el nombre personalizado, no el mapeo técnico.")
            confirm.setIcon(QMessageBox.Icon.Question)
            confirm.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            confirm.setDefaultButton(QMessageBox.StandardButton.No)
            
            # Aplicar estilo moderno al diálogo
            confirm.setStyleSheet("""
                QMessageBox {
                    background-color: #2d2d2d;
                    color: white;
                }
                QLabel {
                    color: white;
                }
                QPushButton {
                    background-color: #3a3a3a;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #454545;
                }
                QPushButton:pressed {
                    background-color: #555555;
                }
            """)
            
            # Si el usuario confirma, eliminar el puerto
            if confirm.exec() == QMessageBox.StandardButton.Yes:
                # Eliminar solo del archivo JSON
                json_file = "usb_port_names.json"
                if os.path.exists(json_file):
                    with open(json_file, 'r', encoding='utf-8') as f:
                        port_names = json.load(f)
                    
                    if port_id in port_names:
                        del port_names[port_id]
                        with open(json_file, 'w', encoding='utf-8') as f:
                            json.dump(port_names, f, indent=2, ensure_ascii=False)
                
                # Mostrar notificación de éxito
                pos = QCursor.pos()
                QToolTip.showText(
                    pos,
                    f"Nombre personalizado del puerto {port_id} eliminado",
                    self,
                    QRect(),
                    3000
                )
                
                # Actualizar la vista
                self.load_ports_from_puertos_usb()
                
                print(f"Nombre personalizado del puerto {port_id} eliminado de usb_port_names.json")
        
        except Exception as e:
            print(f"Error eliminando nombre personalizado: {e}")
            import traceback
            traceback.print_exc()