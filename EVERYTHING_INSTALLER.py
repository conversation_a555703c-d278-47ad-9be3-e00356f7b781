#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Instalador unificado de Everything usando el archivo MSI oficial
"""

import os
import sys
import subprocess
import tempfile
import time
import ctypes
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QProgressBar, QTextEdit, QFrame, QApplication, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont
from APARIENCIA import apply_acrylic_and_rounded
from CREAR import CustomCloseButton, Icono_BUSCADOR
from BARRA_TOTAL import BarraTotal

def get_everything_msi_path():
    """Obtiene la ruta del instalador MSI de Everything incluido"""
    def search_msi_in_directory(directory):
        """Busca el instalador MSI en un directorio específico"""
        if not os.path.exists(directory):
            return None
        
        # Buscar nombres específicos conocidos
        possible_names = [
            "Everything-1.4.1.1028.x64.msi",
            "Everything-1.4.1.1024.x64.msi",
            "Everything.msi"
        ]
        
        for name in possible_names:
            msi_path = os.path.join(directory, name)
            if os.path.exists(msi_path):
                return msi_path
        
        # Si no encuentra nombres específicos, buscar cualquier archivo MSI de Everything
        try:
            for filename in os.listdir(directory):
                if (filename.startswith("Everything") and 
                    filename.endswith(".msi") and 
                    os.path.isfile(os.path.join(directory, filename))):
                    return os.path.join(directory, filename)
        except:
            pass
        
        return None
    
    if getattr(sys, 'frozen', False):
        # Si está compilado (exe) - buscar en el directorio temporal de PyInstaller
        everything_dir = os.path.join(sys._MEIPASS, "Everything")
        return search_msi_in_directory(everything_dir)
    else:
        # En desarrollo - buscar en la carpeta del proyecto
        project_dir = os.path.dirname(os.path.abspath(__file__))
        everything_dir = os.path.join(project_dir, "Everything")
        return search_msi_in_directory(everything_dir)

class EverythingInstaller(QThread):
    """Thread para instalar Everything usando el MSI oficial"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    installation_finished = pyqtSignal(bool, str)
    
    def __init__(self, msi_path, install_mode="system"):
        super().__init__()
        self.msi_path = msi_path
        self.install_mode = install_mode  # "system" o "portable"
        
    def run(self):
        """Ejecuta la instalación de Everything"""
        try:
            self.status_updated.emit("Iniciando instalación de Everything...")
            self.progress_updated.emit(10)
            
            if self.install_mode == "system":
                self._install_system()
            else:
                self._install_portable()
                
        except Exception as e:
            self.installation_finished.emit(False, f"Error durante la instalación: {str(e)}")
    
    def _install_system(self):
        """Instalación del sistema usando MSI"""
        self.status_updated.emit("Preparando instalación del sistema...")
        self.progress_updated.emit(20)
        
        # Parámetros para instalación silenciosa del sistema
        msi_args = [
            "msiexec",
            "/i", self.msi_path,
            "/quiet",  # Instalación silenciosa
            "/norestart"  # No reiniciar automáticamente
        ]
        
        self.status_updated.emit("Ejecutando instalador MSI...")
        self.progress_updated.emit(30)
        
        try:
            # Ejecutar MSI con permisos de administrador usando ShellExecute
            import ctypes
            
            # Preparar parámetros como string
            params = f'/i "{self.msi_path}" /quiet /norestart'
            
            self.status_updated.emit("Solicitando permisos de administrador...")
            
            # Ejecutar con permisos de administrador
            result = ctypes.windll.shell32.ShellExecuteW(
                None,
                "runas",  # Ejecutar como administrador
                "msiexec",
                params,
                None,
                0  # SW_HIDE
            )
            
            if result <= 32:
                self.installation_finished.emit(False, "El usuario canceló la solicitud de permisos de administrador")
                return
            
            self.status_updated.emit("Instalando Everything (esto puede tomar unos segundos)...")
            
            # Esperar a que termine la instalación
            max_wait = 60  # Esperar máximo 60 segundos
            for i in range(max_wait):
                time.sleep(1)
                progress = 50 + (i * 40 // max_wait)
                self.progress_updated.emit(progress)
                
                # Verificar si Everything ya se instaló
                if os.path.exists(r"C:\Program Files\Everything\Everything.exe"):
                    break
                
                if i % 10 == 0 and i > 0:
                    self.status_updated.emit(f"Instalando... ({i}/{max_wait}s)")
            
            # Verificar si la instalación fue exitosa
            if os.path.exists(r"C:\Program Files\Everything\Everything.exe"):
                self._verify_and_start_installation()
            else:
                self.installation_finished.emit(False, "Everything no se instaló correctamente")
                
        except Exception as e:
            self.installation_finished.emit(False, f"Error ejecutando MSI: {str(e)}")
    
    def _install_portable(self):
        """Instalación portátil extrayendo del MSI"""
        self.status_updated.emit("Preparando instalación portátil...")
        self.progress_updated.emit(20)
        
        # Determinar directorio portátil
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
            portable_dir = os.path.join(exe_dir, "Everything_Portable")
        else:
            project_dir = os.path.dirname(os.path.abspath(__file__))
            portable_dir = os.path.join(project_dir, "Everything_Portable")
        
        self.status_updated.emit(f"Instalando en: {portable_dir}")
        
        # Crear directorio temporal para extraer MSI
        with tempfile.TemporaryDirectory() as temp_dir:
            self.status_updated.emit("Extrayendo archivos del MSI...")
            self.progress_updated.emit(40)
            
            # Extraer MSI a directorio temporal
            extract_args = [
                "msiexec",
                "/a", self.msi_path,
                f'TARGETDIR="{temp_dir}"',
                "/quiet"
            ]
            
            process = subprocess.Popen(
                extract_args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            return_code = process.wait()
            
            if return_code != 0:
                self.installation_finished.emit(False, f"Error extrayendo MSI: código {return_code}")
                return
            
            self.status_updated.emit("Copiando archivos a instalación portátil...")
            self.progress_updated.emit(60)
            
            # Buscar archivos de Everything en el directorio extraído
            everything_files = []
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    if file.lower().startswith("everything") and file.lower().endswith((".exe", ".ini", ".lng")):
                        everything_files.append(os.path.join(root, file))
            
            if not everything_files:
                self.installation_finished.emit(False, "No se encontraron archivos de Everything en el MSI")
                return
            
            # Crear directorio portátil
            if not os.path.exists(portable_dir):
                os.makedirs(portable_dir)
            
            # Copiar archivos
            import shutil
            for file_path in everything_files:
                filename = os.path.basename(file_path)
                target_path = os.path.join(portable_dir, filename)
                shutil.copy2(file_path, target_path)
                self.status_updated.emit(f"Copiado: {filename}")
            
            self.progress_updated.emit(80)
            
            # Configurar para modo portátil
            self._configure_portable_mode(portable_dir)
            
            # Verificar e iniciar
            everything_exe = os.path.join(portable_dir, "Everything.exe")
            if os.path.exists(everything_exe):
                self._start_everything(everything_exe)
                self.progress_updated.emit(100)
                self.status_updated.emit("¡Instalación portátil completada!")
                self.installation_finished.emit(True, "Everything se instaló correctamente en modo portátil")
            else:
                self.installation_finished.emit(False, "No se encontró Everything.exe después de la instalación portátil")
    
    def _configure_portable_mode(self, portable_dir):
        """Configura Everything.ini para modo portátil"""
        ini_path = os.path.join(portable_dir, "Everything.ini")
        
        config_lines = [
            "startup=0",
            "run_in_background=1",
            "minimize_to_tray=1",
            "show_tray_icon=1",
            "auto_include_fixed_volumes=1",
            "auto_include_removable_volumes=1",
            "monitor_changes=1",
            "index_attributes=1",
            "index_file_size=1",
            "index_date_created=1",
            "index_date_modified=1",
            "index_date_accessed=0",
            "match_case=0",
            "match_whole_word=0",
            "match_path=0",
            "regex=0",
            "sort=1",
            "http_server_enabled=0",
            "etp_server_enabled=0",
            "allow_multiple_instances=0",
            "db_location=Everything.db",
            "log_file=Everything.log",
        ]
        
        try:
            with open(ini_path, 'w', encoding='utf-8') as f:
                f.write("# Everything Configuration - Modo Portátil ZETACOPY\n")
                f.write("# Configuración optimizada para instalación portátil\n\n")
                for config_line in config_lines:
                    f.write(f"{config_line}\n")
        except Exception as e:
            print(f"Error configurando Everything.ini: {e}")
    
    def _verify_and_start_installation(self):
        """Verifica la instalación del sistema e inicia Everything"""
        self.status_updated.emit("Verificando instalación...")
        self.progress_updated.emit(90)
        
        # Buscar Everything en ubicaciones estándar
        possible_paths = [
            r"C:\Program Files\Everything\Everything.exe",
            r"C:\Program Files (x86)\Everything\Everything.exe"
        ]
        
        everything_exe = None
        for path in possible_paths:
            if os.path.exists(path):
                everything_exe = path
                break
        
        if everything_exe:
            self._start_everything(everything_exe)
            self.progress_updated.emit(100)
            self.status_updated.emit("¡Instalación completada!")
            self.installation_finished.emit(True, "Everything se instaló correctamente")
        else:
            self.installation_finished.emit(False, "Everything no se encontró después de la instalación")
    
    def _start_everything(self, everything_exe):
        """Inicia Everything"""
        try:
            self.status_updated.emit("Iniciando Everything...")
            subprocess.Popen(
                [everything_exe, "-startup"],
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        except Exception as e:
            print(f"Error iniciando Everything: {e}")

class EverythingInstallDialog(QDialog):
    """Diálogo para instalación de Everything"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.installer_thread = None
        self.setup_ui()
        self.setup_window()
        
    def setup_window(self):
        """Configura la ventana del diálogo"""
        self.setWindowTitle("Instalador de Everything")
        self.setFixedSize(400, 200)  # Más pequeño y compacto
        self.setWindowFlags(
            Qt.WindowType.Dialog | 
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)
        
        # Centrar en pantalla
        if self.parent():
            parent_geo = self.parent().geometry()
            x = parent_geo.x() + (parent_geo.width() - self.width()) // 2
            y = parent_geo.y() + (parent_geo.height() - self.height()) // 2
            self.move(x, y)
    
    def showEvent(self, event):
        """Aplicar efecto acrílico cuando se muestra"""
        super().showEvent(event)
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd, mode='dark', is_tooltip=False)
    
    def setup_ui(self):
        """Configura la interfaz del diálogo simple y compacta"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Header compacto con icono y título
        header_layout = QHBoxLayout()
        
        # Icono de Everything más pequeño
        icon_label = QLabel()
        icon_pixmap = Icono_BUSCADOR(32).pixmap(32, 32)
        icon_label.setPixmap(icon_pixmap)
        icon_label.setFixedSize(32, 32)
        header_layout.addWidget(icon_label)
        
        # Título compacto
        title_label = QLabel("Instalar Everything")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Botón de cerrar
        self.close_button = CustomCloseButton(self)
        self.close_button.clicked.connect(self.reject)
        header_layout.addWidget(self.close_button)
        
        layout.addLayout(header_layout)
        
        # Estado de instalación (inicialmente oculto)
        self.status_label = QLabel("Preparando instalación...")
        self.status_label.setStyleSheet("color: #CCCCCC; font-size: 11px;")
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)
        
        # Barra de progreso personalizada (inicialmente oculta)
        self.progress_bar = BarraTotal()
        self.progress_bar.setFixedHeight(20)
        self.progress_bar.setBaseColor("#4CAF50")  # Verde
        self.progress_bar.setShowPercentage(True)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Botones de instalación compactos
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # Botón instalación del sistema
        self.system_install_button = QPushButton("🛡️ Sistema")
        self.system_install_button.setStyleSheet("""
            QPushButton {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                border: none;
                border-radius: 6px;
                color: white;
                font-size: 11px;
                font-weight: bold;
                padding: 10px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5CBF60, stop:1 #4CAF50);
            }
            QPushButton:pressed {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }
            QPushButton:disabled {
                background-color: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.5);
            }
        """)
        self.system_install_button.clicked.connect(lambda: self.start_installation("system"))
        buttons_layout.addWidget(self.system_install_button)
        
        # Botón instalación portátil
        self.portable_install_button = QPushButton("📁 Portátil")
        self.portable_install_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                color: white;
                font-size: 11px;
                padding: 10px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.05);
            }
            QPushButton:disabled {
                background-color: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
            }
        """)
        self.portable_install_button.clicked.connect(lambda: self.start_installation("portable"))
        buttons_layout.addWidget(self.portable_install_button)
        
        # Botón cancelar
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                color: white;
                font-size: 11px;
                padding: 10px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.05);
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def start_installation(self, install_mode="system"):
        """Inicia la instalación de Everything"""
        try:
            # Buscar el instalador MSI
            msi_path = get_everything_msi_path()
            
            if not msi_path or not os.path.exists(msi_path):
                self.show_error("No se encontró el instalador MSI de Everything")
                return
            
            self.update_status(f"Usando instalador: {os.path.basename(msi_path)}")
            
            # Mostrar elementos de progreso
            self.progress_bar.setVisible(True)
            self.status_label.setVisible(True)
            
            # Deshabilitar botones de instalación
            self.system_install_button.setEnabled(False)
            self.portable_install_button.setEnabled(False)
            self.cancel_button.setEnabled(False)
            
            # Crear e iniciar thread de instalación
            self.installer_thread = EverythingInstaller(msi_path, install_mode)
            self.installer_thread.progress_updated.connect(self.progress_bar.setValue)
            self.installer_thread.status_updated.connect(self.update_status)
            self.installer_thread.installation_finished.connect(self.installation_finished)
            self.installer_thread.start()
            
        except Exception as e:
            self.show_error(f"Error iniciando instalación: {str(e)}")
    
    def update_status(self, message):
        """Actualiza el estado de la instalación"""
        self.status_label.setText(message)
    
    def installation_finished(self, success, message):
        """Maneja el final de la instalación"""
        if success:
            self.update_status("✓ " + message)
            self.progress_bar.setValue(100)
            QTimer.singleShot(2000, self.accept)
        else:
            self.show_error(message)
            self.system_install_button.setEnabled(True)
            self.portable_install_button.setEnabled(True)
            self.cancel_button.setEnabled(True)
    
    def show_error(self, message):
        """Muestra un mensaje de error"""
        self.update_status(f"✗ Error: {message}")
        self.progress_bar.setBaseColor("#FF5722")  # Rojo para error
        self.system_install_button.setEnabled(True)
        self.portable_install_button.setEnabled(True)
        self.cancel_button.setEnabled(True)

def check_everything_installation():
    """Verifica si Everything está instalado"""
    # Verificar instalación del sistema
    system_paths = [
        r"C:\Program Files\Everything\Everything.exe",
        r"C:\Program Files (x86)\Everything\Everything.exe"
    ]
    
    for path in system_paths:
        if os.path.exists(path):
            return True, path
    
    # Verificar instalación portátil
    if getattr(sys, 'frozen', False):
        exe_dir = os.path.dirname(sys.executable)
        portable_path = os.path.join(exe_dir, "Everything_Portable", "Everything.exe")
        if os.path.exists(portable_path):
            return True, portable_path
    else:
        project_dir = os.path.dirname(os.path.abspath(__file__))
        portable_path = os.path.join(project_dir, "Everything_Portable", "Everything.exe")
        if os.path.exists(portable_path):
            return True, portable_path
    
    return False, None

def show_everything_install_dialog(parent=None):
    """Muestra el diálogo de instalación de Everything"""
    dialog = EverythingInstallDialog(parent)
    result = dialog.exec()
    return result == QDialog.DialogCode.Accepted

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    installed, path = check_everything_installation()
    if not installed:
        if show_everything_install_dialog():
            print("Everything instalado correctamente")
        else:
            print("Instalación cancelada")
    else:
        print(f"Everything ya está instalado en: {path}")
    
    sys.exit(0)