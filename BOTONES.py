from PyQt6.QtWidgets import (
    QPushButton, QLabel, QFrame, QVBoxLayout, QHBoxLayout, QApplication,
    QProgressBar, QGraphicsDropShadowEffect, QDialog, QWidget
)
from PyQt6.QtGui import QIcon, QColor, QPainter, QPainterPath, QLinearGradient, QPen, QPixmap
from PyQt6.QtCore import (
    Qt, QSize, QThread, QPropertyAnimation, pyqtProperty, QEasingCurve, 
    QPoint, QRectF, QRect, QTimer, pyqtSignal, QMetaObject
)
from MONITOREO import get_motherboard_info, get_disk_serial_number
from CREAR import create_download_icon, create_upload_icon, icono_espejo_copiar, icono_espejo_comparar, icono_espejo_normal, create_stop_icon, icono_MATRIX
import os
import time
import queue
import ctypes
import traceback
from APARIENCIA import apply_acrylic_and_rounded
import win32api
import json
import sys
from cryptography.fernet import Fernet
from datetime import datetime
from AJUSTES import load_config
from CREAR import (
    CustomCloseButton, 
    CustomMaximizeButton, 
    CustomMinimizeButton, 
    registro,
    create_code_icon,
    create_download_icon, # Añadir esta línea
    create_settings_icon,
    boton_pausa,
    create_eye_icon,
    create_eject_icon,
    renombrar,
    create_refresh_icon,
    nueva_carpeta,
    create_repair_icon,
    boton_empaketar,
    boton_back,
    create_clear_icon,
    create_reindex_icon,
    Icono_BUSCADOR
)
class AnimatedButton(QPushButton):
    def __init__(self, icon_path=None, parent=None, is_custom_icon=False):
        super().__init__(parent)
        self.setFlat(True)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setFixedSize(28, 28)
        self.is_custom_icon = is_custom_icon
        self._tooltip = None
        self.unsetCursor()
        
        # Si se proporciona una ruta de icono, cargar desde archivo
        if icon_path:
            self.setIcon(QIcon(icon_path))
        
        # Configurar tamaños de icono
        self._icon_size = 20
        self._hover_size = 24
        self.setIconSize(QSize(self._icon_size, self._icon_size))
        
        # Configurar efectos de sombra
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(8)
        self.shadow.setXOffset(0)
        self.shadow.setYOffset(0)
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.setGraphicsEffect(self.shadow)
        
        # Tooltip personalizado
        self._tooltip = None
        self.clicked.connect(self.play_button_sound)
        self._tooltip_text = ""
        
        # Estilo del botón
        self.setStyleSheet("""
            QPushButton {
                border: none;
                background: transparent;
            }
        """)
    
    def play_button_sound(self):
        """Reproduce el sonido de botón cuando se hace clic"""
        from SONIDOS import sound_manager
        sound_manager.play_sound('button')
    
    def setToolTip(self, text):
        self._tooltip_text = text

    def update_tooltip(self):
        """Actualiza el tooltip inmediatamente si está visible"""
        if self._tooltip and self._tooltip.isVisible():
            self._tooltip.hide()
            self._tooltip = CustomToolTip(self._tooltip_text)
            self._tooltip.show_tooltip(self.mapToGlobal(QPoint(self.width()//2, 0)))

    def enterEvent(self, event):
        # Llamar al método original para mantener comportamiento base
        super().enterEvent(event)
        
        # Mostrar tooltip si existe
        if self._tooltip_text:
            if not self._tooltip:
                self._tooltip = CustomToolTip(self._tooltip_text)
            self._tooltip.show_tooltip(self.mapToGlobal(QPoint(self.width()//2, 0)))
        
        # Cambiar a efecto de luz al hacer hover
        self.shadow.setColor(QColor(255, 255, 255, 100))
        self.shadow.setBlurRadius(15)
        
        # Animar a un tamaño más grande
        self.animate_icon_size(self._hover_size)

    def leaveEvent(self, event):
        # Llamar al método original para mantener comportamiento base
        super().leaveEvent(event)
        
        # Ocultar tooltip
        if self._tooltip:
            self._tooltip.hide()
            
        # Restaurar efecto de sombra normal
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.shadow.setBlurRadius(8)
        
        # Volver al tamaño original
        self.animate_icon_size(self._icon_size)

    def animate_icon_size(self, target_size):
        # Crear una nueva animación cada vez
        self.animation = QPropertyAnimation(self, b"iconSize")
        self.animation.setDuration(100)
        self.animation.setStartValue(self.iconSize())
        self.animation.setEndValue(QSize(target_size, target_size))
        self.animation.start()

    @pyqtProperty(int)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.setIconSize(QSize(size, size))

class CircularProgressButton(AnimatedButton):
    stop_spinning_signal = pyqtSignal()  # Nueva señal para detener el spinning
    
    def __init__(self, icon_path, parent=None):
        super().__init__(icon_path, parent)
        self.rotation_angle = 0
        self.is_spinning = False
        self.spin_timer = QTimer(self)
        self.spin_timer.timeout.connect(self.update_rotation)
        self.spin_timer.setInterval(50)
        self._original_icon = None
        self._icon_pixmap = None
        
        # Conectar la señal al método de detención
        self.stop_spinning_signal.connect(self._stop_spinning_internal)

    def update_rotation(self):
        self.rotation_angle = (self.rotation_angle + 10) % 360
        self.update()

    def start_spinning(self):
        # Asegurarnos de que esto se ejecuta en el hilo principal
        if QThread.currentThread() is QApplication.instance().thread():
            self.is_spinning = True
            self.spin_timer.start()
        else:
            # Si estamos en otro hilo, usar metaObject().invokeMethod
            QMetaObject.invokeMethod(self, "start_spinning",
                                   Qt.ConnectionType.QueuedConnection)

    def stop_spinning(self):
        # Emitir la señal para detener el spinning desde cualquier hilo
        self.stop_spinning_signal.emit()

    def _stop_spinning_internal(self):
        """Método interno que se ejecuta en el hilo principal"""
        self.is_spinning = False
        self.spin_timer.stop()
        self.rotation_angle = 0
        self.update()

    def set_icon_and_pixmap(self, icon, pixmap):
        """Método para establecer tanto el icono como el pixmap"""
        self._original_icon = icon
        self._icon_pixmap = pixmap
        self.setIcon(icon)

    def paintEvent(self, event):
        if self.is_spinning and self._icon_pixmap:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
            painter.save()
            
            center = self.rect().center()
            current_size = self._icon_size
            icon_rect = QRect(
                center.x() - current_size // 2,
                center.y() - current_size // 2,
                current_size,
                current_size
            )
            
            painter.translate(center)
            painter.rotate(self.rotation_angle)
            painter.translate(-center)
            
            if self._icon_pixmap:
                painter.drawPixmap(
                    icon_rect,
                    self._icon_pixmap.scaled(
                        QSize(current_size, current_size),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                )
            
            painter.restore()
        else:
            super().paintEvent(event)

# Importar las clases de reindexación desde el nuevo módulo
from REINDEXAR import ReindexProgressDialog, ReindexThread, ReindexSelectionDialog

class CustomMessageBox(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)
        self.content_frame = QFrame(self)
        self.content_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.content_frame.setFrameShadow(QFrame.Shadow.Raised)
        self.content_frame.setStyleSheet("QFrame { background-color: #2D2D2C; border-radius: 10px; border: 1px solid gray; }")
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(10)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(0)
        self.shadow_effect.setColor(Qt.GlobalColor.black)
        self.content_frame.setGraphicsEffect(self.shadow_effect)
        self.color_animation = QPropertyAnimation(self, b"shadow_color")
        self.color_animation.setDuration(2000)
        self.color_animation.setLoopCount(-1)
        colors = [
            QColor(66, 133, 244, 160),   # Google Blue
            QColor(234, 67, 53, 160),    # Google Red
            QColor(251, 188, 5, 160),    # Google Yellow
            QColor(52, 168, 83, 160),    # Google Green
            QColor(66, 133, 244, 160)    # Volver al azul
        ]
        for i, color in enumerate(colors):
            self.color_animation.setKeyValueAt(i/(len(colors)-1), color)
        self.color_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self.color_animation.start()
        self.layout = QVBoxLayout(self.content_frame)
        self.message_label = QLabel("¿CERRAR APLICACION?", self.content_frame)
        self.message_label.setStyleSheet("color: white; border: none;")
        self.layout.addWidget(self.message_label)
        self.button_layout = QHBoxLayout()
        self.button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        from CREAR import create_yes_icon
        self.yes_button = AnimatedButton(None)
        self.yes_button.setIcon(create_yes_icon(size=30))
        self.yes_button.setIconSize(QSize(25, 25))
        self.yes_button._icon_size = 25
        self.yes_button.clicked.connect(self.accept)
        self.yes_button.enterEvent = self.on_yes_hover
        self.yes_button.leaveEvent = self.on_leave_hover
        self.yes_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.button_layout.addWidget(self.yes_button)
        from CREAR import create_no_icon
        self.no_button = AnimatedButton(None)
        self.no_button.setIcon(create_no_icon(size=30))
        self.no_button.setIconSize(QSize(25, 25))
        self.no_button._icon_size = 25
        self.no_button.clicked.connect(self.reject)
        self.no_button.enterEvent = self.on_no_hover
        self.no_button.leaveEvent = self.on_leave_hover
        self.no_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.button_layout.addWidget(self.no_button)
        self.layout.addLayout(self.button_layout)
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self.content_frame)

    def on_yes_hover(self, event):
        self.shadow_effect.setColor(Qt.GlobalColor.blue)
        AnimatedButton.enterEvent(self.yes_button, event)

    def on_no_hover(self, event):
        self.shadow_effect.setColor(Qt.GlobalColor.red)
        AnimatedButton.enterEvent(self.no_button, event)

    def on_leave_hover(self, event):
        self.shadow_effect.setColor(Qt.GlobalColor.black)
        AnimatedButton.leaveEvent(self.yes_button, event)
        AnimatedButton.leaveEvent(self.no_button, event)

    @pyqtProperty(QColor)
    def shadow_color(self):
        return self.shadow_effect.color()

    @shadow_color.setter
    def shadow_color(self, color):
        self.shadow_effect.setColor(color)

class CustomToolTip(QLabel):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.setStyleSheet("""
            QLabel {
                color: white;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(150)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def paintEvent(self, event):
        if self.isVisible():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            path = QPainterPath()
            rect = QRectF(self.rect()).adjusted(1, 1, -1, -1)
            radius = min(rect.height(), rect.width()) * 0.47  # Aumentado de 0.25 a 0.45 para bordes más redondeados
            path.addRoundedRect(rect, radius, radius)
            painter.setPen(Qt.PenStyle.NoPen)
            for i in range(2):
                shadow_path = QPainterPath()
                shadow_rect = rect.adjusted(-i, -i, i, i)
                shadow_path.addRoundedRect(shadow_rect, radius, radius)
                painter.setBrush(QColor(0, 0, 0, 15 - i * 5))
                painter.drawPath(shadow_path)
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0.0, QColor(65, 65, 65, 250))
            gradient.setColorAt(0.5, QColor(55, 55, 55, 250))
            gradient.setColorAt(1.0, QColor(50, 50, 50, 250))
            painter.setBrush(gradient)
            painter.drawPath(path)
            pen = QPen()
            pen.setColor(QColor(255, 255, 255, 30))
            pen.setWidth(1)
            pen.setStyle(Qt.PenStyle.SolidLine)
            painter.setPen(pen)
            painter.drawPath(path)
            super().paintEvent(event)
        
    def hideEvent(self, event):
        self.opacity_animation.stop()
        self.scale_animation.stop()
        super().hideEvent(event)

    def show_tooltip(self, pos):
        self.adjustSize()
        final_pos = pos + QPoint(-self.width() // 2, 35)
        self.move(final_pos)
        self.show()



class Botones(QFrame):
    reparar_signal = pyqtSignal(list)
    mostrar_signal = pyqtSignal()
    cola_signal = pyqtSignal(str)
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.mirror_selected = False
        self.mirror_mode = "normal"
        self.button_container = QFrame(self)
        self.button_container.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 40);
                border-radius: 17px;
                border: 1px solid rgba(255, 255, 255, 30);
            }
        """)
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(QColor(0, 0, 0, 50))
        self.button_container.setGraphicsEffect(shadow)
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        button_layout = QHBoxLayout(self.button_container)
        button_layout.setContentsMargins(12, 2, 12, 2)
        button_layout.setSpacing(8)
        self.BOTON_STOP()
        self.create_back_button()
        self.create_new_folder_button()
        self.registro()
        self.NEW_EMPAKETADO()
        self.CLEAR()
        self.RENAME()
        self.COLA()
        self.button_container.adjustSize()
        main_layout.addWidget(self.button_container)
        self.setFixedHeight(50)
        self.set_hand_cursor_for_all_buttons()

    def BOTON_STOP(self):
        button_layout = self.button_container.layout()
        self.STOP = AnimatedButton(None, is_custom_icon=True)  # Indicamos que es un icono personalizado
        self.STOP.setIcon(create_stop_icon(size=26))  # Tamaño base del icono
        self.STOP.setIconSize(QSize(22, 22))  # Tamaño inicial del icono
        self.STOP._icon_size = 22 # Tamaño inicial
        self.STOP._hover_size = 26  # Tamaño al hacer hover
        self.STOP.clicked.connect(self.check_license_for_stop)
        self.STOP.setToolTip("Detener Copia")
        self.STOP.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.STOP)
        
        # Crear el botón RESTORE con el nuevo icono personalizado
        from CREAR import restaurar_copia
        self.RESTORE = AnimatedButton(None, is_custom_icon=True)
        self.RESTORE.setIcon(restaurar_copia(size=26))
        self.RESTORE.setIconSize(QSize(22, 22))
        self.RESTORE._icon_size = 22
        self.RESTORE._hover_size = 26
        self.RESTORE.clicked.connect(self.check_license_for_restart)
        self.RESTORE.setToolTip("Reiniciar Operación")
        self.RESTORE.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.RESTORE)
        

        self.PAUSE = AnimatedButton(None, is_custom_icon=True)
        self.PAUSE.setIcon(boton_pausa(size=26))
        self.PAUSE.setIconSize(QSize(21, 21))
        self.PAUSE._icon_size = 21
        self.PAUSE._hover_size = 26
        self.PAUSE.clicked.connect(self.pause_queue)
        self.PAUSE.setToolTip("Pausar Cola")
        self.PAUSE.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.PAUSE)

        self.EXPULSAR = AnimatedButton(None, is_custom_icon=True)
        self.EXPULSAR.setIcon(create_eject_icon(size=28))
        self.EXPULSAR.setIconSize(QSize(24, 24))
        self.EXPULSAR._icon_size = 24
        self.EXPULSAR._hover_size = 28
        self.EXPULSAR.clicked.connect(self.check_license_for_expulsar)
        self.EXPULSAR.setToolTip("Expulsar Disco")
        self.EXPULSAR.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.EXPULSAR)
        
        # Nuevo botón DISK_ARROW con icono personalizado
        self.DISK_ARROW = AnimatedButton(None, is_custom_icon=True)
        self.DISK_ARROW.setIcon(icono_MATRIX(size=26))
        self.DISK_ARROW.setIconSize(QSize(23, 23))
        self.DISK_ARROW._icon_size = 23
        self.DISK_ARROW._hover_size = 26
        self.DISK_ARROW.setToolTip("MATRIX")
        self.DISK_ARROW.setCursor(Qt.CursorShape.PointingHandCursor)
        # Sobrescribir el método mousePressEvent para manejar el clic derecho
        self.DISK_ARROW.mousePressEvent = lambda event: self.handle_disk_arrow_click(event)
        button_layout.addWidget(self.DISK_ARROW)
        
        self.CLOSE = CustomCloseButton()
        self.CLOSE.clicked.connect(self.closeEvent)
        button_layout.addWidget(self.CLOSE)

        salvar_icon = create_upload_icon(size=24)  # Crear el icono con la función definida
        self.SALVAR = AnimatedButton(None, is_custom_icon=True)
        self.SALVAR.setIcon(salvar_icon)  # Establecer el icono en el botón
        self.SALVAR.setToolTip("Guardar Cola")
        self.SALVAR.clicked.connect(self.check_license_for_salvar)
        self.SALVAR.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.SALVAR)

        # Usar el nuevo icono generado programáticamente para CARGAR
        self.CARGAR = AnimatedButton(None, is_custom_icon=True)
        self.CARGAR.setIcon(create_download_icon(size=24))
        self.CARGAR.setIconSize(QSize(20, 20))
        self.CARGAR._icon_size = 20
        self.CARGAR._hover_size = 24
        self.CARGAR.clicked.connect(self.check_license_for_cargar)
        self.CARGAR.setToolTip("Cargar Cola")
        self.CARGAR.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.CARGAR)

        self.AJUSTES = AnimatedButton(None, is_custom_icon=True)
        self.AJUSTES.setIcon(create_settings_icon(size=28))
        self.AJUSTES.setIconSize(QSize(23, 23))
        self.AJUSTES._icon_size = 23
        self.AJUSTES._hover_size = 28
        self.AJUSTES.clicked.connect(self.open_ajustes)
        self.AJUSTES.setToolTip("Configuración")
        self.AJUSTES.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.AJUSTES)

        icono_normal, _ = icono_espejo_normal(size=28)  # Cambiar de 24 a 32 (o el tamaño deseado)
        self.MIRROR = AnimatedButton(icono_normal)
        self.MIRROR.setIconSize(QSize(26, 26))  # Ajustar el tamaño del icono en el botón
        self.MIRROR._icon_size = 26  # Tamaño base
        self.MIRROR._hover_size = 28  # Tamaño al hacer hover
        self.MIRROR.clicked.connect(self.toggle_mirror_mode)
        self.MIRROR.setToolTip("MODO COMPARACIÓN")
        self.MIRROR.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.MIRROR)
        
        self.MAXIMIZAR = CustomMaximizeButton()
        self.MAXIMIZAR.clicked.connect(self.MAXIMIZAR_action)
        button_layout.addWidget(self.MAXIMIZAR)

        self.MINIMIZAR = CustomMinimizeButton()
        self.MINIMIZAR.clicked.connect(self.MINIMIZAR_action)
        button_layout.addWidget(self.MINIMIZAR)

        self.MOSTRAR = AnimatedButton(None, is_custom_icon=True)
        self.MOSTRAR.setIcon(create_eye_icon(size=32))
        self.MOSTRAR.setIconSize(QSize(28, 28))
        self.MOSTRAR._icon_size = 28
        self.MOSTRAR._hover_size = 32
        self.MOSTRAR.clicked.connect(self.emitir_mostrar_signal)
        self.MOSTRAR.setToolTip("Actualizar Discos")
        self.MOSTRAR.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.MOSTRAR)

        icon, pixmap = create_reindex_icon(32)
        self.REINDEXAR = CircularProgressButton(None)
        self.REINDEXAR.setIcon(icon)
        self.REINDEXAR._icon_pixmap = pixmap  # Establecer el pixmap original para la animación
        self.REINDEXAR.clicked.connect(self.check_license_for_reindexar)
        self.REINDEXAR.setToolTip("Reindexar Discos")
        self.REINDEXAR.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.REINDEXAR)

        # **CÓDIGO CORREGIDO: Botón de búsqueda (Zfind) al lado de Reindexar Discos**
        # Asegúrate de importar Icono_BUSCADOR al principio del archivo BOTONES.py
        # (ya lo hiciste, lo cual es bueno)

        self.ZFIND = AnimatedButton(None, is_custom_icon=True) # Usar AnimatedButton para el efecto hover
        self.ZFIND.setIcon(Icono_BUSCADOR(28)) # Usar el icono de búsqueda
        self.ZFIND.setIconSize(QSize(24, 24))
        self.ZFIND._icon_size = 24  # Tamaño inicial
        self.ZFIND._hover_size = 28  # Tamaño al hacer hover
        self.ZFIND.setToolTip("Zfind") # Tooltip corregido
        self.ZFIND.clicked.connect(self.open_zfind_window) # Conectar al nuevo método
        self.ZFIND.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.ZFIND) # Añade el botón de búsqueda al layout

        self.setup_filter_button()
        button_layout.addWidget(self.FILTER)

        self.REPARAR = AnimatedButton(None, is_custom_icon=True)  # Indicamos que es un icono personalizado
        self.REPARAR.setIcon(create_repair_icon(size=28))  # Tamaño base del icono
        self.REPARAR.setIconSize(QSize(24, 24))  # Tamaño inicial del icono
        self.REPARAR._icon_size = 24  # Tamaño inicial
        self.REPARAR._hover_size = 28  # Tamaño al hacer hover
        self.REPARAR.clicked.connect(self.check_license_for_reparar)
        self.REPARAR.setToolTip("Reparar Discos")
        self.REPARAR.setCursor(Qt.CursorShape.PointingHandCursor)
        button_layout.addWidget(self.REPARAR)

    def setup_filter_button(self):
        from CREAR import icono_Discos
        self.FILTER = AnimatedButton(None, is_custom_icon=True)
        self.FILTER.setIcon(icono_Discos(size=40))
        
        # Configurar tamaño base
        self.FILTER.setMinimumSize(24, 24)
        self.FILTER.setMaximumSize(24, 24)
        self.FILTER.setIconSize(QSize(20, 20))
        self.FILTER._icon_size = 20  # Tamaño base del icono
        
        # Restaurar la animación con tamaños personalizados
        original_enter = self.FILTER.enterEvent
        original_leave = self.FILTER.leaveEvent
        
        def custom_enter_event(event):
            # Llamar al método original para efectos de sombra
            super(type(self.FILTER), self.FILTER).enterEvent(event)
            # Animar a un tamaño ligeramente mayor
            if self.current_filter == "usb":
                self.FILTER.animate_icon_size(34)  # Aumentar de 30 a 34 para USB
            else:
                self.FILTER.animate_icon_size(24)  # Aumentar de 20 a 24 para otros
        
        def custom_leave_event(event):
            # Llamar al método original para efectos de sombra
            super(type(self.FILTER), self.FILTER).leaveEvent(event)
            # Volver al tamaño original
            if self.current_filter == "usb":
                self.FILTER.animate_icon_size(30)  # Volver a 30 para USB
            else:
                self.FILTER.animate_icon_size(20)  # Volver a 20 para otros
        
        # Reemplazar los métodos
        self.FILTER.enterEvent = custom_enter_event
        self.FILTER.leaveEvent = custom_leave_event
        
        self.FILTER.setToolTip("Mostrar Todos los Discos")
        self.current_filter = "all"  # Estado inicial
        self.FILTER.clicked.connect(self.toggle_filter_mode)

    def toggle_filter_mode(self):
        modes = {
            "all": {
                "next": "internal",
                "tooltip": "Mostrar Todos los Discos"
            },
            "internal": {
                "next": "external",
                "tooltip": "Mostrar Solo Internos"
            },
            "external": {
                "next": "usb",
                "tooltip": "Mostrar Solo Externos"
            },
            "usb": {
                "next": "all",
                "tooltip": "Mostrar Solo USB"
            }
        }
        self.current_filter = modes[self.current_filter]["next"]
        if self.current_filter == "usb":
            from CREAR import create_usb_icon
            # Reducir el tamaño de generación del icono USB
            usb_icon = create_usb_icon(size=40)  # Reducido de 80 a 40
            self.FILTER.setIcon(usb_icon)
            # Mantener el tamaño visual similar a los otros iconos
            self.FILTER.setIconSize(QSize(24, 24))  # Reducido de 30 a 24
            self.FILTER._icon_size = 24  # Reducido de 30 a 24
            original_enter = self.FILTER.enterEvent
            original_leave = self.FILTER.leaveEvent
            def usb_enter_event(event):
                super(type(self.FILTER), self.FILTER).enterEvent(event)
                self.FILTER.animate_icon_size(28)  # Reducido de 34 a 28 en hover
            def usb_leave_event(event):
                super(type(self.FILTER), self.FILTER).leaveEvent(event)
                self.FILTER.animate_icon_size(24)  # Reducido de 30 a 24 al salir
            self.FILTER.enterEvent = usb_enter_event
            self.FILTER.leaveEvent = usb_leave_event
        elif self.current_filter == "internal":
            # Importar y usar la función create_internal_hdd_icon
            from CREAR import create_internal_hdd_icon
            internal_icon = create_internal_hdd_icon(size=40)
            self.FILTER.setIcon(internal_icon)
            self.FILTER.setIconSize(QSize(20, 20))
            self.FILTER._icon_size = 20
            original_enter = self.FILTER.enterEvent
            original_leave = self.FILTER.leaveEvent
            def normal_enter_event(event):
                super(type(self.FILTER), self.FILTER).enterEvent(event)
                self.FILTER.animate_icon_size(24)  # Aumentar a 24 en hover
            def normal_leave_event(event):
                super(type(self.FILTER), self.FILTER).leaveEvent(event)
                self.FILTER.animate_icon_size(20)  # Volver a 20 al salir
            self.FILTER.enterEvent = normal_enter_event
            self.FILTER.leaveEvent = normal_leave_event
        elif self.current_filter == "external":
            from CREAR import create_external_hdd_icon
            external_icon = create_external_hdd_icon(size=40)
            self.FILTER.setIcon(external_icon)
            self.FILTER.setIconSize(QSize(20, 20))
            self.FILTER._icon_size = 20
            original_enter = self.FILTER.enterEvent
            original_leave = self.FILTER.leaveEvent
            def normal_enter_event(event):
                super(type(self.FILTER), self.FILTER).enterEvent(event)
                self.FILTER.animate_icon_size(24)  # Aumentar a 24 en hover
            def normal_leave_event(event):
                super(type(self.FILTER), self.FILTER).leaveEvent(event)
                self.FILTER.animate_icon_size(20)  # Volver a 20 al salir
            self.FILTER.enterEvent = normal_enter_event
            self.FILTER.leaveEvent = normal_leave_event
        else:  # caso "all"
            from CREAR import icono_Discos
            self.FILTER.setIcon(icono_Discos(size=40))
            self.FILTER.setIconSize(QSize(20, 20))
            self.FILTER._icon_size = 20
            original_enter = self.FILTER.enterEvent
            original_leave = self.FILTER.leaveEvent
            def normal_enter_event(event):
                super(type(self.FILTER), self.FILTER).enterEvent(event)
                self.FILTER.animate_icon_size(24)  # Aumentar a 24 en hover
            def normal_leave_event(event):
                super(type(self.FILTER), self.FILTER).leaveEvent(event)
                self.FILTER.animate_icon_size(20)  # Volver a 20 al salir
            self.FILTER.enterEvent = normal_enter_event
            self.FILTER.leaveEvent = normal_leave_event
        self.FILTER.setToolTip(modes[self.current_filter]["tooltip"])
        self.apply_disk_filter(self.current_filter)
        self.FILTER.update()

    def toggle_mirror_mode(self):
        """Alterna entre los tres modos del espejo: normal, sin copiar, copiando"""
        svg_size = 28        # Tamaño interno del SVG (calidad)
        display_size = 26    # Tamaño visual del icono en pantalla

        if self.mirror_mode == "normal":
            self.mirror_mode = "sin_copiar"
            self.mirror_selected = True
            icono, _ = icono_espejo_comparar(size=svg_size)
            self.MIRROR.setIcon(icono)
            self.MIRROR.setIconSize(QSize(display_size, display_size))
            self.MIRROR._icon_size = display_size
            self.MIRROR.setToolTip("Espejo Sin Copiar")
            self.MIRROR.update_tooltip()  

        elif self.mirror_mode == "sin_copiar":
            self.mirror_mode = "copiando"
            self.mirror_selected = True
            icono, _ = icono_espejo_copiar(size=svg_size)
            self.MIRROR.setIcon(icono)
            self.MIRROR.setIconSize(QSize(display_size, display_size))
            self.MIRROR._icon_size = display_size
            self.MIRROR.setToolTip("Espejo Copiando")
            self.MIRROR.update_tooltip()  

        else:
            self.mirror_mode = "normal"
            self.mirror_selected = False
            icono, _ = icono_espejo_normal(size=svg_size)
            self.MIRROR.setIcon(icono)
            self.MIRROR.setIconSize(QSize(display_size, display_size))
            self.MIRROR._icon_size = display_size
            self.MIRROR.setToolTip("MODO COMPARACIÓN")
            self.MIRROR.update_tooltip()  


    def set_normal_mode(self):
        """Forza el modo normal del espejo"""
        svg_size = 28
        display_size = 26

        self.mirror_mode = "normal"
        self.mirror_selected = False
        icono, _ = icono_espejo_normal(size=svg_size)
        self.MIRROR.setIcon(icono)
        self.MIRROR.setIconSize(QSize(display_size, display_size))
        self.MIRROR._icon_size = display_size
        self.MIRROR.setToolTip("MODO COMPARACIÓN")
        self.MIRROR.update_tooltip()  

    def emitir_cola_signal(self):
        selected_items = self.main_window.list_widget.selectedItems()
        if not selected_items:
            print("No hay discos seleccionados para mostrar la cola.")
            return
        disco = selected_items[0].text().split(' ')[-1].strip('()')
        print(f"Emitiendo señal para mostrar la cola del disco: {disco}")
        self.cola_signal.emit(disco)
    
    def emitir_mostrar_signal(self):
        """Maneja la actualización de discos de forma asíncrona"""
        if self.current_filter == "all":
            try:
                if hasattr(self, '_update_worker') and self._update_worker.isRunning():
                    print("Ya hay una actualización en curso")
                    return
                
                self.MOSTRAR.setEnabled(False)
                if isinstance(self.MOSTRAR, CircularProgressButton):
                    self.MOSTRAR.start_spinning()
                    
                self._update_worker = DiskUpdateWorker(self.main_window)
                self._update_worker.finished.connect(self._on_update_complete)
                self._update_worker.error.connect(self._on_update_error)
                self._update_worker.progress.connect(lambda msg: print(f"Progreso: {msg}"))
                # Conectar la nueva señal al método update_volume_list
                self._update_worker.volumes_updated.connect(self.main_window.update_volume_list)
                self._update_worker.start()
                
            except Exception as e:
                print(f"Error iniciando actualización: {e}")
                self._reset_button_state()
        else:
            print(f"Actualización ignorada: modo de filtro activo ({self.current_filter})")
    def _on_update_complete(self):
        """Método llamado cuando finaliza la actualización"""
        self._reset_button_state()
        self.mostrar_signal.emit()
        print("Actualización completada exitosamente")
        if hasattr(self, '_update_worker'):
            self._update_worker.deleteLater()
            delattr(self, '_update_worker')

    def _on_update_error(self, error_msg):
        """Maneja errores durante la actualización"""
        self._reset_button_state()
        print(f"Error en actualización: {error_msg}")
        if hasattr(self, '_update_worker'):
            self._update_worker.deleteLater()
            delattr(self, '_update_worker')

    def _reset_button_state(self):
        """Restaura el estado del botón"""
        self.MOSTRAR.setEnabled(True)
        if isinstance(self.MOSTRAR, CircularProgressButton):
            self.MOSTRAR.stop_spinning()

    def emitir_reparar_signal(self):
        selected_items = self.main_window.list_widget.selectedItems()
        if not selected_items:
            print("No hay discos seleccionados para reparar.")
            return
        discos = [item.text().split(' ')[-1].strip('()') for item in selected_items]
        print(f"Emitiendo señal para reparar discos: {discos}")
        self.reparar_signal.emit(discos)
    
    def open_ajustes(self):
        """Abre la ventana de ajustes precargada"""
        try:
            # Mostrar un indicador de carga
            loading_indicator = QLabel("Cargando configuración...", self.main_window)
            loading_indicator.setStyleSheet("background-color: rgba(0, 0, 0, 150); color: white; padding: 10px; border-radius: 5px;")
            loading_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
            loading_indicator.setFixedSize(200, 50)
            
            # Centrar en la ventana principal
            center_pos = self.main_window.rect().center() - loading_indicator.rect().center()
            loading_indicator.move(center_pos)
            loading_indicator.show()
            
            # Usar QTimer para permitir que se actualice la UI antes de cargar la ventana de ajustes
            def load_settings_window():
                try:
                    # PRIMERO: Verificar si existe el archivo de licencia
                    if getattr(sys, 'frozen', False):
                        base_path = os.path.dirname(sys.executable)
                    else:
                        base_path = os.path.dirname(os.path.abspath(__file__))
                    license_path = os.path.join(base_path, "license.dat")
                    
                    # Si no existe el archivo, simplemente usar la ventana existente o crear una nueva
                    if not os.path.exists(license_path):
                        print("No se encontró archivo de licencia")
                        
                        # Crear la ventana si no existe
                        if not hasattr(self.main_window, 'settings_window') or self.main_window.settings_window is None:
                            from AJUSTES import TransparentWindow
                            self.main_window.settings_window = TransparentWindow(self.main_window)
                        
                        settings_window = self.main_window.settings_window
                        settings_window.license_valid = False
                        
                        # Asegurarse de que LICENCIA y Acerca de estén habilitadas
                        for i in range(settings_window.tab_widget.count()):
                            tab_name = settings_window.tab_widget.tabText(i)
                            if tab_name in ["LICENCIA", "Acerca de", "ACERCA DE", "ACERCA"]:
                                settings_window.tab_widget.setTabEnabled(i, True)
                                if hasattr(settings_window, 'tab_buttons'):
                                    settings_window.tab_buttons[i].setEnabled(True)
                            else:
                                settings_window.tab_widget.setTabEnabled(i, False)
                                if hasattr(settings_window, 'tab_buttons'):
                                    settings_window.tab_buttons[i].setEnabled(False)
                        
                        # Ocultar el indicador de carga
                        loading_indicator.hide()
                        loading_indicator.deleteLater()
                        
                        # Mostrar la ventana de ajustes
                        settings_window.show()
                        settings_window.raise_()
                        settings_window.activateWindow()
                        
                        # Asegurarse de que se muestre la pestaña de LICENCIA
                        for i in range(settings_window.tab_widget.count()):
                            tab_name = settings_window.tab_widget.tabText(i)
                            if tab_name == "LICENCIA":
                                settings_window.tab_widget.setCurrentIndex(i)
                                break
                        
                        return
                    
                    print("\n=== Iniciando verificación de licencia ===")
                    license_valid = False
                    expiration_date = None
                    
                    # Verificar si la licencia ya está activa en la ventana principal
                    if hasattr(self.main_window, 'license_active') and self.main_window.license_active:
                        print("Licencia ya verificada y activa en la ventana principal")
                        license_valid = True
                        # Obtener la fecha de expiración si está disponible
                        if hasattr(self.main_window, 'expiration_date'):
                            expiration_date = self.main_window.expiration_date
                    else:
                        # Verificar la licencia manualmente
                        try:
                            with open(license_path, "rb") as license_file:
                                combined_data = license_file.read()
                                key, encrypted = combined_data.split(b'|', 1)
                                fernet = Fernet(key)
                                decrypted = fernet.decrypt(encrypted).decode()
                                
                                # Separar información de hardware y fecha
                                hw_info, date_str = decrypted.split('||')
                                parts = hw_info.split('|')
                                
                                # Obtener información actual del hardware
                                motherboard_info = get_motherboard_info()
                                current_model = motherboard_info[1]  # Modelo
                                current_disk_serial = get_disk_serial_number("C:")
                                
                                print("\nVerificación de Hardware:")
                                print(f"Hardware almacenado: {parts}")
                                print(f"Hardware actual: ['{current_model}', '{current_disk_serial}']")
                                
                                # Verificar coincidencia de hardware
                                stored_model = parts[0]
                                stored_disk_serial = parts[1] if len(parts) >= 2 else None
                                
                                if current_model == stored_model and current_disk_serial == stored_disk_serial:
                                    hardware_match = True
                                else:
                                    hardware_match = False
                                
                                if hardware_match:
                                    print("Hardware verificado correctamente")
                                    expiration_date = datetime.fromisoformat(date_str)
                                    print(f"Fecha de expiración: {expiration_date}")
                                    print(f"Fecha actual: {datetime.now()}")
                                    
                                    if datetime.now() < expiration_date:
                                        license_valid = True
                                        print("Licencia válida y activa")
                                    else:
                                        print("Licencia expirada")
                                else:
                                    print("Error: Hardware no coincide")
                        except Exception as e:
                            print(f"Error verificando licencia: {e}")
                            import traceback
                            traceback.print_exc()
                    
                    # Usar la ventana precargada si existe, o crear una nueva
                    if not hasattr(self.main_window, 'settings_window') or self.main_window.settings_window is None:
                        from AJUSTES import TransparentWindow
                        self.main_window.settings_window = TransparentWindow(self.main_window)
                    
                    settings_window = self.main_window.settings_window
                    settings_window.license_valid = license_valid
                    
                    # IMPORTANTE: Desconectar cualquier conexión existente para evitar bucles
                    try:
                        if hasattr(settings_window, '_tab_changed_connected') and settings_window._tab_changed_connected:
                            settings_window.tab_widget.currentChanged.disconnect()
                            settings_window._tab_changed_connected = False
                    except Exception as e:
                        print(f"Error al desconectar señal: {e}")
                    
                    # Configurar pestañas según el estado de la licencia
                    if license_valid:
                        settings_window.enable_all_tabs()
                        # Actualizar la pestaña de licencia para mostrar la bienvenida premium
                        for i in range(settings_window.tab_widget.count()):
                            tab_name = settings_window.tab_widget.tabText(i)
                            if tab_name == "LICENCIA":
                                # Crear la pestaña de bienvenida premium
                                old_widget = settings_window.tab_widget.widget(i)
                                if old_widget:
                                    old_widget.deleteLater()
                                empty_tab = QWidget()
                                empty_layout = QVBoxLayout(empty_tab)
                                empty_layout.setContentsMargins(20, 20, 20, 20)
                                
                                content_widget = QWidget()
                                content_widget.setObjectName("welcomeContainer")
                                content_widget.setStyleSheet("""
                                    QWidget#welcomeContainer {
                                        background-color: rgba(0, 0, 0, 0.2);
                                        border-radius: 15px;
                                        border: 1px solid rgba(255, 255, 255, 0.1);
                                    }
                                """)
                                content_layout = QVBoxLayout(content_widget)
                                content_layout.setContentsMargins(30, 30, 30, 30)
                                content_layout.setSpacing(20)

                                # Contenedor para el ícono
                                icon_container = QWidget()
                                icon_container.setFixedSize(150, 150)
                                container_layout = QVBoxLayout(icon_container)
                                container_layout.setContentsMargins(0, 0, 0, 0)

                         
                                icon_path = os.path.join(os.path.dirname(__file__), 'iconos', 'ZETACOPY.ico')
                                icon_pixmap = QPixmap(icon_path)
                                icon_pixmap = icon_pixmap.scaled(128, 128, Qt.AspectRatioMode.KeepAspectRatio, 
                                                            Qt.TransformationMode.SmoothTransformation)

                                # Usar el efecto de sombra importado
                                from PRESENTACION import IconShadowEffect
                                shadow_widget = IconShadowEffect(icon_pixmap)
                                container_layout.addWidget(shadow_widget, alignment=Qt.AlignmentFlag.AlignCenter)
                                content_layout.addWidget(icon_container, alignment=Qt.AlignmentFlag.AlignCenter)

                                # Resto del contenido
                                welcome_label = QLabel()
                                welcome_label.setObjectName("welcomeLabel")
                                welcome_label.setStyleSheet("""
                                    QLabel#welcomeLabel {
                                        background: transparent;
                                    }
                                """)
                                
                                try:
                                    # Obtener la fecha del archivo de licencia
                                    if getattr(sys, 'frozen', False):
                                        base_path = os.path.dirname(sys.executable)
                                    else:
                                        base_path = os.path.dirname(os.path.abspath(__file__))
                                    license_path = os.path.join(base_path, "license.dat")
                                    
                                    with open(license_path, "rb") as license_file:
                                        combined_data = license_file.read()
                                        key, encrypted = combined_data.split(b'|', 1)
                                        fernet = Fernet(key)
                                        decrypted = fernet.decrypt(encrypted).decode()
                                        date_str = decrypted.split('||')[1]
                                        expiration_date = datetime.fromisoformat(date_str)
                                        exp_date_str = expiration_date.strftime("%d/%m/%Y")
                                except Exception as e:
                                    print(f"Error al obtener fecha de licencia: {e}")
                                    exp_date_str = "Error al obtener fecha"
                                
                                welcome_text = f"""
                                    <div style='text-align: center;'>
                                        <p style='font-size: 24px; font-weight: bold; color: #00a2ff; margin-bottom: 20px;'>
                                            ¡ZETACOPY Premium!
                                        </p>
                                        <p style='font-size: 16px; color: white; margin: 15px 0;'>
                                            Gracias por ser parte de la familia ZETACOPY.
                                        </p>
                                        <p style='font-size: 15px; color: #00a2ff; margin: 15px 0;'>
                                            Has desbloqueado todas las características premium:
                                        </p>
                                        <p style='font-size: 14px; color: white; margin: 10px 0;'>
                                            ✓ Transferencia de archivos ilimitada<br>
                                            ✓ Monitoreo en tiempo real<br>
                                            ✓ Gestión avanzada de colas<br>
                                            ✓ Soporte para múltiples dispositivos<br>
                                            ✓ Todas las funciones premium desbloqueadas
                                        </p>
                                        <p style='font-size: 14px; color: #00a2ff; margin-top: 20px;'>
                                            ¡Disfruta de la experiencia completa de ZETACOPY!
                                        </p>
                                        <p style='font-size: 14px; color: #00ff00; margin-top: 15px;'>
                                            Tu licencia es válida hasta: {exp_date_str}
                                        </p>
                                        <p style='font-weight: bold; color: #00a2ff; margin-top: 20px;'>
                                            Creado por Jorge Yadiel Prado
                                        </p>
                                        <p style='color: white; margin-top: 5px;'>
                                            Contacto: +58668183
                                        </p>
                                        <p style='font-size: 12px; color: white;'>
                                            © 2024 Todos los derechos reservados
                                        </p>
                                    </div>
                                """
                                welcome_label.setText(welcome_text)
                                welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                                content_layout.addWidget(welcome_label)
                                empty_layout.addWidget(content_widget)
                                settings_window.tab_widget.removeTab(i)
                                settings_window.tab_widget.insertTab(i, empty_tab, "LICENCIA")
                                break
                    else:
                        # Si la licencia no es válida, bloquear todas las pestañas excepto LICENCIA y Acerca de
                        for i in range(settings_window.tab_widget.count()):
                            tab_name = settings_window.tab_widget.tabText(i)
                            if tab_name in ["LICENCIA", "Acerca de", "ACERCA DE", "ACERCA"]:
                                settings_window.tab_widget.setTabEnabled(i, True)
                                if hasattr(settings_window, 'tab_buttons'):
                                    settings_window.tab_buttons[i].setEnabled(True)
                            else:
                                settings_window.tab_widget.setTabEnabled(i, False)
                                if hasattr(settings_window, 'tab_buttons'):
                                    settings_window.tab_buttons[i].setEnabled(False)
                        
                    # Asegurarse de que la pestaña "Acerca de" SIEMPRE esté habilitada, independientemente del estado de la licencia
                    for i in range(settings_window.tab_widget.count()):
                        tab_name = settings_window.tab_widget.tabText(i)
                        if tab_name in ["Acerca de", "ACERCA DE", "ACERCA"]:
                            settings_window.tab_widget.setTabEnabled(i, True)
                            if hasattr(settings_window, 'tab_buttons'):
                                settings_window.tab_buttons[i].setEnabled(True)
                    
                    # Ocultar el indicador de carga
                    loading_indicator.hide()
                    loading_indicator.deleteLater()
                    
                    # Mostrar la ventana de ajustes
                    settings_window.show()
                    settings_window.raise_()
                    settings_window.activateWindow()
                    
                except Exception as e:
                    print(f"Error al cargar ventana de ajustes: {e}")
                    import traceback
                    traceback.print_exc()
                    loading_indicator.hide()
                    loading_indicator.deleteLater()
            
            QTimer.singleShot(100, load_settings_window)
        except Exception as e:
            print(f"Error al preparar carga de ajustes: {e}")

    def MINIMIZAR_action(self):
        try:
            # Obtener el handle de la ventana
            hwnd = int(self.main_window.winId())
            
            # Importar las funciones necesarias de Windows
            from ctypes import windll
            from ctypes.wintypes import HWND
            
            # Definir las constantes de Windows
            SW_MINIMIZE = 6
            
            # Usar la API de Windows directamente para minimizar
            windll.user32.ShowWindow(HWND(hwnd), SW_MINIMIZE)
            
            # Deshabilitar temporalmente las actualizaciones de la ventana
            self.main_window.setUpdatesEnabled(False)
            
            # Restaurar las actualizaciones después de un breve retraso
            def restore_updates():
                if self.main_window.windowState() == Qt.WindowState.WindowMinimized:
                    self.main_window.setUpdatesEnabled(True)
            
            QTimer.singleShot(500, restore_updates)
            
        except Exception as e:
            print(f"Error en minimización: {str(e)}")
            # Fallback al método estándar si hay error
            self.main_window.showMinimized()
    
    def MAXIMIZAR_action(self):
        if self.main_window.isMaximized():
            self.main_window.showNormal()
        else:
            self.main_window.showMaximized()
    
    def create_back_button(self):
        self.BACK = AnimatedButton(None, is_custom_icon=True)
        self.BACK.setIcon(boton_back(size=32))
        self.BACK.setIconSize(QSize(28, 28))
        self.BACK._icon_size = 28
        self.BACK._hover_size = 32
        self.BACK.clicked.connect(self.main_window.go_back)
        self.BACK.setToolTip("Volver")
        self.layout().addWidget(self.BACK)
        self.BACK.setVisible(False)
    
    def create_new_folder_button(self):
        self.NEW_FOLDER = AnimatedButton(None, is_custom_icon=True)
        self.NEW_FOLDER.setIcon(nueva_carpeta(size=30))
        self.NEW_FOLDER.setIconSize(QSize(26, 26))
        self.NEW_FOLDER._icon_size = 26
        self.NEW_FOLDER._hover_size = 30
        self.NEW_FOLDER.clicked.connect(self.main_window.create_new_folder)
        self.NEW_FOLDER.setToolTip("Nueva Carpeta")
        self.layout().addWidget(self.NEW_FOLDER)
        self.NEW_FOLDER.setVisible(False)

    def registro(self):
        self.NEW_FILES = AnimatedButton(None, is_custom_icon=True)
        self.NEW_FILES.setIcon(registro(size=28))  # Usa el icono de registro
        self.NEW_FILES.setIconSize(QSize(24, 24))
        self.NEW_FILES._icon_size = 24
        self.NEW_FILES._hover_size = 28
        self.NEW_FILES.clicked.connect(self.main_window.create_new_files)  # Llama a la función en ZETACOPY
        self.NEW_FILES.setToolTip("Nuevo Archivo")
        self.layout().addWidget(self.NEW_FILES)
        self.NEW_FILES.setVisible(False)

    def NEW_EMPAKETADO(self):
        self.NEW_EMPAKETADO = AnimatedButton(None, is_custom_icon=True)
        self.NEW_EMPAKETADO.setIcon(boton_empaketar(size=26))  # Reducido de 32 a 28
        self.NEW_EMPAKETADO.setIconSize(QSize(22, 22))  # Reducido de 28 a 24
        self.NEW_EMPAKETADO._icon_size = 22  # Reducido de 28 a 24
        self.NEW_EMPAKETADO._hover_size = 26 # Reducido de 32 a 28
        self.NEW_EMPAKETADO.clicked.connect(self.main_window.create_empaketado)
        self.NEW_EMPAKETADO.setToolTip("Crear Empaketado")
        self.NEW_EMPAKETADO.setVisible(False)
        self.NEW_EMPAKETADO.setCursor(Qt.CursorShape.PointingHandCursor)

    def CLEAR(self):
        self.CLEAR = AnimatedButton(None, is_custom_icon=True)
        self.CLEAR.setIcon(create_clear_icon(size=26))
        self.CLEAR.setIconSize(QSize(22, 22))
        self.CLEAR._icon_size = 22
        self.CLEAR._hover_size = 26
        self.CLEAR.clicked.connect(lambda: self.main_window.handle_delete())
        self.CLEAR.setToolTip("Borrar Selección")
        self.CLEAR.setVisible(False)
        self.CLEAR.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def RENAME(self):
        self.RENAME = AnimatedButton(None, is_custom_icon=True)
        self.RENAME.setIcon(renombrar(size=26))
        self.RENAME.setIconSize(QSize(22, 22))
        self.RENAME._icon_size = 22
        self.RENAME._hover_size = 26
        self.RENAME.clicked.connect(lambda: self.main_window.show_rename_dialog())
        self.RENAME.setToolTip("Renombrar")
        self.RENAME.setVisible(False)
        self.RENAME.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def COLA(self):
        self.COLA = AnimatedButton(None, is_custom_icon=True)
        self.COLA.setIcon(create_code_icon(size=30))
        self.COLA.setIconSize(QSize(22, 22))
        self.COLA._icon_size = 22
        self.COLA._hover_size = 26
        self.COLA.clicked.connect(self.emitir_cola_signal)
        self.COLA.setToolTip("Cola de Archivos")
        self.COLA.setCursor(Qt.CursorShape.PointingHandCursor)


    def closeEvent(self, event):
        try:
            dialog = ConfirmCloseDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Delegar el cierre a la ventana principal
                if hasattr(self.main_window, 'close'):
                    self.main_window.close()  # Esto dispara el closeEvent de MainWindow
                else:
                    QApplication.quit()
                if hasattr(event, 'accept'):
                    event.accept()
                else:
                    QApplication.quit()
            else:
                if hasattr(event, 'ignore'):
                    event.ignore()
                return False
        except Exception as e:
            print(f"Error en closeEvent: {e}")
            return False
        
    def _stop_thread_without_removing_pending(self, drive_letter):
        """Versión mejorada para cerrar hilos correctamente, manejar archivos incompletos y guardar en pending"""
        try:
            if drive_letter in self.main_window.threads:
                thread = self.main_window.threads[drive_letter]
                if thread.is_alive():
                    # Guardar el archivo actual en proceso si existe
                    current_file = None
                    if hasattr(self.main_window, 'current_copying_file') and drive_letter in self.main_window.current_copying_file:
                        current_file = self.main_window.current_copying_file[drive_letter]
                    
                    # 1. Detener el hilo de forma segura primero
                    print(f"Deteniendo hilo para {drive_letter} durante cierre")
                    thread.do_run = False
                    thread.join(timeout=1)
                    
                    if thread.is_alive():
                        print(f"El hilo para {drive_letter} no se detuvo en el tiempo esperado")
                    else:
                        print(f"Hilo para {drive_letter} detenido correctamente")
                    
                    # 2. Manejar el archivo incompleto si existe
                    if current_file:
                        src_path, dst_path = current_file
                        
                        # Verificar si el archivo de destino está incompleto
                        if os.path.exists(dst_path) and os.path.exists(src_path):
                            try:
                                src_size = os.path.getsize(src_path)
                                dst_size = os.path.getsize(dst_path)
                                
                                if dst_size < src_size:
                                    print(f"Archivo incompleto detectado: {dst_path}")
                                    
                                    # Intentar eliminar el archivo incompleto con múltiples métodos
                                    self.delete_file_with_retries(dst_path, max_retries=3, delay=1)
                            except Exception as e:
                                print(f"Error al verificar tamaño de archivos: {e}")
                        
                        # 3. Guardar el archivo actual para restaurarlo después
                        # Asegurarse de que el archivo actual se incluya en las copias pendientes
                        if not hasattr(self.main_window, 'files_in_queue_by_drive'):
                            self.main_window.files_in_queue_by_drive = {}
                        if drive_letter not in self.main_window.files_in_queue_by_drive:
                            self.main_window.files_in_queue_by_drive[drive_letter] = set()
                        
                        # Añadir el archivo actual a files_in_queue y files_in_queue_by_drive
                        self.main_window.files_in_queue.add((src_path, dst_path))
                        self.main_window.files_in_queue_by_drive[drive_letter].add((src_path, dst_path))
                        
                        # Asegurarse de que el archivo actual se incluya en la cola
                        if drive_letter not in self.main_window.queues:
                            self.main_window.queues[drive_letter] = queue.Queue()
                        
                        # Crear una nueva cola temporal con el archivo actual al principio
                        temp_queue = queue.Queue()
                        temp_queue.put((src_path, dst_path))
                        
                        # Añadir el resto de los archivos de la cola original
                        while not self.main_window.queues[drive_letter].empty():
                            item = self.main_window.queues[drive_letter].get()
                            temp_queue.put(item)
                        
                        # Reemplazar la cola original
                        self.main_window.queues[drive_letter] = temp_queue
                        
                        print(f"Archivo actual en proceso guardado para restauración: {src_path}")
                
                # Limpiar referencias al hilo pero mantener la información de la cola
                self.main_window.threads.pop(drive_letter, None)
                
                # 4. Asegurarse de que se guarden las copias pendientes
                self.main_window.save_pending_copies()
                print(f"Copias pendientes guardadas para {drive_letter} incluyendo archivo actual")
        except Exception as e:
            print(f"Error al detener hilo {drive_letter} durante cierre: {e}")
            import traceback
            traceback.print_exc()

    def delete_file_with_retries(self, file_path, max_retries=5, delay=2):
        """Intenta eliminar un archivo con varios métodos y reintentos"""
        for attempt in range(max_retries):
            try:
                # Método 1: Eliminación directa
                os.remove(file_path)
                print(f"Archivo {file_path} eliminado correctamente (método 1).")
                return True
            except PermissionError:
                print(f"Intento {attempt + 1}: No se puede eliminar {file_path} porque está en uso.")
                try:
                    # Método 2: Cambiar permisos y reintentar
                    os.chmod(file_path, 0o777)
                    os.remove(file_path)
                    print(f"Archivo {file_path} eliminado correctamente (método 2).")
                    return True
                except Exception:
                    try:
                        # Método 3: Usar win32file para cerrar handles y eliminar
                        import win32file
                        import win32con
                        
                        # Intentar abrir y cerrar el archivo para liberar handles
                        handle = win32file.CreateFile(
                            file_path,
                            win32con.GENERIC_WRITE,
                            0,  # Sin compartir
                            None,
                            win32con.OPEN_EXISTING,
                            win32con.FILE_ATTRIBUTE_NORMAL,
                            None
                        )
                        win32file.CloseHandle(handle)
                        
                        # Ahora intentar eliminar
                        os.remove(file_path)
                        print(f"Archivo {file_path} eliminado correctamente (método 3).")
                        return True
                    except Exception:
                        # Esperar antes del siguiente intento
                        time.sleep(delay)
            except Exception as e:
                print(f"Error al eliminar {file_path}: {e}")
                time.sleep(delay)
        
        print(f"No se pudo eliminar {file_path} después de {max_retries} intentos.")
        return False
    
    def pause_queue(self):
        selected_items = self.main_window.list_widget.selectedItems()
        for selected_item in selected_items:
            selected_volume = selected_item.text()
            drive_letter = selected_volume.split(' ')[-1].strip('()')
            if drive_letter in self.main_window.threads:
                thread = self.main_window.threads[drive_letter]
                if thread.is_alive():
                    thread.paused = not getattr(thread, 'paused', False)
                    if thread.paused:
                        self.main_window.pause_times[drive_letter] = time.time()
                        print(f"Copia en {drive_letter} pausada.")
                        self.update_speed_label(drive_letter, "Pausado")
                        if hasattr(self.main_window, 'current_explorer_drive') and self.main_window.current_explorer_drive == drive_letter:
                            self.main_window.show_drive_contents(drive_letter)
                    else:
                        pause_duration = time.time() - self.main_window.pause_times.pop(drive_letter, time.time())
                        self.main_window.copy_start_time[drive_letter] += pause_duration
                        print(f"Copia en {drive_letter} reanudada.")
                        self.main_window.update_speed_display()
                        if hasattr(self.main_window, 'current_explorer_drive') and self.main_window.current_explorer_drive == drive_letter:
                            self.main_window.show_drive_contents(drive_letter)
            self.main_window.update()

    def update_speed_label(self, drive_letter, text):
        for i in range(self.main_window.list_widget.count()):
            item = self.main_window.list_widget.item(i)
            if drive_letter in item.text():
                widget = self.main_window.list_widget.itemWidget(item)
                if widget is not None:
                    speed_label = widget.findChildren(QLabel)[2]
                    speed_label.setText(text)
                    break
    
    def expulsar(self):
        self.main_window.expulsar_disco()
        print("Expulsar button clicked")
    
    def clear_queue(self):
        selected_items = self.main_window.list_widget.selectedItems()
        if not selected_items:
            print("No hay discos seleccionados.")
            return
        self.main_window.queue_progress_bar.setValue(0)
        self.main_window.queue_progress_bar.setVisible(False)
        saved_scale = self.main_window.config.get('disk_scale', 100)
        base_height = 35
        base_font = 18
        scale_factor = saved_scale / 100.0
        current_height = int(base_height * scale_factor)
        current_font = int(base_font * scale_factor)
        for selected_item in selected_items:
            drive_letter = selected_item.text().split(' ')[-1].strip('()')
            if not drive_letter.endswith(':'):
                drive_letter += ':'
            print(f"Limpiando cola para el disco: {drive_letter}")
            try:
                # Restablecer el precio antes de detener el hilo
                if hasattr(self.main_window, 'precios_procesados'):
                    self.main_window.precios_procesados.pop(drive_letter, None)
                if hasattr(self.main_window, 'archivos_procesados'):
                    self.main_window.archivos_procesados.pop(drive_letter, None)
                    
                self._stop_thread(drive_letter)
                if hasattr(self.main_window, 'final_space_text'):
                    self.main_window.final_space_text.pop(drive_letter, None)
                
                # Forzar reseteo de barras de progreso con valor 0
                self.main_window.update_progress_signal.emit(drive_letter, 0)
                self.main_window.update_file_progress_signal.emit(drive_letter, 0)
                
                # Llamar explícitamente a _update_ui_after_stop para asegurar limpieza completa
                self._update_ui_after_stop(drive_letter)
                
            except Exception as e:
                print(f"Error al limpiar la cola para {drive_letter}: {e}")
        
        self.main_window.update_all_free_spaces()
        self.main_window.update_queue_progress_signal.emit(0)
        print("Proceso de limpieza completado para todos los discos seleccionados.")

    def set_hand_cursor_for_all_buttons(self):
        for child in self.findChildren(QPushButton):
            child.setCursor(Qt.CursorShape.PointingHandCursor)
            child.setMouseTracking(True)

    def _stop_thread(self, drive_letter):
        try:
            # Marcar que estamos deteniendo este hilo
            if not hasattr(self.main_window, 'stopping_threads'):
                self.main_window.stopping_threads = set()
            self.main_window.stopping_threads.add(drive_letter)
            
            # Detener el hilo principal en MainWindow
            self.main_window.stop_selected_thread(drive_letter)
            
            # Forzar actualización explícita de UI
            for i in range(self.main_window.list_widget.count()):
                item = self.main_window.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.main_window.list_widget.itemWidget(item)
                    if widget:
                        # Resetear barras de progreso y mostrar 0%
                        progress_bar = widget.findChildren(QProgressBar)[0]
                        if progress_bar:
                            progress_bar.setValue(0)
                            progress_bar.setVisible(True)  # Mostrar el 0%
                            progress_bar.update()
                            progress_bar.repaint()
                            QApplication.processEvents()  # Forzar procesamiento de eventos
                            
                        file_progress_bar = widget.findChildren(QProgressBar)[1]
                        if file_progress_bar:
                            file_progress_bar.setValue(0)
                            file_progress_bar.setVisible(True)  # Mostrar el 0%
                            file_progress_bar.update()
                            file_progress_bar.repaint()
                            QApplication.processEvents()  # Forzar procesamiento de eventos
                        
                        speed_label = widget.findChildren(QLabel)[2]
                        if speed_label:
                            speed_label.setText("")
                    break
            
            # Actualizar espacio libre
            
            print(f"UI actualizada correctamente después de detener copia en {drive_letter}")
            
            # Quitar la marca de detención
            if hasattr(self.main_window, 'stopping_threads') and drive_letter in self.main_window.stopping_threads:
                self.main_window.stopping_threads.remove(drive_letter)
        except Exception as e:
            print(f"Error al detener hilo {drive_letter}: {e}")
            # Asegurar que se quite la marca en caso de error
            if hasattr(self.main_window, 'stopping_threads') and drive_letter in self.main_window.stopping_threads:
                self.main_window.stopping_threads.remove(drive_letter)

    def reindexar_discos(self):
        try:
            volumes = []
            for i in range(self.main_window.list_widget.count()):
                item = self.main_window.list_widget.item(i)
                text = item.text()
                volume_name = text.split(' (')[0]
                drive_letter = text.split(' (')[-1].strip(')')
                try:
                    volume_name = win32api.GetVolumeInformation(drive_letter + "\\")[0]
                    disk_info = f"Disco: {volume_name} ({drive_letter})"
                    volumes.append((disk_info, drive_letter))
                except:
                    continue
            if not volumes:
                return
            dialog = ReindexSelectionDialog(volumes, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_drives = dialog.get_selected_drives()
                if selected_drives:
                    if os.path.exists("error_indexado.txt"):
                        os.remove("error_indexado.txt")
                    
                    # NO borrar el archivo completo, usar indexado incremental
                    self.REINDEXAR.setEnabled(False)
                    self.REINDEXAR.start_spinning()  # Iniciar la animación de giro
                    selected_volumes = []
                    for disk_info, drive_letter in volumes:
                        if drive_letter in selected_drives:
                            try:
                                volume_name = win32api.GetVolumeInformation(drive_letter + "\\")[0]
                                selected_volumes.append((f"Disco: {volume_name} ({drive_letter})", drive_letter))
                            except:
                                continue
                    
                    # Pasar información sobre si es indexado completo o incremental
                    self.reindex_thread = ReindexThread(
                        selected_volumes,
                        callback=self.on_reindex_finished,
                        progress_callback=None,
                        incremental=True  # Nuevo parámetro para indexado incremental
                    )
                    self.reindex_thread.start()
        except Exception as e:
            print(f"Error al iniciar reindexación: {e}")
            with open("error_indexado.txt", "w", encoding='utf-8') as error_file:
                error_file.write(f"Error al iniciar el proceso de reindexación:\n{str(e)}\n")
                error_file.write(f"Traza completa del error:\n{traceback.format_exc()}")
            self.REINDEXAR.setEnabled(True)
            self.REINDEXAR.stop_spinning()

    def on_reindex_finished(self):
        """Método llamado cuando finaliza la reindexación"""
        try:
            if os.path.exists("INDEXADO.txt") and os.path.getsize("INDEXADO.txt") > 0:
                print("Reindexación completada exitosamente")
                # Cargar los datos indexados como se hace al inicio
                indexed_data = self.main_window.load_indexed_data()
                self.main_window.indexed_data = indexed_data
                print(f"Datos indexados cargados: {indexed_data.keys()}")
        except Exception as e:
            print(f"Error en finalización de reindexación: {e}")
        finally:
            self.REINDEXAR.setEnabled(True)
            self.REINDEXAR.stop_spinning()

    def apply_disk_filter(self, filter_type):
        try:
            hidden_drives = []
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            hidden_drives_file = os.path.join(base_path, 'hidden_drives.json')
            if not os.path.exists(hidden_drives_file):
                with open(hidden_drives_file, 'w') as f:
                    json.dump([], f)
                print(f"Archivo hidden_drives.json creado en: {hidden_drives_file}")
            for i in range(self.main_window.list_widget.count()):
                item = self.main_window.list_widget.item(i)
                text = item.text()
                drive_letter = text.split('(')[-1].strip(')')
                drive_type = self.main_window.worker.get_drive_type(drive_letter + "\\")
                should_hide = False
                if filter_type == 'all':
                    should_hide = False
                elif filter_type == 'internal':
                    should_hide = not (drive_type == "Disco duro interno" or 
                                     drive_letter.upper().startswith("C:"))
                elif filter_type == 'external':
                    should_hide = not (drive_type == "Disco duro externo" and 
                                     not drive_letter.upper().startswith("C:"))
                elif filter_type == 'usb':
                    should_hide = not (drive_type == "Unidad extraíble (USB)")
                item.setHidden(should_hide)
                if should_hide:
                    try:
                        volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                        disk_info = {
                            "letter": drive_letter,
                            "name": volume_name
                        }
                        hidden_drives.append(disk_info)
                    except Exception as e:
                        print(f"Error obteniendo información del disco {drive_letter}: {e}")
            with open(hidden_drives_file, 'w') as f:
                json.dump(hidden_drives, f)
            if hasattr(self.main_window, 'settings_dialog') and self.main_window.settings_dialog:
                self.main_window.settings_dialog.load_hidden_drives()
            self.main_window.list_widget.update()
            print(f"Drive filter applied: {filter_type}")
        except Exception as e:
            print(f"Error applying disk filter: {e}")
            import traceback
            traceback.print_exc()

    def on_copy_finished(self):
        # Primero ocultar cualquier tooltip
        if hasattr(self, 'close_button'):
            self.close_button.hideTooltip()
        
        # Diferir la actualización de UI
        QTimer.singleShot(0, self.update_ui_after_copy)

    def update_ui_after_copy(self):
        # Actualizar la UI aquí
        pass

    # Añadir estos nuevos métodos para verificar la licencia antes de ejecutar las funciones
    def check_license_for_stop(self):
        self.check_license_and_execute(self.clear_queue)

    def check_license_for_restart(self):
        self.check_license_and_execute(self.main_window.restore_pending_copies)

    def check_license_for_expulsar(self):
        self.check_license_and_execute(self.expulsar)

    def check_license_for_salvar(self):
        """Verifica la licencia antes de ejecutar la función de salvar copia"""
        # Verificar si la licencia está activa
        license_active = False
        if hasattr(self.main_window, 'license_active'):
            license_active = self.main_window.license_active
        
        if license_active:
            # Si la licencia está activa, ejecutar la función de Salvar_Cargar.py
            from Salvar_Cargar import salvar_copia
            salvar_copia(self.main_window)
        else:
            # Si no hay licencia, mostrar el diálogo de activación
            from ACTIVATE import show_license_activation_dialog
            show_license_activation_dialog(self.main_window, self.main_window)

    def check_license_for_cargar(self):
        """Verifica la licencia antes de ejecutar la función de cargar copia"""
        # Verificar si la licencia está activa
        license_active = False
        if hasattr(self.main_window, 'license_active'):
            license_active = self.main_window.license_active
        
        if license_active:
            # Si la licencia está activa, ejecutar la función de Salvar_Cargar.py
            from Salvar_Cargar import cargar_copia
            cargar_copia(self.main_window)
        else:
            # Si no hay licencia, mostrar el diálogo de activación
            from ACTIVATE import show_license_activation_dialog
            show_license_activation_dialog(self.main_window, self.main_window)

    def check_license_and_execute(self, function):
        # Verificar si la licencia está activa
        license_active = False
        if hasattr(self.main_window, 'license_active'):
            license_active = self.main_window.license_active
        
        if license_active:
            # Si la licencia está activa, ejecutar la función normalmente
            function()
        else:
            # Si no hay licencia, mostrar el diálogo de activación
            from ACTIVATE import show_license_activation_dialog
            show_license_activation_dialog(self.main_window, self.main_window)

    def check_license_for_reindexar(self):
        self.check_license_and_execute(self.reindexar_discos)

    def check_license_for_reparar(self):
        self.check_license_and_execute(self.emitir_reparar_signal)  # Aquí está el cambio, debe llamar a emitir_reparar_signal

    def check_license_for_update_disks(self):
        self.check_license_and_execute(self.emitir_mostrar_signal)  # Este es el método correcto que ya tenías

    def check_license_for_disk_action(self):
        """Verifica la licencia antes de ejecutar la acción del disco"""
        if self.main_window.check_license():
            self.disk_action()
        else:
            self.main_window.show_license_dialog()
    
    def handle_disk_arrow_click(self, event):
        """Maneja los clics en el botón DISK_ARROW (MATRIX)"""
        if event.button() == Qt.MouseButton.LeftButton:
            # Clic izquierdo: cargar la matriz seleccionada en los discos seleccionados
            self.load_matrix_to_selected_disks()
        elif event.button() == Qt.MouseButton.RightButton:
            # Clic derecho: mostrar el diálogo de MATRIX
            self.show_matrix_dialog()

    def disk_action(self):
        """Acción a ejecutar cuando se hace clic derecho en el botón de disco con flecha"""
        try:
            # Importar la función desde MATRIX.py
            from MATRIX import show_matrix_dialog
            
            # Mostrar la ventana de MATRIX
            show_matrix_dialog(self.main_window)
            
        except Exception as e:
            print(f"Error en disk_action: {e}")

    def load_matrix_to_selected_disks(self):
        """Carga la matriz seleccionada en los discos seleccionados"""
        try:
            import os
            import queue
            
            # Obtener los discos seleccionados
            selected_items = self.main_window.list_widget.selectedItems()
            if not selected_items:
                print("No hay discos seleccionados para cargar la matriz.")
                return
                
            # Cargar la configuración para obtener la matriz seleccionada
            config = self.main_window.load_config()
            if 'matrix' not in config:
                print("No hay configuración de matriz guardada.")
                return
                
            # Buscar la entrada seleccionada en la matriz
            selected_directory = None
            for entry in config['matrix']:
                if entry.get('selected', False):
                    selected_directory = entry.get('directory')
                    break
                    
            if not selected_directory:
                print("No hay directorio seleccionado en la matriz.")
                return
                
            print(f"Cargando matriz desde: {selected_directory}")
            
            # Verificar que el directorio existe
            if not os.path.exists(selected_directory):
                print(f"El directorio {selected_directory} no existe.")
                return
            
            # Para cada disco seleccionado, procesar el directorio
            for item in selected_items:
                drive_text = item.text()
                drive_letter = drive_text.split(' ')[-1].strip('()')
                print(f"Cargando matriz en disco: {drive_letter}")
                
                # Verificar si el disco está disponible
                if drive_letter in self.main_window.threads and self.main_window.threads[drive_letter].is_alive():
                    print(f"El disco {drive_letter} ya está copiando. No se puede cargar la matriz.")
                    continue
                
                # Inicializar la cola si no existe
                if drive_letter not in self.main_window.queues:
                    self.main_window.queues[drive_letter] = queue.Queue()
                
                # Mostrar la barra de progreso de arrastre
                self.main_window.queue_progress_bar.setValue(0)
                self.main_window.queue_progress_bar.setFormat("Analizando archivos...")
                self.main_window.queue_progress_bar.setVisible(True)
                
                # Obtener el nombre del volumen
                try:
                    volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                except Exception as e:
                    print(f"Error obteniendo nombre del volumen: {e}")
                    volume_name = "Disco"
                
                # Crear una lista de tuplas para processed_files
                # El formato correcto es [(file_path, None, destination_path), ...]
                processed_files = []
                destination_path = os.path.join(drive_letter + "\\", os.path.basename(selected_directory))
                processed_files.append((selected_directory, None, destination_path))
                
                # Llamar al método process_dropped_files con el formato correcto
                if hasattr(self.main_window.list_widget, 'process_dropped_files'):
                    self.main_window.list_widget.process_dropped_files(
                        processed_files=processed_files,
                        drive_letter=drive_letter,
                        shift_pressed=False,
                        current_path=os.path.dirname(selected_directory),
                        volume_name=volume_name,
                        dragging_folder=True
                    )
                    print(f"Matriz cargada en disco {drive_letter} usando process_dropped_files")
                else:
                    print(f"No se encontró el método process_dropped_files")
                
            print("Matriz cargada en los discos seleccionados.")
            
        except Exception as e:
            print(f"Error al cargar la matriz en los discos seleccionados: {e}")
            import traceback
            traceback.print_exc()

    def show_matrix_dialog(self):
        """Muestra el diálogo de MATRIX"""
        try:
            from MATRIX import MatrixDialog
            dialog = MatrixDialog(self.main_window)
            dialog.exec()
        except Exception as e:
            print(f"Error al mostrar el diálogo de MATRIX: {e}")

    def _update_ui_after_stop(self, drive_letter):
        """Actualiza la UI después de detener una copia de forma segura."""
        try:
            # Marcar que estamos actualizando la UI después de detener
            if hasattr(self.main_window, 'stopping_threads') and drive_letter not in self.main_window.stopping_threads:
                self.main_window.stopping_threads.add(drive_letter)
            
            for i in range(self.main_window.list_widget.count()):
                item = self.main_window.list_widget.item(i)
                if drive_letter in item.text():
                    widget = self.main_window.list_widget.itemWidget(item)
                    if widget:
                        # Resetear barras de progreso a 0% y mantenerlas visibles
                        if hasattr(widget, 'progress_bar'):
                            # Primero resetear el valor a 0
                            widget.progress_bar.setValue(0)
                            # Forzar actualización visual
                            widget.progress_bar.update()
                            widget.progress_bar.repaint()
                            QApplication.processEvents()  # Forzar procesamiento de eventos
                            # Mostrar el 0% permanentemente
                            widget.progress_bar.show()
                        
                        if hasattr(widget, 'file_progress_bar'):
                            widget.file_progress_bar.setValue(0)
                            widget.file_progress_bar.update()
                            widget.file_progress_bar.repaint()
                            QApplication.processEvents()  # Forzar procesamiento de eventos
                            widget.file_progress_bar.show()
                        
                        # Limpiar etiqueta de velocidad
                        if hasattr(widget, 'speed_label'):
                            widget.speed_label.setText("")
            
            # Quitar la marca de detención
            if hasattr(self.main_window, 'stopping_threads') and drive_letter in self.main_window.stopping_threads:
                self.main_window.stopping_threads.remove(drive_letter)
        except Exception as e:
            print(f"Error al actualizar UI después de detener: {e}")
            # Asegurar que se quite la marca en caso de error
            if hasattr(self.main_window, 'stopping_threads') and drive_letter in self.main_window.stopping_threads:
                self.main_window.stopping_threads.remove(drive_letter)

    # Método simplificado para abrir la ventana Zfind rápidamente
    def open_zfind_window(self):
        print("Botón de búsqueda Zfind clicado...")
        
        try:
            # Usar el ZFindManager desde MainWindow (ya maneja la verificación de Everything)
            if hasattr(self.main_window, 'zfind_manager'):
                self.main_window.zfind_manager.show_zfind()
                print("ZFind mostrado desde ZFindManager")
            else:
                # Fallback: crear nueva instancia si no existe el manager
                print("ZFindManager no disponible, creando nueva instancia...")
                from ZFind import ZFindWindow
                self.zfind_window_instance = ZFindWindow(parent=None) 
                self.zfind_window_instance.show()
        except Exception as e:
            print(f"Error al mostrar ZFind: {e}")
            import traceback
            traceback.print_exc()

class ConfirmCloseDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # Mensaje con efecto de sombra
        message = QLabel("¿SALIR?")
        message.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        message.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Efecto de sombra para el texto del mensaje
        text_shadow = QGraphicsDropShadowEffect()
        text_shadow.setBlurRadius(8)
        text_shadow.setXOffset(0)
        text_shadow.setYOffset(0)
        text_shadow.setColor(QColor(0, 0, 0, 160))
        message.setGraphicsEffect(text_shadow)
        
        layout.addWidget(message)
        
        # Layout de botones
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        # Botón OK con sombra en el texto
        ok_button = QPushButton("OK")
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42a5f5,
                    stop:0.4 #2196f3,
                    stop:0.6 #1e88e5,
                    stop:1 #1976d2);
                color: white;
                border: none;
                border-radius: 16px;
                padding: 5px 15px;
                font-weight: bold;
                min-width: 60px;
                height: 24px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #64b5f6,
                    stop:0.4 #42a5f5,
                    stop:0.6 #2196f3,
                    stop:1 #1e88e5);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2,
                    stop:0.4 #1e88e5,
                    stop:0.6 #2196f3,
                    stop:1 #42a5f5);
                padding-top: 6px;
                padding-bottom: 4px;
            }
        """)
        
        # Efecto de sombra para el texto del botón OK
        ok_text_shadow = QGraphicsDropShadowEffect()
        ok_text_shadow.setBlurRadius(8)
        ok_text_shadow.setXOffset(0)
        ok_text_shadow.setYOffset(0)
        ok_text_shadow.setColor(QColor(0, 0, 0, 160))
        ok_button.setGraphicsEffect(ok_text_shadow)
        
        # Botón Cancelar con sombra en el texto
        cancel_button = QPushButton("CANCELAR")
        cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff5252,
                    stop:0.4 #f44336,
                    stop:0.6 #e53935,
                    stop:1 #d32f2f);
                color: white;
                border: none;
                border-radius: 16px;
                padding: 5px 15px;
                font-weight: bold;
                min-width: 60px;
                height: 24px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff7070,
                    stop:0.4 #ff5252,
                    stop:0.6 #f44336,
                    stop:1 #e53935);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f,
                    stop:0.4 #e53935,
                    stop:0.6 #f44336,
                    stop:1 #ff5252);
                padding-top: 6px;
                padding-bottom: 4px;
            }
        """)
        
        # Efecto de sombra para el texto del botón Cancelar
        cancel_text_shadow = QGraphicsDropShadowEffect()
        cancel_text_shadow.setBlurRadius(8)
        cancel_text_shadow.setXOffset(0)
        cancel_text_shadow.setYOffset(0)
        cancel_text_shadow.setColor(QColor(0, 0, 0, 160))
        cancel_button.setGraphicsEffect(cancel_text_shadow)

        ok_button.setCursor(Qt.CursorShape.PointingHandCursor)
        cancel_button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

    def showEvent(self, event):
        super().showEvent(event)
        # Aplicar efecto acrílico
        hwnd = int(self.winId())
        apply_acrylic_and_rounded(hwnd)
        
        # Animación de aparición
        self.final_pos = self.pos()
        start_pos = self.final_pos + QPoint(0, 50)
        self.move(start_pos)
        
        anim = QPropertyAnimation(self, b"pos")
        anim.setDuration(300)
        anim.setStartValue(start_pos)
        anim.setEndValue(self.final_pos)
        anim.setEasingCurve(QEasingCurve.Type.OutCubic)
        anim.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        path = QPainterPath()
        path.addRoundedRect(
            float(rect.x()),
            float(rect.y()),
            float(rect.width()),
            float(rect.height()),
            8.0,
            8.0
        )
        # Reducimos la opacidad del fondo negro para coincidir con los otros diálogos
        painter.fillPath(path, QColor(0, 0, 0, 15))

class DiskUpdateWorker(QThread):
    finished = pyqtSignal()
    progress = pyqtSignal(str)
    error = pyqtSignal(str)
    volumes_updated = pyqtSignal(list)  # Nueva señal para enviar los volúmenes filtrados
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self._running = True
        
    def run(self):
        try:
            self.progress.emit("Actualizando discos...")
            
            # Cargar discos ocultos de config.json
            config = load_config()
            config_hidden_drives = config.get('discos_ocultos', [])
            print(f"Discos ocultos en config: {config_hidden_drives}")
            
            # Obtener los volúmenes conectados
            volumes = self.main_window.worker.get_connected_volumes()
            if not self._running:
                return
                
            # Filtrar los volúmenes que están ocultos
            volumes_filtered = []
            for volume in volumes:
                volume_name, drive_type, drive_letter = volume
                disk_info = f"{volume_name} ({drive_letter})"
                
                # Si está oculto en config.json, lo saltamos
                if disk_info in config_hidden_drives:
                    print(f"DiskUpdateWorker: Ignorando disco oculto en config: {disk_info}")
                    continue
                    
                volumes_filtered.append(volume)

            # Emitir la señal con los volúmenes filtrados
            self.volumes_updated.emit(volumes_filtered)
            
        except Exception as e:
            self.error.emit(str(e))
        finally:
            self.finished.emit()
    
    def clear_hidden_drives(self):
        try:
            base_path = os.path.dirname(os.path.abspath(__file__))
            hidden_drives_file = os.path.join(base_path, 'hidden_drives.json')
            temp_file = f"{hidden_drives_file}.tmp"
            with open(temp_file, 'w') as f:
                json.dump([], f)
            if os.path.exists(hidden_drives_file):
                os.remove(hidden_drives_file)
            os.rename(temp_file, hidden_drives_file)
        except Exception as e:
            self.error.emit(f"Error limpiando hidden_drives.json: {str(e)}")
    
    def stop(self):
        self._running = False



