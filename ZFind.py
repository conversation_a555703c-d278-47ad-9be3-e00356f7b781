import sys
import os
from PyQt6.QtWidgets import (
    QMainWindow, QVBoxLayout, QHBoxLayout, QLineEdit, QTreeWidget, QTreeWidgetItem,
    QLabel, QFrame, QPushButton, QApplication, QGraphicsDropShadowEffect, QWidget,
    QSizePolicy, QHeaderView, QStyledItemDelegate, QStyle, QStyleOptionViewItem,
    QAbstractItemView, QStatusBar, QSystemTrayIcon, QMenu
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QThread, QSize, QPoint, QRect, QRunnable, 
    QThreadPool, QObject, QPropertyAnimation, QMimeData, QUrl, QEvent
)
from PyQt6.QtGui import (
    QFont, QColor, QIcon, QPixmap, QPainter, QPainterPath, QKeySequence, 
    QShortcut, QPen, QBrush, QDrag, QImage, QAction
)
from APARIENCIA import apply_acrylic_and_rounded
from CREAR import (
    CustomCloseButton, CustomMaximizeButton, CustomMinimizeButton, 
    icono_carpeta, Icono_BUSCADOR
)
from ICONOS_EXPLORER import FileTypeIconManager
from REDIMENSIONAR import WindowResizer
from Barras_Seleccion import ZfindRoundedRectDelegate  # Importa desde el archivo existente

import subprocess
import ctypes
from ctypes import wintypes
import time

class EverythingAPI:
    """API que utiliza Everything64.dll para realizar búsquedas"""
    
    def __init__(self):
        # Determinar la ruta correcta de la DLL
        if getattr(sys, 'frozen', False):
            # Si está compilado (exe)
            base_path = sys._MEIPASS
        else:
            # En desarrollo
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        self.dll_path = os.path.join(base_path, "Everything64.dll")
        
        # Determinar rutas de Everything (priorizar instalación local)
        self.everything_exe_paths = []
        
        if getattr(sys, 'frozen', False):
            # Si está compilado, buscar en la carpeta del ejecutable primero
            exe_dir = os.path.dirname(sys.executable)
            local_everything = os.path.join(exe_dir, "Everything", "Everything.exe")
            self.everything_exe_paths.append(local_everything)
        else:
            # En desarrollo, buscar en carpeta del proyecto
            project_dir = os.path.dirname(os.path.abspath(__file__))
            local_everything = os.path.join(project_dir, "Everything_Installed", "Everything.exe")
            self.everything_exe_paths.append(local_everything)
        
        # Luego buscar en instalaciones del sistema
        self.everything_exe_paths.extend([
            r"C:\Program Files\Everything\Everything.exe",
            r"C:\Program Files (x86)\Everything\Everything.exe"
        ])
        
        self.everything_exe = self._find_everything_exe()
        
        # Buscar Everything.ini priorizando instalación local
        self.everything_ini_paths = []
        
        if getattr(sys, 'frozen', False):
            # Si está compilado, buscar en la carpeta del ejecutable primero
            exe_dir = os.path.dirname(sys.executable)
            local_ini = os.path.join(exe_dir, "Everything", "Everything.ini")
            self.everything_ini_paths.append(local_ini)
        else:
            # En desarrollo, buscar en carpeta del proyecto
            project_dir = os.path.dirname(os.path.abspath(__file__))
            local_ini = os.path.join(project_dir, "Everything_Installed", "Everything.ini")
            self.everything_ini_paths.append(local_ini)
        
        # Luego buscar en ubicaciones del sistema
        self.everything_ini_paths.extend([
            os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "Everything", "Everything.ini"),
            os.path.join(os.path.expanduser("~"), "AppData", "Local", "Everything", "Everything.ini"),
            os.path.join("C:", "Program Files", "Everything", "Everything.ini"),
            os.path.join("C:", "Program Files (x86)", "Everything", "Everything.ini"),
            os.path.join("Everything", "Everything.ini")
        ])
        self.dll = None
        self._load_dll()
    
    def _find_everything_exe(self):
        """Encuentra la ruta de Everything.exe"""
        for path in self.everything_exe_paths:
            if os.path.exists(path):
                return path
        return None
        
    def _load_dll(self):
        """Carga la DLL de Everything"""
        try:
            # Verificar que la DLL existe
            if not os.path.exists(self.dll_path):
                print(f"EverythingAPI: DLL no encontrada en {self.dll_path}")
                return False
            
            print(f"EverythingAPI: Intentando cargar DLL desde {self.dll_path}")
            
            # Cambiar al directorio de la DLL temporalmente para resolver dependencias
            original_dir = os.getcwd()
            dll_dir = os.path.dirname(self.dll_path)
            
            try:
                if dll_dir:  # Solo cambiar directorio si no está vacío
                    os.chdir(dll_dir)
                
                # Cargar la DLL
                self.dll = ctypes.WinDLL(self.dll_path)
                print("EverythingAPI: DLL cargada correctamente")
            finally:
                if dll_dir:  # Solo restaurar si se cambió
                    os.chdir(original_dir)
            
            # Definir las funciones de la DLL
            self.dll.Everything_SetSearchW.argtypes = [wintypes.LPCWSTR]
            self.dll.Everything_SetSearchW.restype = None
            
            self.dll.Everything_QueryW.argtypes = [wintypes.BOOL]
            self.dll.Everything_QueryW.restype = wintypes.BOOL
            
            self.dll.Everything_GetNumResults.argtypes = []
            self.dll.Everything_GetNumResults.restype = wintypes.DWORD
            
            self.dll.Everything_GetResultFullPathNameW.argtypes = [wintypes.DWORD, wintypes.LPWSTR, wintypes.DWORD]
            self.dll.Everything_GetResultFullPathNameW.restype = wintypes.DWORD
            
            self.dll.Everything_GetResultFileNameW.argtypes = [wintypes.DWORD]
            self.dll.Everything_GetResultFileNameW.restype = wintypes.LPCWSTR
            
            self.dll.Everything_IsFileResult.argtypes = [wintypes.DWORD]
            self.dll.Everything_IsFileResult.restype = wintypes.BOOL
            
            self.dll.Everything_IsFolderResult.argtypes = [wintypes.DWORD]
            self.dll.Everything_IsFolderResult.restype = wintypes.BOOL
            
            self.dll.Everything_SetMax.argtypes = [wintypes.DWORD]
            self.dll.Everything_SetMax.restype = None
            
            self.dll.Everything_GetLastError.argtypes = []
            self.dll.Everything_GetLastError.restype = wintypes.DWORD
            
            # Funciones adicionales para configuración
            self.dll.Everything_SetSort.argtypes = [wintypes.DWORD]
            self.dll.Everything_SetSort.restype = None
            
            self.dll.Everything_SetRequestFlags.argtypes = [wintypes.DWORD]
            self.dll.Everything_SetRequestFlags.restype = None
            
            self.dll.Everything_SetMatchCase.argtypes = [wintypes.BOOL]
            self.dll.Everything_SetMatchCase.restype = None
            
            self.dll.Everything_SetMatchWholeWord.argtypes = [wintypes.BOOL]
            self.dll.Everything_SetMatchWholeWord.restype = None
            
            self.dll.Everything_SetMatchPath.argtypes = [wintypes.BOOL]
            self.dll.Everything_SetMatchPath.restype = None
            
            self.dll.Everything_SetRegex.argtypes = [wintypes.BOOL]
            self.dll.Everything_SetRegex.restype = None
            
            # Funciones para obtener configuración actual
            self.dll.Everything_GetMatchCase.argtypes = []
            self.dll.Everything_GetMatchCase.restype = wintypes.BOOL
            
            self.dll.Everything_GetMatchWholeWord.argtypes = []
            self.dll.Everything_GetMatchWholeWord.restype = wintypes.BOOL
            
            self.dll.Everything_GetMatchPath.argtypes = []
            self.dll.Everything_GetMatchPath.restype = wintypes.BOOL
            
            self.dll.Everything_GetRegex.argtypes = []
            self.dll.Everything_GetRegex.restype = wintypes.BOOL
            
            self.dll.Everything_GetSort.argtypes = []
            self.dll.Everything_GetSort.restype = wintypes.DWORD
            
            # Funciones para filtros nativos de Everything
            try:
                self.dll.Everything_SetFilter.argtypes = [wintypes.DWORD]
                self.dll.Everything_SetFilter.restype = None
                
                self.dll.Everything_GetFilter.argtypes = []
                self.dll.Everything_GetFilter.restype = wintypes.DWORD
                
                print("EverythingAPI: Funciones de filtro nativo cargadas")
            except AttributeError:
                print("EverythingAPI: Funciones de filtro nativo no disponibles en esta versión")
            
            return True
            
        except OSError as e:
            if "cannot find" in str(e).lower() or "dependencies" in str(e).lower():
                print(f"EverythingAPI: Error de dependencias de DLL: {e}")
                print("EverythingAPI: Asegúrate de que Everything esté instalado correctamente")
            else:
                print(f"EverythingAPI: Error del sistema cargando DLL: {e}")
            self.dll = None
            return False
        except Exception as e:
            print(f"EverythingAPI: Error inesperado cargando DLL: {e}")
            self.dll = None
            return False
    
    def is_everything_available(self):
        """Verifica si Everything está disponible"""
        if self.dll is None:
            return False
        
        # Asegurar que Everything esté ejecutándose
        self._ensure_everything_running()
        
        # Probar una búsqueda simple para verificar que funciona
        try:
            self.dll.Everything_SetSearchW("")
            result = self.dll.Everything_QueryW(True)  # True = wait for results
            return result != 0
        except Exception as e:
            print(f"EverythingAPI: Error verificando disponibilidad: {e}")
            return False
    
    def search(self, query, max_results=100):
        """Realiza una búsqueda usando Everything DLL o PowerShell como fallback"""
        if self.dll is None:
            print("EverythingAPI: DLL no disponible, usando fallback de PowerShell")
            return self._search_with_powershell(query, max_results)
            
        try:
            # Asegurar que Everything esté ejecutándose
            self._ensure_everything_running()
            
            # Configurar parámetros de búsqueda
            self.dll.Everything_SetMax(max_results)
            
            # Aplicar configuración desde Everything.ini para coincidir exactamente
            self._apply_everything_ini_settings()
            
            # Configurar flags de solicitud para obtener información completa
            # EVERYTHING_REQUEST_FILE_NAME | EVERYTHING_REQUEST_PATH | EVERYTHING_REQUEST_FULL_PATH_AND_FILE_NAME
            request_flags = 0x00000001 | 0x00000002 | 0x00000004
            self.dll.Everything_SetRequestFlags(request_flags)
            
            # Establecer la consulta de búsqueda
            self.dll.Everything_SetSearchW(query)
            
            print(f"EverythingAPI: Ejecutando búsqueda: '{query}'")
            
            # Mostrar configuración actual (solo en modo debug)
            # self.print_current_settings()
            
            # Ejecutar la búsqueda
            if not self.dll.Everything_QueryW(True):  # True = wait for results
                error = self.dll.Everything_GetLastError()
                print(f"EverythingAPI: Error en búsqueda, código: {error}")
                return []
            
            # Obtener el número de resultados
            num_results = self.dll.Everything_GetNumResults()
            print(f"EverythingAPI: Encontrados {num_results} resultados")
            
            results = []
            
            # Procesar cada resultado
            for i in range(min(num_results, max_results)):
                try:
                    # Obtener el nombre del archivo
                    filename_ptr = self.dll.Everything_GetResultFileNameW(i)
                    if filename_ptr:
                        filename = ctypes.wstring_at(filename_ptr)
                    else:
                        filename = ""
                    
                    # Obtener la ruta completa
                    buffer_size = 260  # MAX_PATH
                    buffer = ctypes.create_unicode_buffer(buffer_size)
                    
                    actual_size = self.dll.Everything_GetResultFullPathNameW(i, buffer, buffer_size)
                    
                    if actual_size > buffer_size:
                        # El buffer era muy pequeño, crear uno más grande
                        buffer = ctypes.create_unicode_buffer(actual_size)
                        self.dll.Everything_GetResultFullPathNameW(i, buffer, actual_size)
                    
                    full_path = buffer.value if buffer.value else ""
                    
                    # Determinar si es archivo o carpeta
                    is_file = bool(self.dll.Everything_IsFileResult(i))
                    is_folder = bool(self.dll.Everything_IsFolderResult(i))
                    
                    # Si no se pudo determinar, usar el nombre
                    if not is_file and not is_folder:
                        is_folder = not ('.' in filename and len(filename.split('.')[-1]) <= 4)
                        is_file = not is_folder
                    
                    # Crear el resultado
                    result_item = {
                        'path': full_path,
                        'name': filename,
                        'is_file': is_file,
                        'is_folder': is_folder,
                        'full_path': full_path
                    }
                    
                    results.append(result_item)
                    
                except Exception as e:
                    print(f"EverythingAPI: Error procesando resultado {i}: {e}")
                    continue
            
            print(f"EverythingAPI: Procesados {len(results)} resultados")
            return results
            
        except Exception as e:
            print(f"EverythingAPI: Error en búsqueda: {e}")
            return []
    
    def _ensure_everything_running(self):
        """Asegura que Everything esté ejecutándose"""
        try:
            # Verificar si Everything ya está ejecutándose
            import psutil
            everything_running = False
            
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and 'everything' in proc.info['name'].lower():
                    everything_running = True
                    break
            
            if not everything_running and self.everything_exe and os.path.exists(self.everything_exe):
                print("EverythingAPI: Iniciando Everything...")
                # Configurar para ocultar la consola
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                
                # Iniciar Everything en segundo plano
                subprocess.Popen([self.everything_exe, "-startup"], 
                               startupinfo=startupinfo,
                               creationflags=subprocess.CREATE_NO_WINDOW)
                # Esperar un poco para que se inicie
                time.sleep(2)
                
        except ImportError:
            # Si psutil no está disponible, intentar iniciar Everything de todos modos
            try:
                if self.everything_exe and os.path.exists(self.everything_exe):
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    
                    subprocess.Popen([self.everything_exe, "-startup"], 
                                   startupinfo=startupinfo,
                                   creationflags=subprocess.CREATE_NO_WINDOW)
                    time.sleep(2)
            except Exception as e:
                print(f"EverythingAPI: Error iniciando Everything: {e}")
        except Exception as e:
            print(f"EverythingAPI: Error verificando/iniciando Everything: {e}")
    
    def is_everything_installed(self):
        """Verifica si Everything está instalado en el sistema"""
        return self.everything_exe is not None and os.path.exists(self.everything_exe)
    
    def get_current_settings(self):
        """Obtiene la configuración actual de Everything"""
        if self.dll is None:
            return None
        
        try:
            settings = {
                'match_case': bool(self.dll.Everything_GetMatchCase()),
                'match_whole_word': bool(self.dll.Everything_GetMatchWholeWord()),
                'match_path': bool(self.dll.Everything_GetMatchPath()),
                'regex': bool(self.dll.Everything_GetRegex()),
                'sort': self.dll.Everything_GetSort()
            }
            return settings
        except Exception as e:
            print(f"EverythingAPI: Error obteniendo configuración: {e}")
            return None
    
    def print_current_settings(self):
        """Imprime la configuración actual de Everything"""
        settings = self.get_current_settings()
        if settings:
            print("EverythingAPI: Configuración actual:")
            print(f"  - Sensible a mayúsculas: {settings['match_case']}")
            print(f"  - Palabras completas: {settings['match_whole_word']}")
            print(f"  - Buscar en ruta: {settings['match_path']}")
            print(f"  - Regex: {settings['regex']}")
            print(f"  - Ordenamiento: {settings['sort']}")
        else:
            print("EverythingAPI: No se pudo obtener la configuración")
    
    def _read_everything_ini_settings(self):
        """Lee la configuración desde Everything.ini"""
        settings = {
            'match_case': False,
            'match_whole_word': False,
            'match_path': False,
            'regex': False,
            'sort': 'Name',
            'sort_ascending': True
        }
        
        # Buscar Everything.ini en múltiples ubicaciones
        ini_path = None
        for path in self.everything_ini_paths:
            if os.path.exists(path):
                ini_path = path
                break
        
        if ini_path is None:
            print("EverythingAPI: Everything.ini no encontrado en ninguna ubicación conocida")
            return settings
        
        try:
            print(f"EverythingAPI: Leyendo configuración desde {ini_path}")
            with open(ini_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        if key == 'match_case':
                            settings['match_case'] = value == '1'
                        elif key == 'match_whole_word':
                            settings['match_whole_word'] = value == '1'
                        elif key == 'match_path':
                            settings['match_path'] = value == '1'
                        elif key == 'match_regex':
                            settings['regex'] = value == '1'
                        elif key == 'sort':
                            settings['sort'] = value
                        elif key == 'sort_ascending':
                            settings['sort_ascending'] = value == '1'
            
            print(f"EverythingAPI: Configuración leída desde Everything.ini:")
            print(f"  - Match case: {settings['match_case']}")
            print(f"  - Match whole word: {settings['match_whole_word']}")
            print(f"  - Match path: {settings['match_path']}")
            print(f"  - Regex: {settings['regex']}")
            print(f"  - Sort: {settings['sort']} ({'asc' if settings['sort_ascending'] else 'desc'})")
            
        except Exception as e:
            print(f"EverythingAPI: Error leyendo Everything.ini: {e}")
        
        return settings
    
    def _apply_everything_ini_settings(self):
        """Aplica la configuración desde Everything.ini"""
        if self.dll is None:
            return
        
        settings = self._read_everything_ini_settings()
        
        try:
            # Aplicar configuración de búsqueda
            self.dll.Everything_SetMatchCase(settings['match_case'])
            self.dll.Everything_SetMatchWholeWord(settings['match_whole_word'])
            self.dll.Everything_SetMatchPath(settings['match_path'])
            self.dll.Everything_SetRegex(settings['regex'])
            
            # Aplicar ordenamiento
            sort_value = 1  # Por defecto: EVERYTHING_SORT_NAME_ASCENDING
            
            if settings['sort'] == 'Name':
                sort_value = 1 if settings['sort_ascending'] else 2
            elif settings['sort'] == 'Path':
                sort_value = 3 if settings['sort_ascending'] else 4
            elif settings['sort'] == 'Size':
                sort_value = 5 if settings['sort_ascending'] else 6
            elif settings['sort'] == 'Extension':
                sort_value = 7 if settings['sort_ascending'] else 8
            elif settings['sort'] == 'Type':
                sort_value = 9 if settings['sort_ascending'] else 10
            elif settings['sort'] == 'Date Modified':
                sort_value = 11 if settings['sort_ascending'] else 12
            elif settings['sort'] == 'Date Created':
                sort_value = 13 if settings['sort_ascending'] else 14
            elif settings['sort'] == 'Attributes':
                sort_value = 15 if settings['sort_ascending'] else 16
            elif settings['sort'] == 'Date Recently Changed':
                sort_value = 17 if settings['sort_ascending'] else 18
            elif settings['sort'] == 'Date Accessed':
                sort_value = 19 if settings['sort_ascending'] else 20
            elif settings['sort'] == 'Run Count':
                sort_value = 21 if settings['sort_ascending'] else 22
            elif settings['sort'] == 'Date Run':
                sort_value = 23 if settings['sort_ascending'] else 24
            
            self.dll.Everything_SetSort(sort_value)
            
            print(f"EverythingAPI: Configuración aplicada (sort value: {sort_value})")
            
        except Exception as e:
            print(f"EverythingAPI: Error aplicando configuración: {e}")
    
    def _search_with_powershell(self, query, max_results=100):
        """Búsqueda de fallback usando PowerShell y Everything CLI"""
        try:
            print(f"EverythingAPI: Ejecutando búsqueda con PowerShell: '{query}'")
            
            # Escapar la consulta para PowerShell
            escaped_query = query.replace('"', '""').replace("'", "''")
            
            # Script de PowerShell para usar Everything CLI
            powershell_script = f'''
            try {{
                # Verificar si Everything CLI está disponible
                $everythingCli = "C:\\Program Files\\Everything\\es.exe"
                if (-not (Test-Path $everythingCli)) {{
                    $everythingCli = "C:\\Program Files (x86)\\Everything\\es.exe"
                }}
                
                if (Test-Path $everythingCli) {{
                    # Usar Everything CLI
                    $results = & "$everythingCli" -max {max_results} "{escaped_query}"
                    foreach ($result in $results) {{
                        if ($result -and $result.Trim() -ne "") {{
                            $isFile = Test-Path -Path $result -PathType Leaf
                            $isFolder = Test-Path -Path $result -PathType Container
                            $name = Split-Path -Leaf $result
                            
                            Write-Output "RESULT:$result|$name|$isFile|$isFolder"
                        }}
                    }}
                }} else {{
                    # Fallback: búsqueda básica con Get-ChildItem
                    Write-Output "FALLBACK: Everything CLI no encontrado, usando búsqueda básica"
                    
                    # Búsqueda básica en ubicaciones comunes
                    $searchPaths = @("C:\\Users", "C:\\Program Files", "C:\\Program Files (x86)")
                    $count = 0
                    
                    foreach ($path in $searchPaths) {{
                        if ($count -ge {max_results}) {{ break }}
                        
                        try {{
                            $items = Get-ChildItem -Path $path -Recurse -Force -ErrorAction SilentlyContinue | 
                                     Where-Object {{ $_.Name -like "*{escaped_query}*" }} | 
                                     Select-Object -First ({max_results} - $count)
                            
                            foreach ($item in $items) {{
                                $isFile = -not $item.PSIsContainer
                                $isFolder = $item.PSIsContainer
                                
                                Write-Output "RESULT:$($item.FullName)|$($item.Name)|$isFile|$isFolder"
                                $count++
                                
                                if ($count -ge {max_results}) {{ break }}
                            }}
                        }} catch {{
                            continue
                        }}
                    }}
                }}
            }} catch {{
                Write-Output "ERROR: $($_.Exception.Message)"
            }}
            '''
            
            # Configurar para ocultar la consola
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # Ejecutar PowerShell
            result = subprocess.run(
                ["powershell", "-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", powershell_script],
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=30,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode != 0:
                print(f"EverythingAPI: Error en PowerShell: {result.stderr}")
                return []
            
            # Procesar resultados
            results = []
            for line in result.stdout.strip().split('\n'):
                if line.startswith("RESULT:"):
                    try:
                        parts = line[7:].split('|')  # Remover "RESULT:"
                        if len(parts) >= 4:
                            full_path, name, is_file_str, is_folder_str = parts[:4]
                            
                            result_item = {
                                'path': full_path,
                                'name': name,
                                'is_file': is_file_str.lower() == 'true',
                                'is_folder': is_folder_str.lower() == 'true',
                                'full_path': full_path
                            }
                            results.append(result_item)
                    except Exception as e:
                        print(f"EverythingAPI: Error procesando línea: {line}, error: {e}")
                        continue
                elif line.startswith("FALLBACK:"):
                    print(f"EverythingAPI: {line}")
                elif line.startswith("ERROR:"):
                    print(f"EverythingAPI: {line}")
            
            print(f"EverythingAPI: PowerShell encontró {len(results)} resultados")
            return results
            
        except subprocess.TimeoutExpired:
            print("EverythingAPI: Timeout en búsqueda con PowerShell")
            return []
        except Exception as e:
            print(f"EverythingAPI: Error en búsqueda con PowerShell: {e}")
            return []

# Señales para el hilo de búsqueda
class SearchSignals(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

# Trabajador para búsquedas en segundo plano
class SearchWorker(QRunnable):
    def __init__(self, query, everything_api, max_results=200):
        super().__init__()
        self.query = query
        self.everything_api = everything_api
        self.max_results = max_results
        self.signals = SearchSignals()
        
    def run(self):
        try:
            results = self.everything_api.search(self.query, self.max_results)
            self.signals.finished.emit(results)
        except Exception as e:
            self.signals.error.emit(f"Error en búsqueda: {str(e)}")

# Delegado para la columna de tamaño con fondo gris redondeado
class ZfindSizeDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.radius = 8  # Radio para el rectángulo del tamaño
        self.bg_color = QColor(60, 60, 60, 180)  # Color gris oscuro semitransparente
        
        # Colores específicos para diferentes tipos
        self.folder_color = QColor(70, 130, 180, 180)  # Azul acero para carpetas
        self.small_file_color = QColor(60, 179, 113, 180)  # Verde medio para archivos pequeños
        self.medium_file_color = QColor(255, 165, 0, 180)  # Naranja para archivos medianos
        self.large_file_color = QColor(205, 92, 92, 180)  # Rojo para archivos grandes
        
    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index):
        if index.column() == 1:  # Solo para la columna "Tamaño"
            painter.save()
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            size_text = index.data(Qt.ItemDataRole.DisplayRole)
            
            # Calcular el ancho exacto del texto del tamaño
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.horizontalAdvance(size_text) + 20  # Añadir padding
            
            # Padding para el fondo
            padding_vertical = 2
            
            # Calcular el rectángulo para el fondo
            bg_rect = QRect(
                option.rect.x() + (option.rect.width() - text_width) // 2,  # Centrar horizontalmente
                option.rect.y() + padding_vertical,
                text_width,
                option.rect.height() - (2 * padding_vertical)
            )
            
            # Asegurar que el radio no exceda la mitad de la altura del rectángulo
            current_radius = min(self.radius, bg_rect.height() // 2)
            
            # Dibujar el fondo redondeado
            painter.setBrush(QBrush(self.bg_color))
            painter.setPen(QPen(Qt.PenStyle.NoPen))
            painter.drawRoundedRect(bg_rect, current_radius, current_radius)
            
            # Configurar la fuente para el texto
            font = painter.font()
            font.setPointSize(8)  # Tamaño de fuente ligeramente reducido
            font.setWeight(QFont.Weight.Medium)  # Peso medio para mejor legibilidad
            painter.setFont(font)
            
            # Dibujar el texto centrado en el rectángulo
            painter.setPen(QPen(Qt.GlobalColor.white))
            painter.drawText(bg_rect, Qt.AlignmentFlag.AlignCenter, size_text)
            
            painter.restore()
        else:
            super().paint(painter, option, index)

# Delegado para la columna de extensión con colores
class ZfindExtensionDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.radius = 6  # Radio para el rectángulo de la extensión
        
        # Colores para diferentes extensiones
        self.extension_colors = {
            # --- Carpetas ---
            'DIR': QColor(0, 120, 215, 180),   # Azul para carpetas
            
            # --- Extensiones de Video ---
            'mp4': QColor(64, 152, 219, 180),  # Azul brillante
            'avi': QColor(76, 175, 80, 180),   # Verde Material
            'mkv': QColor(103, 58, 183, 180),  # Púrpura Profundo
            'mov': QColor(255, 152, 0, 180),   # Naranja Ámbar
            'mpg': QColor(229, 57, 53, 180),   # Rojo Vibrante
            'mpeg': QColor(229, 57, 53, 180),  # Rojo Vibrante
            'wmv': QColor(96, 125, 139, 180),  # Azul Grisáceo
            'flv': QColor(255, 235, 59, 180),  # Amarillo Material
            'webm': QColor(0, 150, 136, 180),  # Teal
            '3gp': QColor(123, 31, 162, 180),  # Púrpura Intenso
            'ts': QColor(0, 121, 107, 180),    # Verde Oscuro Teal
            'vob': QColor(244, 81, 30, 180),   # Naranja Oscuro
            'rmvb': QColor(84, 110, 122, 180), # Azul Grisáceo Oscuro
            'divx': QColor(67, 160, 71, 180),  # Verde Más Claro
            'ogv': QColor(211, 47, 47, 180),   # Rojo Fuerte
            
            # --- Extensiones de Audio ---
            'mp3': QColor(233, 30, 99, 180),   # Rosa Fuerte
            'wav': QColor(33, 150, 243, 180),  # Azul Brillante
            'flac': QColor(255, 193, 7, 180),  # Amarillo Sol
            'aac': QColor(255, 87, 34, 180),   # Naranja Rojo
            'ogg': QColor(139, 195, 74, 180),  # Verde Lima
            
            # --- Extensiones de Imagen ---
            'jpg': QColor(255, 160, 0, 180),   # Naranja Más Suave
            'jpeg': QColor(255, 160, 0, 180),  # Naranja Más Suave
            'png': QColor(255, 112, 67, 180),  # Naranja Salmón
            'gif': QColor(158, 158, 158, 180), # Gris Neutro
            'bmp': QColor(117, 117, 117, 180), # Gris Oscuro
            'tiff': QColor(48, 63, 159, 180),  # Azul Índigo
            'ico': QColor(158, 158, 158, 180), # Gris Neutro
            
            # --- Extensiones de Documentos ---
            'txt': QColor(121, 134, 139, 180), # Gris Azulado Suave
            'pdf': QColor(198, 40, 40, 180),   # Rojo Material Oscuro
            'doc': QColor(25, 118, 210, 180),  # Azul Medio
            'docx': QColor(25, 118, 210, 180), # Azul Medio
            'xls': QColor(56, 142, 60, 180),   # Verde Oscuro
            'xlsx': QColor(56, 142, 60, 180),  # Verde Oscuro
            'ppt': QColor(253, 216, 53, 180),  # Amarillo Brillante
            'pptx': QColor(253, 216, 53, 180), # Amarillo Brillante
            'rtf': QColor(100, 181, 246, 180), # Azul Cielo Pastel
            
            # --- Extensiones Comprimidas ---
            'zip': QColor(117, 117, 117, 180), # Gris Medio
            'rar': QColor(117, 117, 117, 180), # Gris Medio
            '7z': QColor(117, 117, 117, 180),  # Gris Medio
            'tar': QColor(97, 97, 97, 180),    # Gris Oscuro
            'gz': QColor(66, 66, 66, 180),     # Gris Muy Oscuro
            
            # --- Extensiones de Ejecutables/Sistema ---
            'exe': QColor(103, 58, 183, 180),  # Púrpura Intenso
            'dll': QColor(38, 50, 56, 180),    # Azul Gris Oscuro
            'sys': QColor(96, 125, 139, 180),  # Azul Grisáceo
            'msi': QColor(123, 31, 162, 180),  # Púrpura Más Intenso
            'iso': QColor(30, 136, 229, 180),  # Azul Material
            
            # --- Extensiones de Código/Desarrollo ---
            'py': QColor(38, 50, 56, 180),     # Azul Gris Oscuro para Python
            'js': QColor(255, 235, 59, 180),   # Amarillo vibrante
            'html': QColor(255, 87, 34, 180),  # Naranja Fuerte
            'css': QColor(30, 136, 229, 180),  # Azul Material Brillante
            'xml': QColor(121, 134, 139, 180), # Gris Azulado Suave
            'json': QColor(38, 50, 56, 180),   # Azul Gris Oscuro para JSON
            'php': QColor(123, 31, 162, 180),  # Púrpura Oscuro
            'java': QColor(244, 81, 30, 180),  # Naranja Quemado
            'c': QColor(66, 66, 66, 180),      # Gris Oscuro
            'cpp': QColor(66, 66, 66, 180),    # Gris Oscuro
            'h': QColor(66, 66, 66, 180),      # Gris Oscuro
            'hpp': QColor(66, 66, 66, 180),    # Gris Oscuro
            'cs': QColor(103, 58, 183, 180),   # Púrpura Brillante
            'ts': QColor(0, 150, 136, 180),    # Teal
            'go': QColor(0, 172, 193, 180),    # Azul Cian
            
            # --- Otras ---
            'font': QColor(207, 216, 220, 180),# Gris Azulado Claro
            'log': QColor(211, 47, 47, 180),   # Rojo Más Fuerte
            'dat': QColor(55, 71, 79, 180),    # Azul Gris Oscuro
            'tmp': QColor(97, 97, 97, 180),    # Gris Medio Oscuro
            'ini': QColor(97, 97, 97, 180),    # Gris Medio Oscuro
            'conf': QColor(97, 97, 97, 180),   # Gris Medio Oscuro
            'bak': QColor(158, 158, 158, 180), # Gris Neutro
        }
        
        self.default_color = QColor(60, 70, 80, 180)  # Color por defecto (gris pizarra oscuro)
        self.folder_color = QColor(0, 120, 215, 180)  # Color azul para carpetas (DIR)
        
    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index):
        if index.column() == 0:  # Solo para la columna "Ext"
            painter.save()
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            ext_display = index.data(Qt.ItemDataRole.DisplayRole)
            
            # Verificar si es una carpeta (DIR)
            is_folder = (ext_display == "DIR")
            
            # Seleccionar color según el tipo (carpeta o archivo)
            if is_folder:
                color = self.folder_color
            else:
                color = self.extension_colors.get(ext_display, self.default_color)
            
            # Calcular el ancho exacto del texto de la extensión
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.horizontalAdvance(ext_display)
            
            # Padding para el fondo
            padding_horizontal = 2 # Reduced horizontal padding
            padding_vertical = 2
            
            # Nuevo: Ancho fijo para el rectángulo de fondo de la extensión
            fixed_ext_bg_width = 30 # Un ancho fijo que se vea bien para todas las extensiones
            
            # Calcular el nuevo ancho y posición X para centrar el rectángulo alrededor del texto
            bg_width = fixed_ext_bg_width
            bg_x = option.rect.x() + (option.rect.width() - bg_width) // 2
            bg_rect = QRect(
                bg_x,
                option.rect.y() + padding_vertical,
                bg_width,
                option.rect.height() - (2 * padding_vertical)
            )
            
            # Asegurar que el radio no exceda la mitad de la altura del rectángulo
            current_radius = min(self.radius, bg_rect.height() // 2)
            
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(Qt.PenStyle.NoPen))
            painter.drawRoundedRect(bg_rect, current_radius, current_radius)
            
            font = painter.font()
            font.setPointSize(7)  # Reduced font size for extension text
            font.setWeight(QFont.Weight.Bold) # Make the text bold
            painter.setFont(font)
            
            # Dibujar la sombra del texto (negra y ligeramente desplazada)
            shadow_offset_x = 1
            shadow_offset_y = 1
            shadow_color = QColor(0, 0, 0, 150) # Negro con opacidad para la sombra
            painter.setPen(QPen(shadow_color))
            shadow_rect = QRect(option.rect.x() + shadow_offset_x, 
                                option.rect.y() + shadow_offset_y, 
                                option.rect.width(), 
                                option.rect.height())
            painter.drawText(shadow_rect, Qt.AlignmentFlag.AlignCenter, ext_display)
            
            # Dibujar el texto principal (blanco)
            painter.setPen(QPen(Qt.GlobalColor.white))
            painter.drawText(option.rect, Qt.AlignmentFlag.AlignCenter, ext_display)
            
            painter.restore()
        else:
            super().paint(painter, option, index)

class EverythingNotFoundDialog(QFrame):
    """Diálogo cuando Everything.exe no está disponible"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Everything no encontrado")
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(450, 300)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Frame principal
        self.main_frame = QFrame()
        self.main_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(45, 45, 44, 240);
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 30);
            }
        """)
        
        # Sombra
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 100))
        self.main_frame.setGraphicsEffect(shadow)
        
        main_layout.addWidget(self.main_frame)
        
        # Layout del frame
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(20, 20, 20, 20)
        frame_layout.setSpacing(15)
        
        # Título
        title_label = QLabel("⚠️ Everything no encontrado")
        title_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(title_label)
        
        # Descripción
        desc_label = QLabel(
            "ZFind requiere Everything.exe para funcionar.\n\n"
            "Asegúrate de que Everything.exe esté en la carpeta\n"
            "'Everything' del directorio de ZFind."
        )
        desc_label.setStyleSheet("color: #ccc; font-size: 12px; line-height: 1.4;")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setWordWrap(True)
        frame_layout.addWidget(desc_label)
        
        # Botones
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # Botón cerrar
        close_button = QPushButton("❌ Cerrar")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #666;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #777;
            }
        """)
        close_button.clicked.connect(self.hide_to_tray)
        buttons_layout.addWidget(close_button)
        
        frame_layout.addLayout(buttons_layout)
        
    def showEvent(self, event):
        """Se ejecuta cuando se muestra la ventana"""
        super().showEvent(event)
        
        # Aplicar efectos visuales
        if hasattr(self, 'winId'):
            hwnd = int(self.winId())
            apply_acrylic_and_rounded(hwnd)
            
        # Centrar en la pantalla
        self.center_on_screen()
        
    def center_on_screen(self):
        """Centra la ventana en la pantalla"""
        screen = QApplication.primaryScreen().geometry()
        window_geometry = self.geometry()
        
        x = (screen.width() - window_geometry.width()) // 2
        y = (screen.height() - window_geometry.height()) // 2
        
        self.move(x, y)

class DraggableTreeWidget(QTreeWidget):
    """QTreeWidget personalizado que permite arrastrar elementos"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setAcceptDrops(False)  # No aceptamos soltar elementos aquí
        self.setDragDropMode(QAbstractItemView.DragDropMode.DragOnly)  # Solo permitimos arrastrar
        self.setDefaultDropAction(Qt.DropAction.CopyAction)  # Acción predeterminada: copiar
        
    def startDrag(self, supportedActions):
        """Inicia la operación de arrastre con los elementos seleccionados"""
        selected_items = self.selectedItems()
        if not selected_items:
            return
            
        # Crear un objeto QMimeData para transferir los datos
        mime_data = QMimeData()
        
        # Lista de URLs para los archivos seleccionados
        urls = []
        
        # Calcular el tamaño total de los archivos seleccionados
        total_size = 0
        
        # Añadir cada archivo seleccionado a la lista de URLs
        for item in selected_items:
            path = item.text(3)  # La ruta está en la columna 3
            if not path:
                continue
                
            # Verificar si el archivo existe
            if not os.path.exists(path):
                print(f"Error: La ruta no existe: {path}")
                continue
                
            # Crear la URL para el arrastre
            url = QUrl.fromLocalFile(path)
            urls.append(url)
            
            # Obtener el tamaño del archivo si es un archivo
            if os.path.isfile(path):
                try:
                    total_size += os.path.getsize(path)
                except Exception as e:
                    print(f"Error al obtener tamaño de {path}: {e}")
        
        # Si tenemos URLs, las añadimos al objeto QMimeData
        if urls:
            mime_data.setUrls(urls)
            
            # Añadir formatos adicionales para mejorar la compatibilidad
            # Esto ayuda a que más aplicaciones acepten el arrastre
            if len(urls) == 1:
                # Para un solo archivo, añadir su ruta como texto
                mime_data.setText(urls[0].toLocalFile())
            else:
                # Para múltiples archivos, añadir una lista de rutas
                text_paths = "\n".join([url.toLocalFile() for url in urls])
                mime_data.setText(text_paths)
            
            # Iniciar la operación de arrastre
            drag = QDrag(self)
            drag.setMimeData(mime_data)
            
            # Crear un pixmap personalizado para el cursor de arrastre
            num_files = len(urls)
            
            # Crear un pixmap para el cursor de arrastre
            if num_files == 1:
                # Para un solo archivo, mostrar su nombre y tamaño
                item = selected_items[0]
                file_name = item.text(2)  # Nombre en la columna 2
                file_size = format_size(total_size)  # Formatear el tamaño
                
                # Crear un pixmap con el nombre del archivo y su tamaño
                pixmap = QPixmap(250, 50)  # Aumentamos un poco el tamaño
                pixmap.fill(Qt.GlobalColor.transparent)
                
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.RenderHint.Antialiasing)
                
                # Dibujar un fondo redondeado menos transparente
                painter.setBrush(QColor(50, 50, 50, 230))  # Más opaco (230 en lugar de 200)
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRoundedRect(0, 0, pixmap.width(), pixmap.height(), 15, 15)  # Más redondeado (15 en lugar de 10)
                
                # Dibujar un pequeño indicador en la parte superior izquierda para mostrar el punto de arrastre
                painter.setBrush(QColor(0, 120, 215, 255))  # Azul intenso completamente opaco
                painter.drawEllipse(5, 5, 6, 6)  # Pequeño círculo en la parte superior izquierda
                
                # Dibujar el texto del nombre del archivo
                painter.setPen(QColor(255, 255, 255))  # Blanco puro
                font = QFont("Segoe UI", 9)
                font.setBold(True)  # Texto en negrita
                painter.setFont(font)
                
                # Crear un rectángulo para el nombre del archivo (parte superior)
                name_rect = QRect(15, 5, pixmap.width() - 20, 20)
                painter.drawText(name_rect, Qt.AlignmentFlag.AlignCenter, f"📄 {file_name}")
                
                # Crear un rectángulo para el tamaño (parte inferior)
                size_rect = QRect(15, 25, pixmap.width() - 20, 20)
                font = QFont("Segoe UI", 8)
                font.setBold(True)  # Texto en negrita
                painter.setFont(font)
                painter.setPen(QColor(64, 196, 255))  # Azul claro moderno
                painter.drawText(size_rect, Qt.AlignmentFlag.AlignCenter, f"Tamaño: {file_size}")
                
                painter.end()
                
                # Establecer el pixmap como cursor con el punto de arrastre en la parte superior izquierda
                drag.setPixmap(pixmap)
                # Colocar el punto de arrastre en la parte superior izquierda del pixmap
                drag.setHotSpot(QPoint(5, 5))
            else:
                # Para múltiples archivos, mostrar el número y el tamaño total
                file_size = format_size(total_size)  # Formatear el tamaño total
                
                pixmap = QPixmap(180, 50)  # Aumentamos el tamaño para mostrar más información
                pixmap.fill(Qt.GlobalColor.transparent)
                
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.RenderHint.Antialiasing)
                
                # Dibujar un fondo redondeado menos transparente
                painter.setBrush(QColor(50, 50, 50, 230))  # Más opaco (230 en lugar de 200)
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRoundedRect(0, 0, pixmap.width(), pixmap.height(), 15, 15)  # Más redondeado (15 en lugar de 10)
                
                # Dibujar un pequeño indicador en la parte superior izquierda para mostrar el punto de arrastre
                painter.setBrush(QColor(0, 120, 215, 255))  # Azul intenso completamente opaco
                painter.drawEllipse(5, 5, 6, 6)  # Pequeño círculo en la parte superior izquierda
                
                # Dibujar el texto del número de archivos (parte superior)
                painter.setPen(QColor(255, 255, 255))  # Blanco puro
                font = QFont("Segoe UI", 9)
                font.setBold(True)  # Texto en negrita
                painter.setFont(font)
                
                # Crear un rectángulo para el número de archivos (parte superior)
                files_rect = QRect(15, 5, pixmap.width() - 20, 20)
                painter.drawText(files_rect, Qt.AlignmentFlag.AlignCenter, f"📄 {num_files} archivos")
                
                # Crear un rectángulo para el tamaño total (parte inferior)
                size_rect = QRect(15, 25, pixmap.width() - 20, 20)
                font = QFont("Segoe UI", 8)
                font.setBold(True)  # Texto en negrita
                painter.setFont(font)
                painter.setPen(QColor(64, 196, 255))  # Azul claro moderno
                painter.drawText(size_rect, Qt.AlignmentFlag.AlignCenter, f"Total: {file_size}")
                
                painter.end()
                
                # Establecer el pixmap como cursor con el punto de arrastre en la parte superior izquierda
                drag.setPixmap(pixmap)
                # Colocar el punto de arrastre en la parte superior izquierda del pixmap
                drag.setHotSpot(QPoint(5, 5))
                
            # Ejecutar la operación de arrastre con acciones explícitas
            # Permitir copiar y mover archivos
            actions = Qt.DropAction.CopyAction | Qt.DropAction.MoveAction | Qt.DropAction.LinkAction
            result = drag.exec(actions)
            
            print(f"Arrastre completado con resultado: {result}")

def format_size(size_bytes):
    """Formatea un tamaño en bytes a una representación legible"""
    if size_bytes < 0:
        return "0 B"
    
    # Definir unidades
    units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
    
    # Calcular la unidad adecuada
    i = 0
    while size_bytes >= 1024 and i < len(units) - 1:
        size_bytes /= 1024.0
        i += 1
    
    # Formatear con 2 decimales si es necesario
    if i > 0:
        return f"{size_bytes:.2f} {units[i]}"
    else:
        return f"{size_bytes} {units[i]}"

class ZFindWindow(QMainWindow):
    """Ventana principal de ZFind con estilo mejorado"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        print("ZFindWindow: Inicializando...")
        
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        self.hwnd = self.winId().__int__()
        try:
            apply_acrylic_and_rounded(self.hwnd)
            print("ZFindWindow: Efecto acrílico aplicado.")
        except Exception as e:
            print(f"ZFindWindow: Error al aplicar efecto acrílico: {e}")
        
        self.setWindowTitle("ZFind - Buscador")
        self.setGeometry(100, 100, 800, 600)
        
        self.main_widget = QWidget(self)
        self.main_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(30, 30, 30, 30);
                border-radius: 0px;
            }
        """)
        self.setCentralWidget(self.main_widget)
        
        self.window_resizer = WindowResizer(self)
        
        self.layout = QVBoxLayout(self.main_widget)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(8)
        
        # Inicializar componentes
        self.create_title_bar()
        self.setup_results_tree()
        self.setup_status_bar()
        
        # Inicializar API de búsqueda
        try:
            print("ZFindWindow: Inicializando API de búsqueda...")
            self.everything_api = EverythingAPI()
            
            # Verificar si Everything está instalado y disponible
            if self.everything_api.is_everything_installed() and self.everything_api.is_everything_available():
                print("ZFindWindow: API de búsqueda inicializada correctamente")
                self.init_welcome_message()
            else:
                print("ZFindWindow: Everything no está disponible, mostrando mensaje de instalación")
                self.show_everything_not_found()
                
        except Exception as e:
            print(f"ZFindWindow: Error inicializando API de búsqueda: {e}")
            self.show_everything_not_found()
    
    def init_welcome_message(self):
        """Inicializa el mensaje de bienvenida en el árbol de resultados"""
        try:
            # Mostrar mensaje inicial en el árbol de resultados
            self.results_tree.clear()
            welcome_item = QTreeWidgetItem()
            welcome_item.setText(0, "")  # Columna Ext vacía
            welcome_item.setText(1, "")  # Columna Tamaño vacía
            welcome_item.setText(2, "Bienvenido a ZFind")  # Nombre
            welcome_item.setText(3, "Escribe para buscar con Everything")  # Ruta
            self.results_tree.addTopLevelItem(welcome_item)
            
            # Configurar búsqueda
            self.search_timer = QTimer(self)
            self.search_timer.setSingleShot(True)
            self.search_timer.timeout.connect(self._execute_search)
            
            self.max_results = 1000
            self.current_query = ""
            self.is_searching = False
            
            # Configurar ThreadPool para búsquedas
            self.thread_pool = QThreadPool()
            self.thread_pool.setMaxThreadCount(1)
            
        except Exception as e:
            print(f"ZFindWindow: Error al inicializar mensaje de bienvenida: {e}")
            self.show_everything_not_found()
            return
            
        # Configurar FileIconManager
        self.file_icon_manager = FileTypeIconManager()
        
        # Variables para estadísticas
        self.total_files = 0
        self.total_folders = 0
        self.total_size = 0
        
        print("ZFindWindow: Inicialización completada.")
        
    def show_everything_install_dialog(self):
        """Muestra el diálogo de instalación de Everything"""
        try:
            from EVERYTHING_INSTALLER import show_everything_install_dialog
            
            # Mostrar diálogo de instalación
            if show_everything_install_dialog(self):
                # Si se instaló correctamente, reinicializar API
                print("ZFindWindow: Everything instalado, reinicializando API...")
                self.everything_api = EverythingAPI()
                
                if self.everything_api.is_everything_available():
                    print("ZFindWindow: API reinicializada correctamente")
                    self.init_welcome_message()
                else:
                    print("ZFindWindow: Error reinicializando API")
                    self.show_everything_not_found()
            else:
                # Si se canceló la instalación, cerrar ZFind
                print("ZFindWindow: Instalación cancelada, cerrando ZFind")
                self.close()
                
        except Exception as e:
            print(f"ZFindWindow: Error mostrando diálogo de instalación: {e}")
            self.show_everything_not_found()
    
    def show_everything_not_found(self):
        """Muestra diálogo de Everything no encontrado"""
        dialog = EverythingNotFoundDialog(self)
        dialog.exec()
        
    def create_title_bar(self):
        """Crea la barra de título con botones de ventana, logo y barra de búsqueda."""
        title_bar = QWidget()
        title_bar.setFixedHeight(40)
        title_bar.setStyleSheet("""
            QWidget {
                background-color: transparent;
                border-radius: 0px;
            }
        """)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(10, 5, 10, 5)
        title_layout.setSpacing(10)
        
        # Logo
        self.zfind_icon_label = QLabel(self)
        self.zfind_icon_label.setPixmap(Icono_BUSCADOR(30).pixmap(30, 30))
        title_layout.addWidget(self.zfind_icon_label)
        
        # Campo de búsqueda
        self.search_input = QLineEdit(self)
        self.search_input.setPlaceholderText("Buscar archivos y carpetas...")
        self.search_input.setFixedWidth(300)
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: rgba(255, 255, 255, 30);
                border: 1px solid rgba(255, 255, 255, 50);
                border-radius: 8px;
                padding: 5px 10px;
                color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 1px solid rgba(0, 120, 215, 150);
            }
        """)
        self.search_input.textChanged.connect(self._on_search_text_changed)
        title_layout.addWidget(self.search_input)
        
        # Checkbox Optimize Zet
        from PyQt6.QtWidgets import QCheckBox
        self.optimize_zet_checkbox = QCheckBox("Optimize Zet", self)
        self.optimize_zet_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 11px;
                font-weight: bold;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 1px solid rgba(255, 255, 255, 100);
                background-color: rgba(255, 255, 255, 20);
            }
            QCheckBox::indicator:checked {
                background-color: rgba(0, 120, 215, 180);
                border: 1px solid rgba(0, 120, 215, 200);
            }
            QCheckBox::indicator:hover {
                border: 1px solid rgba(0, 120, 215, 150);
                background-color: rgba(255, 255, 255, 40);
            }
            QCheckBox::indicator:checked:hover {
                background-color: rgba(0, 120, 215, 200);
            }
        """)
        self.optimize_zet_checkbox.setToolTip("Filtrar solo archivos de video y audio")
        
        # Cargar estado del checkbox desde config
        self.load_optimize_zet_state()
        
        # Conectar cambio de estado para guardar automáticamente
        self.optimize_zet_checkbox.stateChanged.connect(self.save_optimize_zet_state)
        
        title_layout.addWidget(self.optimize_zet_checkbox)
        
        title_layout.addStretch()
        
        # Botones de control
        control_buttons_layout = QHBoxLayout()
        control_buttons_layout.setContentsMargins(0, 0, 0, 0)
        control_buttons_layout.setSpacing(2)
        
        self.minimize_button = CustomMinimizeButton()
        self.minimize_button.clicked.connect(self.showMinimized)
        control_buttons_layout.addWidget(self.minimize_button)
        
        self.maximize_button = CustomMaximizeButton()
        self.maximize_button.clicked.connect(self.toggle_maximize)
        control_buttons_layout.addWidget(self.maximize_button)
        
        self.close_button = CustomCloseButton()
        self.close_button.clicked.connect(self.hide_to_tray)  # Cambiar a método personalizado
        control_buttons_layout.addWidget(self.close_button)
        
        title_layout.addLayout(control_buttons_layout)
        
        self.layout.addWidget(title_bar)
        
    def setup_status_bar(self):
        """Configura la barra de estado en la parte inferior"""
        # Crear un contenedor para la barra de estado
        self.status_container = QFrame(self)
        self.status_container.setObjectName("statusContainer")
        self.status_container.setFixedHeight(30)
        self.status_container.setStyleSheet("""
            #statusContainer {
                background: rgba(40, 40, 40, 180);
                border: none;
                border-radius: 10px;
                padding: 0px 10px;
            }
        """)
        
        # Layout para la barra de estado
        status_layout = QHBoxLayout(self.status_container)
        status_layout.setContentsMargins(10, 0, 10, 0)
        status_layout.setSpacing(15)
        
        # Etiquetas para la información
        self.items_label = QLabel("0 elementos")
        self.items_label.setStyleSheet("""
            color: white;
            font-size: 11px;
        """)
        status_layout.addWidget(self.items_label)
        
        # Separador vertical
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        separator1.setStyleSheet("background-color: rgba(255, 255, 255, 50);")
        separator1.setFixedWidth(1)
        status_layout.addWidget(separator1)
        
        # Etiqueta para el tamaño total
        self.size_label = QLabel("0 B")
        self.size_label.setStyleSheet("""
            color: white;
            font-size: 11px;
        """)
        status_layout.addWidget(self.size_label)
        
        # Separador vertical
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        separator2.setStyleSheet("background-color: rgba(255, 255, 255, 50);")
        separator2.setFixedWidth(1)
        status_layout.addWidget(separator2)
        
        # Etiqueta para la selección
        self.selection_label = QLabel("Ningún elemento seleccionado")
        self.selection_label.setStyleSheet("""
            color: white;
            font-size: 11px;
        """)
        status_layout.addWidget(self.selection_label)
        
        status_layout.addStretch()
        
        # Añadir la barra de estado al layout principal
        self.layout.addWidget(self.status_container)
        
        # Conectar la señal de selección cambiada
        self.results_tree.itemSelectionChanged.connect(self.update_selection_info)
    
    def update_status_bar(self):
        """Actualiza la información en la barra de estado"""
        # Actualizar etiqueta de elementos
        if self.total_folders > 0 and self.total_files > 0:
            self.items_label.setText(f"{self.total_folders + self.total_files} elementos ({self.total_folders} carpetas, {self.total_files} archivos)")
        elif self.total_folders > 0:
            self.items_label.setText(f"{self.total_folders} carpetas")
        elif self.total_files > 0:
            self.items_label.setText(f"{self.total_files} archivos")
        else:
            self.items_label.setText("0 elementos")
        
        # Actualizar etiqueta de tamaño
        self.size_label.setText(format_size(self.total_size))
    
    def update_selection_info(self):
        """Actualiza la información de los elementos seleccionados"""
        selected_items = self.results_tree.selectedItems()
        
        if not selected_items:
            self.selection_label.setText("Ningún elemento seleccionado")
            return
        
        # Calcular tamaño de los elementos seleccionados
        total_selected_size = 0
        selected_files = 0
        selected_folders = 0
        
        for item in selected_items:
            path = item.text(3)
            try:
                if os.path.isfile(path):
                    total_selected_size += os.path.getsize(path)
                    selected_files += 1
                elif os.path.isdir(path):
                    selected_folders += 1
                    # No calculamos el tamaño de las carpetas para evitar operaciones lentas
            except Exception:
                pass
        
        # Actualizar etiqueta de selección
        if selected_files > 0 and selected_folders > 0:
            self.selection_label.setText(f"{selected_files + selected_folders} seleccionados ({format_size(total_selected_size)})")
        elif selected_files > 0:
            self.selection_label.setText(f"{selected_files} archivos seleccionados ({format_size(total_selected_size)})")
        elif selected_folders > 0:
            self.selection_label.setText(f"{selected_folders} carpetas seleccionadas")
        else:
            self.selection_label.setText("Ningún elemento seleccionado")
            
    def setup_results_tree(self):
        """Configura el árbol de resultados con estilo mejorado"""
        # Crear un contenedor para el árbol de resultados
        self.results_container = QFrame(self)
        self.results_container.setObjectName("resultsContainer")
        self.results_container.setStyleSheet("""
            #resultsContainer {
                background: rgba(40, 40, 40, 180);
                border: none;
                border-radius: 10px;
                padding: 5px;
            }
        """)
        
        # Crear layout para el contenedor
        container_layout = QVBoxLayout(self.results_container)
        container_layout.setContentsMargins(5, 5, 5, 5)
        container_layout.setSpacing(0)
        
        # Crear el árbol de resultados con soporte para arrastrar
        self.results_tree = DraggableTreeWidget(self.results_container)
        
        # Configurar cabeceras
        self.results_tree.setHeaderLabels(["Ext", "Tamaño", "Nombre", "Ruta"])
        
        # Configuración de redimensionamiento para las columnas
        self.results_tree.header().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Ext
        self.results_tree.header().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Tamaño
        self.results_tree.header().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)          # Nombre
        self.results_tree.header().setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)          # Ruta
        
        self.results_tree.setUniformRowHeights(True)
        self.results_tree.setRootIsDecorated(False)
        self.results_tree.setAnimated(False)
        self.results_tree.setVerticalScrollMode(QTreeWidget.ScrollMode.ScrollPerPixel)
        
        # Estilo visual actualizado
        self.results_tree.setStyleSheet("""
            QTreeWidget {
                background: transparent;
                border: none;
                color: white;
                font-size: 12px;
                outline: none;
            }
            
            QTreeWidget::item {
                background: transparent;
                padding-left: 8px;
                height: 20px;
                border: none;
                margin: 0px;
                color: white;
            }
            
            QTreeWidget::item:focus {
                outline: none;
                border: none;
            }
            
            QHeaderView {
                background: transparent;
            }
            
            QHeaderView::section {
                background-color: rgba(255, 255, 255, 30);
                color: white;
                padding: 3px 8px;
                margin: 1px 2px 0px 2px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11px;
                height: 22px;
            }
            
            QTreeWidget::branch {
                image: none;
                border: none;
                background: transparent;
            }
            
            /* Estilo para las barras de desplazamiento */
            QScrollBar:vertical {
                border: none;
                background: rgba(0, 0, 0, 50);
                width: 10px;
                margin: 0px 0px 0px 0px;
                border-radius: 5px;
            }
            
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 100);
                border-radius: 5px;
                min-height: 20px;
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: none;
                height: 0px;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)
        
        # Aplicar delegados personalizados
        self.results_tree.setItemDelegateForColumn(0, ZfindExtensionDelegate(self.results_tree))  # Delegado para la columna Ext
        self.results_tree.setItemDelegateForColumn(1, ZfindSizeDelegate(self.results_tree))  # Delegado para la columna Tamaño
        self.results_tree.setItemDelegate(ZfindRoundedRectDelegate(self.results_tree))
        
        self.results_tree.setIconSize(QSize(18, 18))
        self.results_tree.setIndentation(0)
        self.results_tree.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.results_tree.itemDoubleClicked.connect(self.open_selected_item)
        
        # Añadir el árbol al contenedor
        container_layout.addWidget(self.results_tree)
        
        # Añadir el contenedor al layout principal
        self.layout.addWidget(self.results_container)
        
    def _on_search_text_changed(self, text):
        """Maneja cambios en el texto de búsqueda con debouncing optimizado."""
        previous_query = getattr(self, 'current_query', '')
        self.current_query = text
        self.search_timer.stop()
        
        text = text.strip()
        if not text:
            self.results_tree.clear()
            return
            
        if text.strip() == previous_query.strip() and text.strip():
            return
            
        text_len = len(text)
        if hasattr(self, '_last_keystroke_time'):
            import time
            now = time.time()
            time_since_last = now - self._last_keystroke_time
            self._last_keystroke_time = now
            
            if time_since_last < 0.1:
                if text_len <= 2:
                    self.search_timer.start(800)
                else:
                    self.search_timer.start(400)
                return
        else:
            import time
            self._last_keystroke_time = time.time()
            
        if text_len == 1:
            self.search_timer.start(700)
        elif text_len == 2:
            self.search_timer.start(500)
        elif text_len <= 4:
            self.search_timer.start(300)
        else:
            self.search_timer.start(200)
            
    def _execute_search(self):
        """Ejecuta la búsqueda usando el archivo INDEXADO.txt."""
        query = self.current_query.strip()
        self.results_tree.clear()
        
        if not query:
            return
            
        if hasattr(self, 'is_searching') and self.is_searching:
            return
            
        if self.everything_api.is_everything_available():
            self._search_with_everything(query)
        else:
            print("ZFindWindow: ERROR - No se puede realizar búsqueda sin el archivo INDEXADO.txt")
            error_item = QTreeWidgetItem(self.results_tree)
            error_item.setText(0, "")  # Columna Ext vacía para el error
            error_item.setText(1, "")  # Columna Tamaño vacía para el error
            error_item.setText(2, "Error: Archivo de índice no disponible")  # Nombre
            error_item.setText(3, "ZFind requiere el archivo INDEXADO.txt para funcionar")  # Ruta
            self.results_tree.addTopLevelItem(error_item)
            
    def _search_with_everything(self, query):  # Mantenemos el nombre por compatibilidad
        try:
            self.is_searching = True
            print(f"ZFindWindow: Buscando con Everything: '{query}'")
            
            # Determinar el número máximo de resultados basado en la longitud de la consulta
            query_len = len(query.strip())
            if query_len == 1:
                max_results = 100
            elif query_len == 2:
                max_results = 300
            elif query_len <= 4:
                max_results = 500
            else:
                max_results = self.max_results
            
            # Aplicar filtro Optimize Zet si está marcado
            final_query = query
            if self.optimize_zet_checkbox.isChecked():
                print("ZFindWindow: Optimize Zet activado - filtro se aplicará en resultados")
            else:
                print("ZFindWindow: Búsqueda normal sin filtros")
            
            # Iniciar el trabajador de búsqueda
            worker = SearchWorker(final_query, self.everything_api, max_results)
            worker.signals.finished.connect(self._on_search_finished)
            worker.signals.error.connect(self._on_search_error)
            self.thread_pool.start(worker)
            
        except Exception as e:
            print(f"ZFindWindow: Error al iniciar el hilo de búsqueda: {e}")
            self.is_searching = False
            
    def _on_search_finished(self, results):
        try:
            self.results_tree.setUpdatesEnabled(False)
            self.results_tree.clear()
            
            items_to_add = []
            folders = []
            files = []
            seen_paths = set()
            
            # Reiniciar estadísticas
            self.total_files = 0
            self.total_folders = 0
            self.total_size = 0
            
            for result in results:
                if result['full_path'] in seen_paths:
                    continue
                    
                seen_paths.add(result['full_path'])
                
                # Obtener la extensión para filtrar archivos .lnk y aplicar Optimize Zet
                file_path = result['full_path']
                name = file_path.split('\\')[-1] if '\\' in file_path else file_path
                ext = name.rsplit('.', 1)[-1].lower() if '.' in name else ''
                
                if ext == 'lnk':  # Si es un archivo .lnk, saltar
                    continue
                
                # Aplicar filtro Optimize Zet si está marcado
                if self.optimize_zet_checkbox.isChecked():
                    # Definir extensiones de video y audio
                    video_audio_extensions = {
                        'mp4', 'avi', 'mkv', 'mov', 'mpg', 'mpeg', 'wmv', 'flv', 
                        'webm', '3gp', 'ts', 'vob', 'rmvb', 'divx', 'ogv', 'm4v',
                        'asf', 'rm', 'f4v', 'mts', 'm2ts', 'mp3', 'wav', 'flac', 
                        'aac', 'ogg', 'wma', 'm4a', 'opus', 'aiff', 'au', 'ra', 
                        'amr', 'ac3', 'dts'
                    }
                    
                    # Si es un archivo, verificar que tenga extensión de video/audio
                    if result.get('is_file', False) and ext not in video_audio_extensions:
                        continue
                    
                    # Si es una carpeta, saltarla cuando Optimize Zet está activo
                    if result.get('is_folder', False):
                        continue
                
                # Verificar si el archivo/carpeta existe
                if not os.path.exists(file_path):
                    print(f"ZFindWindow: Archivo no encontrado: {file_path}")
                    continue
                
                # Determinar si es carpeta o archivo
                # Si ya viene marcado como carpeta desde IndexadoAPI, respetamos esa clasificación
                if result.get('is_folder', False):
                    is_folder = True
                    is_file = False
                else:
                    # Verificar en el sistema de archivos
                    is_folder = os.path.isdir(file_path)
                    is_file = os.path.isfile(file_path)
                
                # Actualizar la información en el resultado
                result['is_folder'] = is_folder
                result['is_file'] = is_file
                
                if is_folder:
                    folders.append(result)
                    self.total_folders += 1
                elif is_file:
                    files.append(result)
                    self.total_files += 1
                    
                    # Calcular tamaño del archivo
                    try:
                        file_size = os.path.getsize(file_path)
                        self.total_size += file_size
                        
                        # Guardar el tamaño como dato adicional para usarlo después
                        result['size'] = file_size
                    except Exception as e:
                        print(f"Error al obtener tamaño de {file_path}: {e}")
                        result['size'] = 0
            
            # Procesar carpetas
            folder_icon = icono_carpeta(18)
            for folder in folders:
                item = QTreeWidgetItem()
                item.setText(0, "DIR")  # Indicador de directorio en la columna de extensión
                item.setText(1, "Carpeta")  # Indicador de carpeta en lugar de tamaño
                item.setText(2, folder['name'])  # Nombre
                item.setText(3, folder['full_path'])  # Ruta
                
                # Guardar datos adicionales
                item.setData(1, Qt.ItemDataRole.UserRole, {
                    'is_folder': True,
                    'path': folder['full_path']
                })
                
                # Aplicar estilo especial para carpetas
                # Usar un color de fondo diferente para distinguir carpetas
                item.setBackground(2, QColor(40, 70, 100, 40))  # Color azul oscuro semitransparente
                
                # Usar fuente en negrita para carpetas
                font = item.font(2)
                font.setBold(True)
                item.setFont(2, font)
                
                if folder_icon:
                    item.setIcon(2, folder_icon)  # Icono en la columna Nombre
                    
                items_to_add.append(item)
            
            # Procesar archivos
            icon_cache = {}
            for file_result in files:
                item = QTreeWidgetItem()
                file_path = file_result['full_path']
                name = file_path.split('\\')[-1] if '\\' in file_path else file_path
                ext = name.rsplit('.', 1)[-1].lower() if '.' in name else ''
                
                # Solo filtrar archivos .lnk
                if ext == 'lnk':
                    continue
                
                # No filtrar por tipo de extensión
                # Mostrar todos los archivos que se encuentran en el índice
                
                if ext: # Display extension for all files if it exists
                    # Limitar la longitud de la extensión mostrada para evitar romper el diseño
                    if len(ext) > 5:  # Si la extensión es más larga que 5 caracteres
                        ext = ext[:5] + "…"  # Truncar y añadir puntos suspensivos
                    item.setText(0, ext.lower()) # Set it as lowercase without the dot
                else:
                    item.setText(0, "") # No extension for folders or files without one
                    
                # Mostrar el tamaño del archivo en la columna 1
                file_size = file_result.get('size', 0)
                item.setText(1, format_size(file_size))
                    
                item.setText(2, file_result['name'])  # Nombre en la columna 2
                item.setText(3, file_result['full_path'])  # Ruta en la columna 3
                
                # Guardar datos adicionales
                item.setData(1, Qt.ItemDataRole.UserRole, {
                    'is_folder': False,
                    'path': file_path,
                    'size': file_result.get('size', 0)
                })
                
                if ext and ext in icon_cache:
                    item.setIcon(2, icon_cache[ext])  # Icono en la columna Nombre
                else:
                    icon = self.file_icon_manager.get_icon_for_file(file_path)
                    if icon:
                        item.setIcon(2, icon)  # Icono en la columna Nombre
                        if ext:
                            icon_cache[ext] = icon
                            
                items_to_add.append(item)
            
            # Añadir los elementos al árbol
            self.results_tree.addTopLevelItems(items_to_add)
            
            # Si no hay resultados, mostrar un mensaje
            total_items = len(items_to_add)
            if total_items == 0:
                no_results_item = QTreeWidgetItem()
                no_results_item.setText(0, "")  # Columna Ext vacía
                no_results_item.setText(1, "")  # Columna Tamaño vacía
                no_results_item.setText(2, "No se encontraron resultados")  # Nombre
                no_results_item.setText(3, f"No hay coincidencias para: '{self.current_query}'")  # Ruta
                self.results_tree.addTopLevelItem(no_results_item)
            
            self.results_tree.setUpdatesEnabled(True)
            
            # Actualizar la barra de estado
            self.update_status_bar()
            
            print(f"ZFindWindow: Se agregaron {total_items} elementos al árbol ({len(folders)} carpetas, {len(files)} archivos)")
            
        except Exception as e:
            print(f"ZFindWindow: Error al procesar resultados: {e}")
            error_item = QTreeWidgetItem(self.results_tree)
            error_item.setText(0, "")
            error_item.setText(1, "")
            error_item.setText(2, f"Error al procesar resultados: {str(e)}")
            error_item.setText(2, "Intente de nuevo o contacte al soporte.")
            self.results_tree.addTopLevelItem(error_item)
            
            # Reiniciar estadísticas en caso de error
            self.total_files = 0
            self.total_folders = 0
            self.total_size = 0
            self.update_status_bar()
            
        finally:
            self.is_searching = False
            
    def _on_search_error(self, message):
        print(f"ZFindWindow: Error en el hilo de búsqueda: {message}")
        self.results_tree.setUpdatesEnabled(False)
        self.results_tree.clear()
        
        error_item = QTreeWidgetItem(self.results_tree)
        error_item.setText(0, "")
        error_item.setText(1, "Error en búsqueda")
        error_item.setText(2, message)
        
        self.results_tree.addTopLevelItem(error_item)
        self.results_tree.setUpdatesEnabled(True)
        
        # Reiniciar estadísticas en caso de error
        self.total_files = 0
        self.total_folders = 0
        self.total_size = 0
        self.update_status_bar()
        
        self.is_searching = False
        
    def open_selected_item(self, item):
        """Abre el elemento seleccionado"""
        if not item:
            return
            
        path = item.text(3)  # La ruta está en la columna 3
        if not path:
            return
            
        try:
            os.startfile(path)
        except Exception as e:
            print(f"Error al abrir archivo: {e}")
            
    def toggle_maximize(self):
        """Alterna entre maximizado y restaurado."""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
            
    def mousePressEvent(self, event):
        """Maneja eventos de presión del mouse para arrastrar ventana."""
        if self.window_resizer.handle_mouse_press(event):
            return
        super().mousePressEvent(event)
        
    def mouseMoveEvent(self, event):
        """Maneja eventos de movimiento del mouse para arrastrar ventana."""
        if self.window_resizer.handle_mouse_move(event):
            return
        super().mouseMoveEvent(event)
        
    def mouseReleaseEvent(self, event):
        """Maneja eventos de liberación del mouse."""
        if self.window_resizer.handle_mouse_release(event):
            return
        super().mouseReleaseEvent(event)
        
    def resizeEvent(self, event):
        """Maneja eventos de redimensionamiento."""
        super().resizeEvent(event)
        if hasattr(self, 'window_resizer'):
            self.window_resizer.update_positions()
            
    def load_optimize_zet_state(self):
        """Carga el estado del checkbox Optimize Zet desde config.json"""
        try:
            import json
            config_path = "config.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    optimize_zet_enabled = config.get('zfind_optimize_zet', False)
                    self.optimize_zet_checkbox.setChecked(optimize_zet_enabled)
                    print(f"ZFindWindow: Estado Optimize Zet cargado: {optimize_zet_enabled}")
            else:
                print("ZFindWindow: config.json no encontrado, usando estado por defecto")
        except Exception as e:
            print(f"ZFindWindow: Error cargando estado Optimize Zet: {e}")
    
    def save_optimize_zet_state(self):
        """Guarda el estado del checkbox Optimize Zet en config.json"""
        try:
            import json
            config_path = "config.json"
            config = {}
            
            # Cargar configuración existente
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # Actualizar estado de Optimize Zet
            config['zfind_optimize_zet'] = self.optimize_zet_checkbox.isChecked()
            
            # Guardar configuración
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
            print(f"ZFindWindow: Estado Optimize Zet guardado: {config['zfind_optimize_zet']}")
        except Exception as e:
            print(f"ZFindWindow: Error guardando estado Optimize Zet: {e}")
    
    def reapply_acrylic_effect(self):
        """Reaplica el efecto acrílico cuando la ventana se muestra desde el área de notificación"""
        try:
            from APARIENCIA import apply_acrylic_and_rounded
            hwnd = int(self.winId())
            apply_acrylic_and_rounded(hwnd)
            print("ZFindWindow: Efecto acrílico reaplicado correctamente")
        except Exception as e:
            print(f"ZFindWindow: Error reaplicando efecto acrílico: {e}")
    
    def hide_to_tray(self):
        """Oculta la ventana al área de notificación restaurando correctamente el estado del botón"""
        try:
            # Restaurar el estado normal del botón de cerrar
            if hasattr(self, 'close_button') and self.close_button is not None:
                # Ocultar tooltip si está visible
                self.close_button.hideTooltip()
                
                # Detener cualquier animación en curso
                if hasattr(self.close_button, '_animation'):
                    self.close_button._animation.stop()
                
                # Restaurar escala normal inmediatamente
                if hasattr(self.close_button, '_scale'):
                    self.close_button._scale = 1.0
                    self.close_button.update()
                
                # Simular leaveEvent para completar la transición
                fake_event = QEvent(QEvent.Type.Leave)
                self.close_button.leaveEvent(fake_event)
            
            # Ocultar la ventana
            self.hide()
            print("ZFindWindow: Ventana ocultada correctamente al área de notificación")
            
        except Exception as e:
            print(f"ZFindWindow: Error ocultando ventana: {e}")
            # Fallback: ocultar la ventana de todos modos
            self.hide()


class ZFindManager:
    """Maneja el sistema completo de ZFind incluyendo área de notificación y carga rápida"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.zfind_instance = None
        self.zfind_tray_icon = None
    
    def initialize_system(self):
        """Inicializa el sistema completo de ZFind"""
        try:
            print("ZFindManager: Inicializando sistema ZFind en área de notificación...")
            
            # Inicializar área de notificación
            self._setup_system_tray()
            
            # Inicializar ZFind en segundo plano
            self._init_zfind_background()
            
            print("ZFindManager: Sistema ZFind inicializado correctamente")
            
        except Exception as e:
            print(f"ZFindManager: Error inicializando sistema: {e}")
    
    def _setup_system_tray(self):
        """Configura el icono y menú del área de notificación"""
        try:
            from CREAR import Icono_BUSCADOR
            
            # Verificar si el sistema soporta área de notificación
            if not QSystemTrayIcon.isSystemTrayAvailable():
                print("ZFindManager: Área de notificación no disponible en este sistema")
                return
            
            # Crear el icono del área de notificación
            self.zfind_tray_icon = QSystemTrayIcon(self.main_window)
            self.zfind_tray_icon.setIcon(Icono_BUSCADOR(32))
            self.zfind_tray_icon.setToolTip("ZFind - Buscador de Archivos")
            
            # Crear menú contextual
            tray_menu = QMenu()
            
            show_action = QAction("Mostrar ZFind", self.main_window)
            show_action.triggered.connect(self.show_zfind)
            tray_menu.addAction(show_action)
            
            tray_menu.addSeparator()
            
            hide_action = QAction("Ocultar ZFind", self.main_window)
            hide_action.triggered.connect(self.hide_zfind)
            tray_menu.addAction(hide_action)
            
            self.zfind_tray_icon.setContextMenu(tray_menu)
            
            # Conectar doble clic
            self.zfind_tray_icon.activated.connect(self._on_tray_activated)
            
            # Mostrar el icono
            self.zfind_tray_icon.show()
            
            print("ZFindManager: Área de notificación configurada correctamente")
            
        except Exception as e:
            print(f"ZFindManager: Error configurando área de notificación: {e}")
    
    def _init_zfind_background(self):
        """Prepara ZFind para carga rápida (sin crear la instancia aún)"""
        try:
            # No crear la instancia aún, solo preparar para carga rápida
            self.zfind_instance = None
            
            print("ZFindManager: ZFind preparado para carga rápida")
            
        except Exception as e:
            print(f"ZFindManager: Error preparando ZFind: {e}")
            self.zfind_instance = None
    
    def show_zfind(self):
        """Muestra ZFind desde el área de notificación"""
        try:
            # Verificar si Everything está instalado antes de crear/mostrar ZFind
            if self.zfind_instance is None:
                # Verificar instalación de Everything antes de crear la instancia
                from EVERYTHING_INSTALLER import check_everything_installation
                
                installed, path = check_everything_installation()
                if not installed:
                    print("ZFindManager: Everything no está instalado, mostrando diálogo de instalación...")
                    from EVERYTHING_INSTALLER import show_everything_install_dialog
                    
                    if show_everything_install_dialog(self.main_window):
                        print("ZFindManager: Everything instalado correctamente")
                    else:
                        print("ZFindManager: Instalación de Everything cancelada")
                        return
                
                # Crear nueva instancia solo cuando se necesite
                print("ZFindManager: Creando nueva instancia de ZFind...")
                self.zfind_instance = ZFindWindow(parent=None)
                print("ZFindManager: Nueva instancia de ZFind creada")
            
            # Mostrar la instancia
            if self.zfind_instance is not None:
                # Reaplicar efecto acrílico antes de mostrar
                self.zfind_instance.reapply_acrylic_effect()
                self.zfind_instance.show()
                self.zfind_instance.raise_()
                self.zfind_instance.activateWindow()
                print("ZFindManager: ZFind mostrado")
                
        except Exception as e:
            print(f"ZFindManager: Error mostrando ZFind: {e}")
    
    def hide_zfind(self):
        """Oculta ZFind al área de notificación"""
        try:
            if self.zfind_instance is not None:
                self.zfind_instance.hide()
                print("ZFindManager: ZFind ocultado al área de notificación")
        except Exception as e:
            print(f"ZFindManager: Error ocultando ZFind: {e}")
    
    def _on_tray_activated(self, reason):
        """Maneja la activación del icono en el área de notificación"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_zfind()

    def closeEvent(self, event):
        """Maneja el evento de cierre de la ventana - ocultar al área de notificación en lugar de cerrar."""
        try:
            # Restaurar el estado normal del botón de cerrar antes de ocultar
            if hasattr(self, 'close_button') and self.close_button is not None:
                # Ocultar tooltip si está visible
                self.close_button.hideTooltip()
                
                # Detener cualquier animación en curso
                if hasattr(self.close_button, '_animation'):
                    self.close_button._animation.stop()
                
                # Restaurar escala normal
                if hasattr(self.close_button, '_scale'):
                    self.close_button._scale = 1.0
                    self.close_button.update()
                
                # Simular leaveEvent para restaurar estado normal
                fake_event = QEvent(QEvent.Type.Leave)
                self.close_button.leaveEvent(fake_event)
            
            # Ignorar el evento de cierre y ocultar la ventana
            event.ignore()
            self.hide()
            print("ZFindWindow: Ventana ocultada al área de notificación")
            
        except Exception as e:
            print(f"ZFindWindow: Error en closeEvent: {e}")
            # Fallback: ocultar la ventana de todos modos
            event.ignore()
            self.hide()
        
    def keyPressEvent(self, event):
        """Maneja eventos de teclado"""
        if event.key() == Qt.Key.Key_Down:
            if self.results_tree.topLevelItemCount() > 0:
                current_item = self.results_tree.currentItem()
                if not current_item:
                    self.results_tree.setCurrentItem(self.results_tree.topLevelItem(0))
                else:
                    current_index = self.results_tree.indexOfTopLevelItem(current_item)
                    if current_index < self.results_tree.topLevelItemCount() - 1:
                        self.results_tree.setCurrentItem(self.results_tree.topLevelItem(current_index + 1))
            return
            
        elif event.key() == Qt.Key.Key_Up:
            if self.results_tree.topLevelItemCount() > 0:
                current_item = self.results_tree.currentItem()
                if current_item:
                    current_index = self.results_tree.indexOfTopLevelItem(current_item)
                    if current_index > 0:
                        self.results_tree.setCurrentItem(self.results_tree.topLevelItem(current_index - 1))
            return
            
        elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            current_item = self.results_tree.currentItem()
            if current_item:
                self.open_selected_item(current_item)
            return
            
        elif event.key() == Qt.Key.Key_Escape:
            self.close()
            return
            
        super().keyPressEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ZFindWindow()
    window.show()
    sys.exit(app.exec())