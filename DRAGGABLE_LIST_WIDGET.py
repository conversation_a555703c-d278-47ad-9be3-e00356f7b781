from PyQt6.QtWidgets import (
    QApplication, QTreeWidgetItem, QFileIconProvider, QTreeWidget, QInputDialog, 
    QDialog, QToolTip, QHeaderView, QLineEdit, QFileDialog, QSpacerItem, 
    QToolButton, QMessageBox, QStackedLayout, QSizePolicy, QMainWindow, 
    QDockWidget, QFrame, QListWidget, QVBoxLayout, QListWidgetItem, 
    QProgressBar, QSizeGrip, QAbstractItemView, QLabel, QHBoxLayout, 
    QWidget, QGraphicsDropShadowEffect, QPushButton  # Añadido <PERSON>ush<PERSON>utton aquí
)
from PyQt6.QtCore import Qt, QMimeData, QPropertyAnimation, QRectF, QParallelAnimationGroup, QMutex, QSize, QFileInfo, QDateTime, QPoint, QRect, QLocale, QEvent, QTimer, QThread, pyqtSignal, Q_ARG, pyqtSlot, QObject, QRunnable, QThreadPool, QMetaObject  # Añadidas estas importaciones
from PyQt6.QtGui import (
    QPainter, QFont, QColor, QCursor, QDragEnterEvent, QDropEvent, 
    QIcon, QPixmap, QShortcut, QKeySequence, QPainterPath, QPen
)
from TOOLTIP_APARIENCIA import showTooltipAtCursor, showTooltipAtWidget, showCustomTooltip, _hide_active_tooltip
from Propiedades_Disco import PropiedadesDialog
from ACTIVATE import show_license_activation_dialog 
from cryptography.fernet import Fernet
import sys
import json
import os
import logging
import win32api
import traceback  
import time
import datetime
import threading
import queue
class DraggableListWidget(QListWidget):
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.setMouseTracking(True)
        from AJUSTES import load_config
        config = load_config()
        self.show_disk_lines = config.get('show_disk_lines', True)  # Cargar estado desde config
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.old_pos = None
        self.setAcceptDrops(True)
        self.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.itemDoubleClicked.connect(self.on_item_double_clicked) 
        self.viewport().setAcceptDrops(True) 
        self.setDragDropMode(QAbstractItemView.DragDropMode.DropOnly)

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.setDropAction(Qt.DropAction.CopyAction)
            event.accept()
        else:
            super().dragEnterEvent(event)
    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Control:
            self.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)  # Activar selección múltiple
        elif event.key() == Qt.Key.Key_Shift:
            self.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)  # Activar selección de rango con Shift
        elif event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return):
            self.main_window.ventana_cola()  # Abrir ventana de cola
        else:
            super().keyPressEvent(event)
    
    def keyReleaseEvent(self, event):
        if event.key() == Qt.Key.Key_Control:
            self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)  # Volver a selección única
        else:
            super().keyReleaseEvent(event)
    
    def event(self, event):
        if event.type() == QEvent.Type.ToolTip:
            item = self.itemAt(event.pos())
            if item and self.is_cursor_over_icon(event.pos(), item):
                drive_letter = item.text().split(' ')[-1].strip('()')
                self.start_tooltip_worker(drive_letter, event.globalPos())
            else:
                if hasattr(self, 'current_tooltip'):
                    self.current_tooltip.close()
                    delattr(self, 'current_tooltip')
            return True
        elif event.type() == QEvent.Type.Leave:
            if hasattr(self, 'current_tooltip'):
                self.current_tooltip.close()
                delattr(self, 'current_tooltip')
            _hide_active_tooltip()  # Asegurarse de ocultar cualquier tooltip activo
        return super().event(event)

    def start_tooltip_worker(self, drive_letter, pos):
        item = self.itemAt(self.mapFromGlobal(pos))
        if item:
            if not hasattr(self, 'propiedades_dialog'):
                self.propiedades_dialog = PropiedadesDialog(self)
            
            # Verificar si el disco está copiando y obtener información del origen
            source_disk_info = None
            if hasattr(self.main_window, 'current_copying_file') and drive_letter in self.main_window.current_copying_file:
                current_file = self.main_window.current_copying_file[drive_letter]
                if current_file and isinstance(current_file, (list, tuple)) and len(current_file) > 0:
                    source_path = current_file[0]
                    source_drive = os.path.splitdrive(source_path)[0]
                    if source_drive:
                        try:
                            source_name = win32api.GetVolumeInformation(f"{source_drive}\\")[0]
                            source_disk_info = (source_drive, source_name or source_drive)
                        except:
                            source_disk_info = (source_drive, source_drive)
            
            self.propiedades_dialog.show_properties(drive_letter, pos, item, source_disk_info)

    def hide_tooltip_if_needed(self, last_pos):
        if hasattr(self, 'current_tooltip'):
            current_pos = self.mapFromGlobal(QCursor.pos())
            item = self.itemAt(current_pos)
            if not item or not self.is_cursor_over_icon(current_pos, item):
                self.current_tooltip.close()
                delattr(self, 'current_tooltip')
                _hide_active_tooltip()  # Asegurarse de ocultar cualquier tooltip activo
    def is_cursor_over_icon(self, pos, item):
        rect = self.visualItemRect(item)
        icon_rect = QRect(rect.left(), rect.top(), 30, rect.height())
        return icon_rect.contains(pos)
    def dragMoveEvent(self, event):
        if event.mimeData().hasUrls():
            event.setDropAction(Qt.DropAction.TargetMoveAction)
            event.acceptProposedAction()
        else:
            super().dragMoveEvent(event)
        self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def leaveEvent(self, event):
        _hide_active_tooltip()
        super().leaveEvent(event)
        self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def dropEvent(self, event: QDropEvent):
        if event.mimeData().hasUrls():
            event.setDropAction(Qt.DropAction.CopyAction)
            event.accept()
            selected_items = self.selectedItems()
            if not selected_items:
                self.show_tooltip("No hay discos seleccionados.")
                return
            archivos_existentes = []
            archivos_en_cola = []
            self.main_window._sound_played = False
            shift_pressed = bool(QApplication.keyboardModifiers() & Qt.KeyboardModifier.ShiftModifier)
            for selected_item in selected_items:
                selected_volume = selected_item.text()
                drive_letter = selected_volume.split(' ')[-1].strip('()')
                volume_name = selected_volume.split(' ')[0].strip()
                current_path = getattr(self.main_window, 'current_drive', None) or drive_letter
                urls = event.mimeData().urls()
                dragging_folder = any(os.path.isdir(url.toLocalFile()) for url in urls)
                # Verificar archivos existentes y en cola
                for url in urls:
                    file_path = url.toLocalFile()
                    if os.path.exists(file_path):
                        destination_path = os.path.join(current_path, os.path.basename(file_path))
                        
                        # Verificar si ya existe en destino
                        if os.path.exists(destination_path):
                            archivos_existentes.append(os.path.basename(file_path))
                        
                        # Verificar si está en cola
                        if self.is_in_queue(file_path, drive_letter):
                            archivos_en_cola.append(os.path.basename(file_path))
                
                # Si hay archivos existentes o en cola, mostrar tooltip y continuar
                if archivos_existentes or archivos_en_cola:
                    mensaje = ""
                    if archivos_existentes:
                        if len(archivos_existentes) > 3:
                            mensaje = f"⚠️ {len(archivos_existentes)} archivos ya existen en el destino"
                        else:
                            mensaje = f"⚠️ Ya existen en el destino: {', '.join(archivos_existentes)}"
                    if archivos_en_cola:
                        if mensaje:
                            if len(archivos_en_cola) > 3:
                                mensaje += f"\n⚠️ {len(archivos_en_cola)} archivos ya están en la cola"
                            else:
                                mensaje += f"\n⚠️ Ya en cola: {', '.join(archivos_en_cola)}"
                        else:
                            if len(archivos_en_cola) > 3:
                                mensaje = f"⚠️ {len(archivos_en_cola)} archivos ya están en la cola"
                            else:
                                mensaje = f"⚠️ Ya en cola: {', '.join(archivos_en_cola)}"
                    self.show_tooltip(mensaje)

                # Verificar si estamos en modo espejo (sin copiar o copiando)
                if self.main_window.botones.mirror_selected and dragging_folder:
                    for url in urls:
                        source_dir = url.toLocalFile()
                        if os.path.isdir(source_dir):
                            dest_dir = os.path.join(current_path, os.path.basename(source_dir))
                            if os.path.exists(dest_dir):
                                self.sync_directories(source_dir, dest_dir)
                                if self.main_window.botones.mirror_mode == "copiando":
                                    self.main_window.botones.set_normal_mode()
                                    continue
                                else:
                                    self.main_window.botones.set_normal_mode()
                                    return
                # Procesar todos los archivos sin verificar espacio
                files_to_process = []
                for url in urls:
                    file_path = url.toLocalFile()
                    try:
                        if os.path.exists(file_path):
                            files_to_process.append(url)
                    except OSError:
                        continue
                if files_to_process:
                    destination_folder = current_path
                    if shift_pressed and not dragging_folder and files_to_process:
                        try:
                            parent_folder = os.path.basename(os.path.dirname(files_to_process[0].toLocalFile()))
                            destination_folder = os.path.join(current_path, parent_folder)
                            os.makedirs(destination_folder, exist_ok=True)
                        except Exception as e:
                            self.show_tooltip(f"Error al crear la carpeta: {str(e)}")
                            continue
                    # Verificar si el disco está copiando o tiene archivos en cola
                    is_copying = self.main_window.is_copying_or_queued(drive_letter)
                    
                    # Si se está arrastrando una carpeta y el disco NO está copiando,
                    # crear los directorios inmediatamente
                    directories_created = False
                    if dragging_folder and not is_copying:
                        for url in files_to_process:
                            file_path = url.toLocalFile()
                            if os.path.isdir(file_path):
                                dest_dir = os.path.join(destination_folder, os.path.basename(file_path))
                                if not os.path.exists(dest_dir):
                                    try:
                                        os.makedirs(dest_dir, exist_ok=True)
                                        directories_created = True
                                    except Exception as e:
                                        print(f"Error creando directorio {dest_dir}: {e}")
                    
                    from ZETACOPY import FileProcessingThread
                    self.processing_thread = FileProcessingThread(
                        files_to_process,
                        drive_letter,
                        shift_pressed,
                        destination_folder,
                        volume_name,
                        dragging_folder,
                        len(files_to_process),
                        self.main_window.queue_progress_bar
                    )
                    self.processing_thread.filesProcessed.connect(self.process_dropped_files)
                    self.processing_thread.progressUpdated.connect(lambda x: None)
                    self.processing_thread.fileProgressUpdated.connect(
                        lambda current, total: self.main_window.queue_progress_bar.setFormat(f"{current} | {total}")
                        if current > 0 else None
                    )
                    QTimer.singleShot(100, lambda: self.processing_thread.progressUpdated.connect(
                        self.main_window.queue_progress_bar.setValue))
                    self.processing_thread.start()
        else:
            event.ignore()
        self.setCursor(Qt.CursorShape.ArrowCursor)

    def sync_directories(self, source_dir, dest_dir):
        """
        Sincroniza el directorio destino con el origen, con opción de copiar archivos
        """
        try:
            source_files = set()
            for root, _, files in os.walk(source_dir):
                rel_path = os.path.relpath(root, source_dir)
                for file in files:
                    source_files.add(os.path.join(rel_path, file).lower())
            dest_files = set()
            for root, _, files in os.walk(dest_dir):
                rel_path = os.path.relpath(root, dest_dir)
                for file in files:
                    dest_files.add(os.path.join(rel_path, file).lower())
            files_to_delete = dest_files - source_files
            files_deleted = 0
            for file in files_to_delete:
                full_path = os.path.join(dest_dir, file)
                try:
                    os.remove(full_path)
                    files_deleted += 1
                except Exception as e:
                    print(f"Error eliminando archivo {full_path}: {e}")
            for root, dirs, _ in os.walk(dest_dir, topdown=False):
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        os.rmdir(dir_path)  # Solo elimina si está vacío
                    except OSError:
                        pass
            if hasattr(self.main_window, 'botones') and self.main_window.botones.mirror_mode == "copiando":
                files_to_copy = source_files - dest_files
                if files_to_copy:
                    for file in files_to_copy:
                        source_path = os.path.join(source_dir, file)
                        dest_path = os.path.join(dest_dir, file)
                        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                        dest_drive = os.path.splitdrive(dest_dir)[0]
                        if not dest_drive.endswith(':'):
                            dest_drive += ':'
                        self.queue_file(source_path, dest_path, dest_drive)
                    self.show_tooltip(f"Modo espejo: {files_deleted} archivos eliminados y {len(files_to_copy)} archivos agregados a la cola")
                else:
                    self.show_tooltip(f"Modo espejo: {files_deleted} archivos eliminados. No hay archivos nuevos para copiar")
                self.main_window.botones.set_normal_mode()
            else:
                if files_deleted > 0:
                    self.show_tooltip(f"Modo espejo: {files_deleted} archivos eliminados del destino")
                else:
                    self.show_tooltip("Modo espejo: No se encontraron diferencias")
                self.main_window.botones.set_normal_mode()
        except Exception as e:
            self.show_tooltip(f"Error en sincronización: {str(e)}")
            self.main_window.botones.set_normal_mode()

    def _process_files_in_background(self, processed_files, drive_letter, current_path, volume_name):
        """Procesa los archivos en un hilo separado para no bloquear la UI"""
        try:
            # Primero crear directorios
            directories_to_create = set()
            for file_path, _, destination_path in processed_files:
                if os.path.isfile(file_path):
                    # Obtener el directorio padre del archivo destino
                    parent_dir = os.path.dirname(destination_path)
                    directories_to_create.add(parent_dir)
                elif os.path.isdir(file_path):
                    # Para directorios, añadir toda la estructura
                    source_base = os.path.dirname(file_path)
                    for root, _, _ in os.walk(file_path):
                        rel_path = os.path.relpath(root, source_base)
                        dest_dir = os.path.join(current_path, rel_path)
                        directories_to_create.add(dest_dir)

            # Crear directorios en orden jerárquico
            for directory in sorted(directories_to_create, key=len):
                try:
                    if not os.path.exists(directory):
                        os.makedirs(directory, exist_ok=True)
                        print(f"Creado directorio: {directory}")
                except Exception as e:
                    print(f"Error creando directorio {directory}: {e}")
                    continue
            QMetaObject.invokeMethod(
                self, 
                "_check_space_in_main_thread",
                Qt.ConnectionType.QueuedConnection,
                Q_ARG(list, processed_files),
                Q_ARG(str, drive_letter)
            )
        except Exception as e:
            print(f"Error en el procesamiento en segundo plano: {e}")
            import traceback
            traceback.print_exc()
            QMetaObject.invokeMethod(
                self.main_window.queue_progress_bar,
                "setVisible",
                Qt.ConnectionType.QueuedConnection,
                Q_ARG(bool, False)
            )

    def _create_directories_and_check_space(self, processed_files, current_path, drive_letter):
        """Función que se ejecuta en un hilo separado para crear directorios y verificar espacio"""
        try:
            # Primero crear toda la estructura de directorios
            directories_to_create = set()
            for file_path, _, destination_path in processed_files:
                if os.path.isfile(file_path):
                    # Obtener el directorio padre del archivo destino
                    parent_dir = os.path.dirname(destination_path)
                    directories_to_create.add(parent_dir)
                elif os.path.isdir(file_path):
                    # Para directorios, añadir toda la estructura
                    source_base = os.path.dirname(file_path)
                    for root, _, _ in os.walk(file_path):
                        rel_path = os.path.relpath(root, source_base)
                        dest_dir = os.path.join(current_path, rel_path)
                        directories_to_create.add(dest_dir)

            # Crear directorios en orden jerárquico
            for directory in sorted(directories_to_create, key=len):
                try:
                    if not os.path.exists(directory):
                        os.makedirs(directory, exist_ok=True)
                        print(f"Creado directorio: {directory}")
                except Exception as e:
                    print(f"Error creando directorio {directory}: {e}")
                    continue
                    
            # Una vez creados los directorios, verificar el espacio disponible
            # Necesitamos usar QMetaObject para llamar a métodos de Qt desde un hilo no-Qt
            QMetaObject.invokeMethod(
                self, 
                "_check_space_in_main_thread",
                Qt.ConnectionType.QueuedConnection,
                Q_ARG(list, processed_files),
                Q_ARG(str, drive_letter)
            )
        except Exception as e:
            print(f"Error en el hilo de creación de directorios: {e}")
            import traceback
            traceback.print_exc()

    @pyqtSlot(list, str)
    def _check_space_in_main_thread(self, processed_files, drive_letter):
        from ZETACOPY import DriveSpaceWorker
        """Método que se ejecuta en el hilo principal para verificar el espacio"""
        space_worker = DriveSpaceWorker(drive_letter, self)
        from functools import partial
        space_worker.finished.connect(
            partial(self.continue_processing_files, processed_files, drive_letter)
        )
        space_worker.start()

    def check_space_for_files(self, processed_files, drive_letter): # ESTA CALCULA EL ESPACIO FINAL DEL DISCO
        from ZETACOPY import get_drive_space
        # Ahora procesar el espacio y los archivos
        free_space = get_drive_space(drive_letter)
        logging.info(f"Espacio libre inicial: {self.format_size(free_space)}")
        
        # Margen de seguridad
        margin = 20 * 1024 * 1024  # 20 MB
        logging.info(f"Margen de seguridad: {self.format_size(margin)}")
        
        # Calcular el espacio que ocuparán los archivos en cola
        espacio_cola = 0
        if drive_letter in self.file_queues and self.file_queues[drive_letter]:
            for file_info in self.file_queues[drive_letter]:
                espacio_cola += file_info.get('size', 0)
        logging.info(f"Espacio usado por copias pendientes: {self.format_size(espacio_cola)}")
        
        # Determinar si hay una copia en progreso
        is_copying_active = self.is_copying(drive_letter) or drive_letter in self.copy_threads
        

        # Calcular espacio requerido para los nuevos archivos
        total_size = 0
        for file in processed_files:
            if os.path.isfile(file):
                try:
                    file_size = os.path.getsize(file)
                    total_size += file_size
                except Exception as e:
                    logging.error(f"Error al obtener tamaño de archivo {file}: {str(e)}")
        logging.info(f"Espacio requerido por nuevos archivos: {self.format_size(total_size)}")
        
        # Verificación simple de espacio disponible
        espacio_disponible = free_space - margin
        if is_copying_active:
            espacio_disponible -= espacio_cola
            logging.info(f"Copia activa - Espacio disponible después de restar cola: {self.format_size(espacio_disponible)}")
        
        # Verificar si hay suficiente espacio
        if espacio_disponible < total_size:
            message = f"No hay suficiente espacio en el disco {drive_letter}.\n"
            message += f"Espacio disponible: {self.format_size(espacio_disponible)}\n"
            message += f"Espacio requerido: {self.format_size(total_size)}"
            QMessageBox.warning(self, "Espacio insuficiente", message)
            return False
        
        # Mostrar información detallada del cálculo
        if is_copying_active:
            logging.info("Cálculo basado en espacio final (durante copia activa):")
            logging.info(f"  - Espacio final anterior: {self.format_size(espacio_final_previo)}")
            logging.info(f"  - Tamaño a añadir: {self.format_size(total_size)}")
        
        # Hay suficiente espacio, continuar con el procesamiento
        space_data = {
            'free_space': espacio_disponible,
            'total_size': total_size
        }
        self.continue_processing_files(processed_files, drive_letter, space_data)
        return True
    def process_dropped_files(self, processed_files, drive_letter, shift_pressed, current_path, volume_name, dragging_folder):
        """
        Procesa los archivos arrastrados, copiándolos solo hasta donde la capacidad del disco lo permita.
        Args:
            processed_files (list): Lista de tuplas (file_path, file_size, destination_path) o QUrl
            drive_letter (str): Letra del disco de destino (ej. 'D:')
            shift_pressed (bool): Indica si la tecla Shift está presionada
            current_path (str): Ruta actual del disco
            volume_name (str): Nombre del volumen del disco
            dragging_folder (bool): Indica si se está arrastrando una carpeta
        """
        try:
            # Verificar si hay archivos para procesar
            if not processed_files:
                self.show_tooltip("No se encontraron archivos válidos para procesar.")
                return

            # --- ARREGLO: Convertir QUrl a tuplas si es necesario ---
            if processed_files and hasattr(processed_files[0], 'toLocalFile'):
                processed_files_tuplas = []
                for url in processed_files:
                    file_path = url.toLocalFile()
                    if os.path.isfile(file_path):
                        file_size = os.path.getsize(file_path)
                        destination_path = os.path.join(current_path, os.path.basename(file_path))
                        processed_files_tuplas.append((file_path, file_size, destination_path))
                    elif os.path.isdir(file_path):
                        for root, _, files in os.walk(file_path):
                            for file in files:
                                src_file = os.path.join(root, file)
                                rel_path = os.path.relpath(src_file, os.path.dirname(file_path))
                                dst_file = os.path.join(current_path, rel_path)
                                file_size = os.path.getsize(src_file)
                                processed_files_tuplas.append((src_file, file_size, dst_file))
                processed_files = processed_files_tuplas
            # --- FIN DEL ARREGLO ---

            # Eliminar notificación de copia pendiente
            try:
                from NOTIFICACION import remove_badge_from_disk
                remove_badge_from_disk(self.main_window, drive_letter)
                print(f"Badge eliminado para {drive_letter}")

                # Obtener la ruta correcta del archivo pending_copies.json
                if getattr(sys, 'frozen', False):
                    base_path = os.path.dirname(sys.executable)
                else:
                    base_path = os.path.dirname(os.path.abspath(__file__))
                pending_file = os.path.join(base_path, 'pending_copies.json')
                print(f"Buscando pending_copies.json en: {pending_file}")
                if os.path.exists(pending_file):
                    with open(pending_file, 'r', encoding='utf-8') as f:
                        pending_data = json.load(f)
                    modified = False
                    volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                    disk_key = f"{volume_name} ({drive_letter})"
                    # Buscar y eliminar las entradas para este disco
                    keys_to_delete = []
                    for key in pending_data.keys():
                        if disk_key in key or drive_letter in key:
                            keys_to_delete.append(key)
                            modified = True
                    # Eliminar las entradas encontradas
                    for key in keys_to_delete:
                        del pending_data[key]
                    # Guardar el archivo actualizado si hubo cambios
                    if modified:
                        with open(pending_file, 'w', encoding='utf-8') as f:
                            json.dump(pending_data, f, ensure_ascii=False, indent=4)
                        print(f"Copia pendiente eliminada para {drive_letter}")
            except Exception as e:
                print(f"Error eliminando notificación: {e}")
                traceback.print_exc()

            # Obtener el modo de pago actual
            from AJUSTES import load_config
            config = load_config()
            modo_pago = config.get('modo_pago', 'dispositivo')
            # Modo de pago (optimizado)

            # Definir extensiones de video permitidas
            video_extensions = {
                '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', 
                '.m4v', '.mpg', '.mpeg', '.3gp', '.ts', '.mts', '.m2ts'
            }
            
            # Inicializar estructuras si no existen
            if not hasattr(self.main_window, 'queues'):
                self.main_window.queues = {}
            if not hasattr(self.main_window, 'files_in_queue'):
                self.main_window.files_in_queue = set()
            if not hasattr(self.main_window, 'files_in_queue_by_drive'):
                self.main_window.files_in_queue_by_drive = {}

            # Verificar espacio disponible en el disco
            import shutil
            total, used, free_space = shutil.disk_usage(current_path)
            MARGEN_SEGURIDAD = 20 * 1024 * 1024  # 20MB en bytes
            espacio_disponible = max(0, free_space - MARGEN_SEGURIDAD)
            total_size = sum(file_size for _, file_size, _ in processed_files)

            # Mostrar barra de progreso
            self.main_window.queue_progress_bar.setFormat("Analizando archivos...")
            self.main_window.queue_progress_bar.setValue(0)
            self.main_window.queue_progress_bar.setVisible(True)
            files_added = 0
            duracion_total = 0
            total_files = 0
            espacio_acumulado = 0
            archivos_rechazados = []
            filtered_files = []
            archivos_realmente_agregados = []  # NUEVO: para sumar solo los realmente nuevos

            # Filtrar archivos que caben en el espacio disponible (sin ordenar)
            for file_path, file_size, destination_path in processed_files:
                if espacio_acumulado + file_size <= espacio_disponible:
                    filtered_files.append((file_path, file_size, destination_path))
                    espacio_acumulado += file_size
                else:
                    archivos_rechazados.append(file_path)
                    print(f"Archivo rechazado por falta de espacio: {file_path}")
            if not filtered_files:
                mensaje_error = f"No hay suficiente espacio en el disco {drive_letter}.\n"
                mensaje_error += f"Espacio disponible: {self.main_window.format_size(espacio_disponible)}\n"
                mensaje_error += f"Espacio requerido: {self.main_window.format_size(total_size)}"
                self.show_tooltip(mensaje_error)
                self.main_window.queue_progress_bar.setVisible(False)
                return

            # Mostrar advertencia si hay archivos rechazados
            if archivos_rechazados:
                mensaje_advertencia = f"Espacio insuficiente en el disco {drive_letter}.\n"
                mensaje_advertencia += f"Espacio disponible: {self.main_window.format_size(espacio_disponible)}\n"
                mensaje_advertencia += f"Espacio requerido: {self.main_window.format_size(total_size)}\n"
                mensaje_advertencia += f"Se copiarán {len(filtered_files)} de {len(processed_files)} archivos.\n"
                mensaje_advertencia += "Archivos no copiados:\n" + "\n".join(archivos_rechazados[:5])
                if len(archivos_rechazados) > 5:
                    mensaje_advertencia += f"\n...y {len(archivos_rechazados) - 5} más."
                self.show_tooltip(mensaje_advertencia)

            # Procesar archivos filtrados
            espacio_acumulado_nuevos = 0  # NUEVO: solo suma de los realmente nuevos
            for file_path, file_size, destination_path in filtered_files:
                # Verificar si el archivo ya existe con el mismo tamaño
                if os.path.exists(destination_path):
                    try:
                        existing_size = os.path.getsize(destination_path)
                        if existing_size == file_size:
                            print(f"Archivo ya existe con el mismo tamaño: {destination_path} ({existing_size} bytes)")
                            continue
                        else:
                            print(f"Archivo existe pero con tamaño diferente: {destination_path} (existente: {existing_size}, nuevo: {file_size})")
                    except OSError as e:
                        print(f"Error verificando archivo existente {destination_path}: {e}")
                        pass
                
                # Verificar si el archivo se está copiando actualmente
                if (hasattr(self.main_window, 'current_copying_file') and 
                    drive_letter in self.main_window.current_copying_file and 
                    self.main_window.current_copying_file[drive_letter] is not None):
                    current_copying = self.main_window.current_copying_file[drive_letter]
                    if (isinstance(current_copying, tuple) and len(current_copying) >= 2):
                        current_dest = current_copying[1]
                        # Normalizar rutas para comparación
                        current_dest_norm = os.path.normpath(current_dest).lower()
                        destination_path_norm = os.path.normpath(destination_path).lower()
                        if current_dest_norm == destination_path_norm:
                            print(f"Archivo se está copiando actualmente, omitiendo: {destination_path}")
                            continue
                
                if not os.path.exists(destination_path):
                    print(f"Archivo no existe, se agregará: {destination_path}")
                # Verificar si ya está en cola para este disco específico
                queue_key = (file_path, destination_path)
                already_in_queue = False
                
                # Verificar en files_in_queue global
                if queue_key in self.main_window.files_in_queue:
                    already_in_queue = True
                
                # Verificar en files_in_queue_by_drive específico del disco
                if (hasattr(self.main_window, 'files_in_queue_by_drive') and 
                    drive_letter in self.main_window.files_in_queue_by_drive and 
                    queue_key in self.main_window.files_in_queue_by_drive[drive_letter]):
                    already_in_queue = True
                
                # Agregar a la cola solo si no está ya en cola
                if drive_letter not in self.main_window.queues:
                    self.main_window.queues[drive_letter] = queue.Queue()
                
                if not already_in_queue:
                    print(f"Agregando archivo a la cola: {file_path} -> {destination_path}")
                    self.main_window.queues[drive_letter].put(queue_key)
                    if drive_letter not in self.main_window.files_in_queue_by_drive:
                        self.main_window.files_in_queue_by_drive[drive_letter] = set()
                    self.main_window.files_in_queue_by_drive[drive_letter].add(queue_key)
                    self.main_window.files_in_queue.add(queue_key)
                    files_added += 1
                    espacio_acumulado_nuevos += file_size  # SOLO suma los realmente nuevos
                    archivos_realmente_agregados.append((file_path, file_size, destination_path))
                    # Contabilizar total de archivos si es modo ficheros
                    if modo_pago == "ficheros":
                        extension = os.path.splitext(file_path)[1].lower()
                        if extension in video_extensions:
                            total_files += 1
                    # Calcular duración si es modo duración
                    if modo_pago == "duracion":
                        extension = os.path.splitext(file_path)[1].lower()
                        if extension in video_extensions:
                            duracion = self.main_window.get_video_duration(file_path)
                            if duracion is not None:
                                duracion_total += duracion
                else:
                    print(f"Archivo ya está en cola, omitiendo: {file_path} -> {destination_path}")

            # Actualizar UI
            if files_added > 0:
                # Actualizar tamaño total según el modo
                if modo_pago == "duracion":
                    self.main_window.update_total_size(drive_letter, espacio_acumulado_nuevos, force_recalculate=True, duracion_min=duracion_total)
                elif modo_pago == "ficheros": 
                    self.main_window.update_total_size(drive_letter, espacio_acumulado_nuevos, force_recalculate=True, total_files=files_added)
                else:  # modo dispositivo
                    self.main_window.update_total_size(drive_letter, espacio_acumulado_nuevos, force_recalculate=True)
                self.main_window.start_copy_thread(drive_letter)
                self.main_window.update_progress_signal.emit(drive_letter, 0)
                self.main_window.update_file_progress_signal.emit(drive_letter, 0)
                self.main_window.update_speed_signal.emit()
            self.main_window.queue_progress_bar.setVisible(False)
            from ZETACOPY import play_drag_sound
            play_drag_sound(files_added)
        except Exception as e:
            print(f"Error procesando archivos: {e}")
            traceback.print_exc()
            self.main_window.queue_progress_bar.setVisible(False)

    def continue_processing_files(self, processed_files, drive_letter, space_data):
        try:
            print("Iniciando verificación de archivos...")
            print(f"Número de archivos a procesar: {len(processed_files)}")
            if len(processed_files) > 1:  # Verificación de licencia para múltiples archivos
                try:
                    # Obtener ruta del archivo de licencia
                    if getattr(sys, 'frozen', False):
                        exe_path = os.path.dirname(os.path.abspath(sys.executable))
                    else:
                        exe_path = os.path.dirname(os.path.abspath(__file__))
                    license_path = os.path.join(exe_path, "license.dat")
                    print(f"Buscando licencia en: {license_path}")
                    if os.path.exists(license_path):
                        print("Archivo de licencia encontrado")
                        with open(license_path, "rb") as license_file:
                            combined_data = license_file.read()
                            key, encrypted = combined_data.split(b'|', 1)
                            fernet = Fernet(key)
                            decrypted = fernet.decrypt(encrypted).decode()
                            date_str = decrypted.split('||')[-1]
                            expiration_date = datetime.fromisoformat(date_str)
                            print(f"Fecha de expiración de licencia: {expiration_date}")
                            manipulation_detected = False
                            for file_path, _, _ in processed_files:
                                if os.path.exists(file_path):
                                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                                    print(f"Archivo: {file_path}")
                                    print(f"Fecha del archivo: {file_time}")
                                    if file_time > expiration_date:
                                        print(f"¡ALERTA! Archivo con fecha futura detectado!")
                                        print(f"Fecha del archivo: {file_time}")
                                        print(f"Fecha de expiración: {expiration_date}")
                                        manipulation_detected = True
                                        break
                            if manipulation_detected:
                                print("Creando diálogo de advertencia...")
                                dialog = QDialog(self.main_window)
                                dialog.setWindowTitle("Advertencia de Licencia")
                                dialog.setFixedSize(400, 200)
                                dialog.setStyleSheet("""
                                    QDialog {
                                        background-color: #2b2b2b;
                                        color: white;
                                    }
                                    QLabel {
                                        color: white;
                                        font-size: 12px;
                                    }
                                    QPushButton {
                                        background-color: #0078d7;
                                        color: white;
                                        border: none;
                                        padding: 8px 16px;
                                        border-radius: 4px;
                                    }
                                    QPushButton:hover {
                                        background-color: #1084d7;
                                    }
                                """)
                                layout = QVBoxLayout()
                                warning_label = QLabel(
                                    "ADVERTENCIA:\n\n"
                                    "Se ha detectado un intento de uso no autorizado de la licencia.\n"
                                    "Los archivos que intenta copiar tienen una fecha posterior\n"
                                    "a la fecha de expiración de su licencia.\n\n"
                                    "Esta acción invalidará su licencia actual."
                                )
                                warning_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                                layout.addWidget(warning_label)
                                accept_button = QPushButton("Aceptar")
                                accept_button.setCursor(Qt.CursorShape.PointingHandCursor)
                                layout.addWidget(accept_button)
                                dialog.setLayout(layout)
                                def on_accept():
                                    try:
                                        print("Botón Aceptar presionado, preparando borrado de licencia...")
                                        license_path = os.path.join(exe_path, "license.dat")
                                        import gc
                                        gc.collect()
                                        for obj in gc.get_objects():
                                            try:
                                                if hasattr(obj, 'name') and obj.name == license_path:
                                                    obj.close()
                                            except:
                                                pass
                                        if os.path.exists(license_path):
                                            try:
                                                os.chmod(license_path, 0o777)
                                                os.remove(license_path)
                                                print("Licencia eliminada exitosamente")
                                            except Exception as e:
                                                print(f"Error eliminando licencia: {e}")
                                        dialog.accept()
                                        QTimer.singleShot(300, lambda: show_license_activation_dialog(self.main_window, self.main_window))
                                    except Exception as e:
                                        print(f"Error en el proceso: {e}")
                                        traceback.print_exc()
                                        dialog.accept()
                                accept_button.clicked.connect(on_accept)
                                print("Mostrando diálogo...")
                                dialog.exec()
                                print("Diálogo cerrado")
                                return
                            else:
                                print("No se detectó manipulación de fecha")
                    else:
                        print("No se encontró archivo de licencia")
                except Exception as e:
                    print(f"Error en verificación de fechas: {e}")
                    traceback.print_exc()

            # Continuar con el procesamiento normal
            total, used, free_space = space_data['total'], space_data['used'], space_data['free_space']
            total_files = len(processed_files)
            files_processed = 0
            last_progress = 0
            files_per_percent = max(1, total_files // 100)
            is_copying = self.main_window.is_copying(drive_letter)
            if drive_letter not in self.main_window.total_sizes:
                self.main_window.total_sizes[drive_letter] = 0

            # Definir margen de seguridad de 20MB
            MARGEN_SEGURIDAD = 20 * 1024 * 1024  # 20MB en bytes

            # Calcular espacio realmente disponible
            espacio_usado_por_copias = self.main_window.total_sizes.get(drive_letter, 0)
            espacio_copiado = self.main_window.copy_bytes_copied.get(drive_letter, 0)
            espacio_pendiente = espacio_usado_por_copias - espacio_copiado
            if is_copying:
                try:
                    final_space_text = self.main_window.final_space_text.get(drive_letter, '0')
                    value, unit = final_space_text.split()
                    value = float(value)
                    multipliers = {
                        'MB': 1024 * 1024,
                        'GB': 1024 * 1024 * 1024,
                        'TB': 1024 * 1024 * 1024 * 1024,
                        'KB': 1024
                    }
                    espacio_disponible = 0
                    for unit_key, multiplier in multipliers.items():
                        if unit_key in unit:
                            espacio_disponible = int(value * multiplier)
                            break
                    else:
                        raise ValueError(f"Unidad desconocida en final_space_text: {unit}")
                    espacio_disponible = max(0, espacio_disponible - MARGEN_SEGURIDAD)
                except Exception as e:
                    print(f"Error al calcular espacio disponible desde final_space_text: {e}")
                    espacio_disponible = max(0, free_space - espacio_pendiente - MARGEN_SEGURIDAD)
            else:
                espacio_disponible = max(0, free_space - espacio_pendiente - MARGEN_SEGURIDAD)
            print(f"Espacio libre inicial: {self.main_window.format_size(free_space)}")
            print(f"Margen de seguridad: {self.main_window.format_size(MARGEN_SEGURIDAD)}")
            print(f"Espacio usado por copias pendientes: {self.main_window.format_size(espacio_pendiente)}")
            print(f"Espacio realmente disponible: {self.main_window.format_size(espacio_disponible)}")
            new_size = 0
            files_to_process = []
            skipped_files = 0
            already_in_queue = 0
            total_files = 0
            files_added = 0

            # Crear un conjunto de archivos en cola para búsqueda rápida
            if drive_letter not in self.main_window.files_in_queue_by_drive:
                self.main_window.files_in_queue_by_drive[drive_letter] = set()
            files_in_queue_set = {os.path.normpath(src).lower() 
                                for src, _ in self.main_window.files_in_queue_by_drive.get(drive_letter, set())}

            # Crear estructura de directorios
            directories_to_create = set()
            for file_path, _, destination_path in processed_files:
                if os.path.isfile(file_path):
                    parent_dir = os.path.dirname(destination_path)
                    directories_to_create.add(parent_dir)
                elif os.path.isdir(file_path):
                    source_base = os.path.dirname(file_path)
                    for root, _, _ in os.walk(file_path):
                        rel_path = os.path.relpath(root, source_base)
                        dest_dir = os.path.join(os.path.dirname(destination_path), rel_path)
                        directories_to_create.add(dest_dir)
            for directory in sorted(directories_to_create, key=len):
                try:
                    if not os.path.exists(directory):
                        os.makedirs(directory, exist_ok=True)
                        print(f"Creado directorio: {directory}")
                except Exception as e:
                    print(f"Error creando directorio {directory}: {e}")
                    continue

            # Procesar archivos y carpetas
            for file_path, file_size, destination_path in processed_files:
                if self.is_in_queue(file_path, drive_letter):
                    print(f"Archivo ya está en la cola: {file_path}")
                    already_in_queue += 1
                    self.show_tooltip(f"El archivo {os.path.basename(file_path)} ya está en la cola")
                    continue
                if os.path.exists(destination_path):
                    try:
                        if os.path.getsize(destination_path) == os.path.getsize(file_path):
                            print(f"Archivo ya existe con el mismo tamaño: {destination_path}")
                            skipped_files += 1
                            self.show_tooltip(f"El archivo {os.path.basename(file_path)} ya existe en el destino", duration=5000)
                            QThread.msleep(100)
                            continue
                    except OSError:
                        pass
                if os.path.isfile(file_path):
                    files_to_process.append((file_path, file_size, destination_path))
                    new_size += file_size
                    total_files += 1
                elif os.path.isdir(file_path):
                    dir_size = 0
                    dir_files = []
                    for root, _, files in os.walk(file_path):
                        files.sort()
                        for file in files:
                            src_file = os.path.join(root, file)
                            rel_path = os.path.relpath(src_file, os.path.dirname(file_path))
                            dst_file = os.path.join(destination_path, rel_path)
                            if self.is_in_queue(src_file, drive_letter):
                                print(f"Archivo en carpeta ya está en la cola: {src_file}")
                                already_in_queue += 1
                                continue
                            if os.path.exists(dst_file):
                                try:
                                    if os.path.getsize(dst_file) == os.path.getsize(src_file):
                                        print(f"Archivo en carpeta ya existe con el mismo tamaño: {dst_file}")
                                        skipped_files += 1
                                        continue
                                except OSError:
                                    pass
                            try:
                                file_size = os.path.getsize(src_file)
                                dir_size += file_size
                                dir_files.append((src_file, file_size, dst_file))
                                total_files += 1
                            except OSError:
                                continue
                    new_size += dir_size
                    files_to_process.extend(dir_files)

            # Verificar si hay suficiente espacio
            if new_size > espacio_disponible:
                if len(files_to_process) == 1:
                    mensaje_error = f"No hay suficiente espacio en el disco {drive_letter}.\n"
                    mensaje_error += f"Espacio disponible: {self.main_window.format_size(espacio_disponible)}\n"
                    mensaje_error += f"Espacio requerido: {self.main_window.format_size(new_size)}"
                    self.show_tooltip(mensaje_error)
                    self.main_window.update_free_space_display(drive_letter, espacio_disponible)
                    QTimer.singleShot(500, lambda: self.main_window.queue_progress_bar.setVisible(False))
                    return
                else:
                    mensaje_advertencia = f"Espacio insuficiente en el disco {drive_letter}.\n"
                    mensaje_advertencia += f"Espacio disponible: {self.main_window.format_size(espacio_disponible)}\n"
                    mensaje_advertencia += f"Espacio requerido: {self.main_window.format_size(new_size)}\n"
                    mensaje_advertencia += "Se procesarán los archivos hasta donde alcance el espacio."
                    self.show_tooltip(mensaje_advertencia)
            if drive_letter not in self.main_window.queues:
                self.main_window.queues[drive_letter] = queue.Queue()
                thread = threading.Thread(target=self.main_window.process_file_queue, args=(drive_letter,))
                thread.start()
                self.main_window.threads[drive_letter] = thread
            self.main_window.queue_progress_bar.setFormat(f"{files_processed} | {total_files}")
            self.main_window.queue_progress_bar.setVisible(True)

            def update_progress():
                nonlocal files_processed, last_progress
                files_processed += 1
                current_progress = min(100, files_processed // files_per_percent)
                if current_progress > last_progress:
                    self.main_window.queue_progress_bar.setValue(current_progress)
                    self.main_window.queue_progress_bar.setFormat(f"{files_processed} | {total_files}")
                    QApplication.processEvents()
                    last_progress = current_progress
                print(f"Archivos procesados: {files_processed}")

            espacio_acumulado = 0
            archivos_rechazados = []
            archivos_procesados = []
            
            # Opcional: ordenar archivos por tamaño para priorizar archivos pequeños
            # files_to_process.sort(key=lambda x: x[1])
            for file_path, file_size, destination_path in files_to_process:
                # Verificar si este archivo específico cabe en el espacio disponible
                if espacio_acumulado + file_size <= espacio_disponible:
                    try:
                        os.makedirs(os.path.dirname(destination_path), exist_ok=True)
                    except PermissionError:
                        print(f"Sin permiso para crear el directorio: {os.path.dirname(destination_path)}")
                        continue
                    
                    # Este archivo cabe, lo agregamos a la cola
                    self.main_window.queues[drive_letter].put((file_path, destination_path))
                    
                    # Agregar a la cola específica del disco
                    if drive_letter not in self.main_window.files_in_queue_by_drive:
                        self.main_window.files_in_queue_by_drive[drive_letter] = set()
                    self.main_window.files_in_queue_by_drive[drive_letter].add((file_path, destination_path))
                    
                    # Mantener también la cola global por compatibilidad
                    self.main_window.files_in_queue.add((file_path, destination_path))
                    files_added += 1
                    espacio_acumulado += file_size
                    archivos_procesados.append((file_path, file_size, destination_path))
                    update_progress()
                else:
                    # Este archivo no cabe, lo agregamos a la lista de rechazados
                    archivos_rechazados.append((file_path, file_size, destination_path))

            # Actualizar el espacio final
            if files_added > 0:
                espacio_final = espacio_disponible - espacio_acumulado
                print(f"Espacio acumulado por nuevos archivos: {self.main_window.format_size(espacio_acumulado)}")
                print(f"Espacio final calculado: {self.main_window.format_size(espacio_final)}")
                if espacio_final < 0:
                    print("ADVERTENCIA: Espacio final negativo detectado")
                    espacio_final = 0
                self.main_window.final_space_text[drive_letter] = self.main_window.format_size(espacio_final)
                self.main_window.update_total_size(drive_letter, espacio_acumulado)
            if archivos_rechazados:
                mensaje_rechazo = f"No hay espacio suficiente para {len(archivos_rechazados)} archivo(s):\n"
                for path, size, _ in archivos_rechazados[:5]:
                    mensaje_rechazo += f"- {os.path.basename(path)} ({self.main_window.format_size(size)})\n"
                if len(archivos_rechazados) > 5:
                    mensaje_rechazo += f"... y {len(archivos_rechazados) - 5} archivos más"
                self.show_tooltip(mensaje_rechazo)
            self.main_window.queue_progress_bar.setValue(100)
            QApplication.processEvents()
            if files_added > 0:
                self.main_window.update_progress_bar(drive_letter, 0)
                if not is_copying:
                    self.main_window.start_copy_thread(drive_letter)
            QTimer.singleShot(500, lambda: self.main_window.queue_progress_bar.setVisible(False))
            if files_added > 0:
                # Reproducir sonido solo una vez por operación de arrastre
                if not hasattr(self.main_window, '_sound_played') or not self.main_window._sound_played:
                    self.main_window.play_drag_sound(files_added)
                    self.main_window._sound_played = True
                mensaje = f"SE AGREGARON {files_added} ARCHIVOS A LA COLA\n"
                if skipped_files > 0:
                    mensaje += f"SE OMITIERON {skipped_files} ARCHIVOS (YA EXISTEN EN DESTINO)\n"
                if already_in_queue > 0:
                    mensaje += f"SE OMITIERON {already_in_queue} ARCHIVOS (YA EN COLA)\n"
                if archivos_rechazados:
                    mensaje += f"SE RECHAZARON {len(archivos_rechazados)} ARCHIVOS (ESPACIO INSUFICIENTE)\n"
                mensaje += f"ESPACIO TOTAL UTILIZADO: {self.main_window.format_size(espacio_acumulado)}"
                self.show_tooltip(mensaje)
            else:
                # Reproducir sonido de error solo una vez por operación
                if not hasattr(self.main_window, '_sound_played') or not self.main_window._sound_played:
                    self.main_window.play_drag_sound(0)
                    self.main_window._sound_played = True
                if skipped_files > 0 or already_in_queue > 0:
                    mensaje = "NO SE AGREGARON ARCHIVOS NUEVOS A LA COLA:\n"
                    if skipped_files > 0:
                        mensaje += f"{skipped_files} archivos ya existen en destino\n"
                    if already_in_queue > 0:
                        mensaje += f"{already_in_queue} archivos ya están en cola"
                    self.show_tooltip(mensaje)
                else:
                    self.show_tooltip("No se pudieron agregar archivos nuevos.")
            self.main_window.save_pending_copies()
        except Exception as e:
            print(f"Error general en continue_processing_files: {e}")
            traceback.print_exc()

    def count_total_files(self, processed_files):
        total = 0
        for file_path, _, _ in processed_files:
            if os.path.isdir(file_path):
                for root, _, files in os.walk(file_path):
                    total += len(files)
            else:
                total += 1
        return total

    def is_in_queue(self, file_path, drive_letter):
        files_in_queue_copy = set(self.main_window.files_in_queue)
        # Normalizar la ruta de entrada
        normalized_file_path = os.path.normpath(file_path)
        for queued_file, queued_dest in files_in_queue_copy:
            # Normalizar las rutas en la cola
            normalized_queued_file = os.path.normpath(queued_file)
            normalized_queued_dest = os.path.normpath(queued_dest)
            
            # Asegurar formato consistente para la letra de unidad
            if drive_letter.endswith(':') and not normalized_queued_dest.startswith(drive_letter + '\\'):
                # Convertir "L:" a "L:\" para comparación
                drive_check = drive_letter + '\\'
            else:
                drive_check = drive_letter
                
            if normalized_queued_dest.startswith(drive_check) and (
                normalized_queued_file == normalized_file_path or 
                (os.path.isdir(normalized_file_path) and normalized_queued_file.startswith(normalized_file_path))
            ):
                return True
        return False

    def show_tooltip(self, message, duration=3000):
        pos = QCursor.pos()
        showCustomTooltip(message, pos, self.main_window, duration)
    
    def update_copy_list_txt(self, drive_letter, volume_name, new_files):
        txt_file_name = f"{volume_name}.txt"
        txt_file_path = os.path.join(drive_letter, txt_file_name)
        existing_files = []
        if os.path.exists(txt_file_path):
            with open(txt_file_path, 'r', encoding='utf-8') as txt_file:
                lines = txt_file.readlines()
                i = 0
                while i < len(lines):
                    if lines[i].startswith("Source:"):
                        source = lines[i].strip().split(": ", 1)[1]
                        destination = lines[i+1].strip().split(": ", 1)[1]
                        existing_files.append({'source': source, 'destination': destination})
                        i += 3  # Saltar la línea en blanco
                    else:
                        i += 1
        all_files = existing_files + new_files
        seen = set()
        unique_files = []
        for file in all_files:
            file_tuple = (file['source'], file['destination'])
            if file_tuple not in seen:
                seen.add(file_tuple)
                unique_files.append(file)
        with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
            for file_info in unique_files:
                txt_file.write(f"Source: {file_info['source']}\n")
                txt_file.write(f"Destination: {file_info['destination']}\n")
                txt_file.write('\n')
        print(f"Lista de copia actualizada en {txt_file_path}")
    
    def on_item_double_clicked(self, item):
        try:
            # Verificar licencia antes de navegar
            if not hasattr(self.main_window, 'license_active') or not self.main_window.license_active:
                print("Navegación bloqueada: licencia no activa")
                # Mostrar diálogo de activación de licencia
                from ACTIVATE import show_license_activation_dialog
                show_license_activation_dialog(self.main_window, self.main_window)
                return
                
            drive_letter = item.text().split(' ')[-1].strip('()')
            
            # Pausa rápida si hay copia en curso en este disco
            if (drive_letter in self.main_window.threads and 
                self.main_window.threads[drive_letter].is_alive()):
                print(f"Pausa rápida para navegación en {drive_letter}")
                self.main_window.threads[drive_letter].paused = True
            
                # Navegar
                QTimer.singleShot(0, lambda: self.main_window.show_drive_contents(drive_letter))
                
                # Reanudar inmediatamente después de cargar el contenido
                if drive_letter in self.main_window.threads:
                    print(f"Reanudando copia en {drive_letter}")
                    self.main_window.threads[drive_letter].paused = False
            else:
                self.main_window.show_drive_contents(drive_letter)
        except Exception as e:
            print(f"Error en doble clic: {e}")
 
    def set_show_disk_lines(self, show):
        self.show_disk_lines = show
        self.update()
    
    def paintEvent(self, event):
        super().paintEvent(event)
        if not self.show_disk_lines:
            return
        painter = QPainter(self.viewport())
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        from PyQt6.QtGui import QColor # Importar QPen y QColor
        line_color = QColor(255, 255, 255, 30) # El color y transparencia se mantienen
        horizontal_margin = 15 # Margen horizontal para la línea (ajusta este valor)
        line_height = 1 # Altura del rectángulo que simulará la línea (en píxeles)
        vertical_offset = 0.5 # Offset vertical para mover la línea más abajo (ajusta este valor a un valor decimal)
        for i in range(self.count()):
            rect = self.visualItemRect(self.item(i))
            painter.setPen(Qt.PenStyle.NoPen) # Sin borde para el rectángulo
            painter.setBrush(line_color) # Rellenar el rectángulo con el color de la línea
            line_rect = QRectF( # Cambiado a QRectF para soportar valores flotantes
                rect.left() + horizontal_margin,
                rect.bottom() + vertical_offset, # Mover la línea más abajo usando el offset
                rect.width() - (2 * horizontal_margin),
                line_height
            )
            painter.drawRect(line_rect)

    def contextMenuEvent(self, event):
        selected_items = self.selectedItems()
        if selected_items:
            item = selected_items[0]
            item_widget = self.itemWidget(item)
            if item_widget and hasattr(item_widget, 'volume_label'):
                full_text = item_widget.volume_label.text()
                parts = full_text.split(' (')
                if len(parts) >= 2:
                    volume_name = parts[0]
                    drive_info = parts[1].split(')')[0]
                    drive_letter = drive_info.split('|')[0].strip() if '|' in drive_info else drive_info
                else:
                    volume_name = full_text
                    drive_letter = ""
                volume_name = volume_name.split('|')[0].strip()
                full_volume_name = f"{volume_name} ({drive_letter})"
            else:
                full_volume_name = "Volumen"
                drive_letter = ""
            from CONTEXTUAL import create_context_menu  # Asegúrate de tener este import arriba si no está
            context_menu, reset_alias_action, create_registry_action, hide_action, explore_action, \
            stop_copy_action, calculate_payment_action, port_mapping_action, rename_action, \
            temp_name_action, set_matrix_action, eject_action = create_context_menu(self, full_volume_name, drive_letter)
            action = context_menu.exec(self.mapToGlobal(event.pos()))
            if action == reset_alias_action:
                self.main_window.reset_temporary_alias(self)
            elif action == create_registry_action:
                self.main_window.create_registry(drive_letter)
            elif action == hide_action:
                self.main_window.hide_drive(self, item)
            elif action == explore_action:
                self.main_window.explore_drive(drive_letter)
            elif action == stop_copy_action:
                self.main_window.botones.clear_queue()
            elif action == calculate_payment_action:
                mensaje, discos_exentos = self.main_window.toggle_payment_calculation(drive_letter, self.main_window)
                self.main_window.discos_exentos = discos_exentos
                self.main_window.actualizar_vista_discos()
            elif action == port_mapping_action:
                self.main_window.map_port()
            elif action == rename_action:
                self.main_window.show_rename_dialog()
            elif action == temp_name_action:
                self.main_window.edit_temporary_name()
            elif action == eject_action:
                self.main_window.expulsar_disco()
            elif action == set_matrix_action:
                # Implementar la acción para establecer MATRIX
                try:
                    # Obtener información del disco
                    volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
                    
                    # Crear la entrada para config.json
                    entry = {
                        'directory': f"{drive_letter}\\",
                        'directory_name': '',
                        'drive_letter': drive_letter,
                        'drive_name': volume_name,
                        'timestamp': time.time(),
                        'selected': True  # Establecer como seleccionado
                    }
                    
                    # Cargar configuración existente
                    if getattr(sys, 'frozen', False):
                        app_path = os.path.dirname(sys.executable)
                    else:
                        app_path = os.path.dirname(os.path.abspath(__file__))
                    
                    config_path = os.path.join(app_path, 'config.json')
                    config = {}
                    if os.path.exists(config_path):
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                    
                    # Inicializar la sección de matriz si no existe
                    if 'matrix' not in config:
                        config['matrix'] = []
                    
                    # Verificar si el disco ya existe en la lista
                    disk_exists = False
                    for existing_entry in config['matrix']:
                        # Desmarcar cualquier entrada seleccionada previamente
                        existing_entry['selected'] = False
                        if existing_entry.get('directory', '') == f"{drive_letter}\\":
                            # Actualizar la entrada existente
                            existing_entry['selected'] = True
                            existing_entry['timestamp'] = time.time()
                            disk_exists = True
                    
                    # Añadir nueva entrada si no existe
                    if not disk_exists:
                        config['matrix'].append(entry)
                    
                    # Guardar la configuración
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=4, ensure_ascii=False)
                    
                    # Mostrar tooltip de confirmación
                    tooltip_text = f"✓ {volume_name} ({drive_letter}) establecido como MATRIX"
                    showCustomTooltip.showText(QCursor.pos(), tooltip_text, self.main_window)
                    QTimer.singleShot(2000, QToolTip.hideText)
                except Exception as e:
                    print(f"Error al establecer MATRIX: {e}")
                    import traceback
                    traceback.print_exc()

    def mouseMoveEvent(self, event):
        item = self.itemAt(event.pos())
        if item and self.is_cursor_over_icon(event.pos(), item):
            self.setCursor(Qt.CursorShape.PointingHandCursor)
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)
        super().mouseMoveEvent(event)

    def leaveEvent(self, event):
        self.setCursor(Qt.CursorShape.ArrowCursor) # Restaurar cursor cuando el mouse sale del widget
        super().leaveEvent(event)

    def is_cursor_over_icon(self, pos, item):
        rect = self.visualItemRect(item)
        icon_rect = QRect(rect.left(), rect.top(), 30, rect.height())
        return icon_rect.contains(pos)

    def mousePressEvent(self, event): # MUESTRA CON CLIC CENTRAL EN EL DISCO EL ICONO DE PAGO
        item = self.itemAt(event.pos())
        if not item:
            self.clearSelection()
            event.accept()
            return
            
        # Añadir lógica para el clic central
        if event.button() == Qt.MouseButton.MiddleButton:
            drive_letter = item.text().split(' ')[-1].strip('()')
            widget = self.itemWidget(item)
            if widget and hasattr(widget, 'volume_label'):
                # Verificar si ya existe el icono de pago
                payment_icon = widget.findChild(QLabel, "payment_icon")
                
                if payment_icon:
                    # Si el icono existe, ocultarlo
                    payment_icon.deleteLater()
                else:
                    # Verificar si el disco está copiando antes de mostrar el icono
                    if self.main_window.is_copying(drive_letter):
                        # Importar y usar el botón de pago desde CREAR.py
                        from CREAR import boton_pago
                        
                        # Crear label para el icono de pago
                        payment_icon = QLabel(widget)
                        payment_icon.setObjectName("payment_icon")
                        
                        # Configurar el icono
                        pixmap = boton_pago(size=18).pixmap(18, 18)
                        payment_icon.setPixmap(pixmap)
                        
                        # Insertar el icono después del texto del volumen
                        layout = widget.layout()
                        layout.insertWidget(1, payment_icon)  # Insertar después del volume_label
                        
                        # Agregar sombra
                        shadow = QGraphicsDropShadowEffect()
                        shadow.setBlurRadius(8)
                        shadow.setXOffset(0)
                        shadow.setYOffset(2)
                        shadow.setColor(QColor(0, 0, 0, 120))
                        payment_icon.setGraphicsEffect(shadow)
                return event.accept()

        super().mousePressEvent(event)

    def queue_file(self, source_path, dest_path, drive_letter):
        """Agrega un archivo a la cola de copia"""
        if drive_letter not in self.queues:
            self.queues[drive_letter] = queue.Queue()
        
        # Normalizar las rutas
        source_path = os.path.normpath(source_path)
        # Asegurar formato consistente para la ruta de destino
        if drive_letter.endswith(':') and not dest_path.startswith(drive_letter + '\\'):
            dest_path = drive_letter + '\\' + dest_path[len(drive_letter) + 1:]
        dest_path = os.path.normpath(dest_path)
        if not os.path.exists(source_path):
            print(f"Archivo origen no existe: {source_path}")
            return
        try:
            file_size = os.path.getsize(source_path)
            if (source_path, dest_path) in self.files_in_queue:
                print(f"Archivo ya está en la cola: {source_path}")
                return
            if not hasattr(self, 'pending_files_by_drive'):
                self.pending_files_by_drive = {}
            if drive_letter not in self.pending_files_by_drive:
                self.pending_files_by_drive[drive_letter] = []
            self.pending_files_by_drive[drive_letter].append((source_path, dest_path, file_size))
            self.files_in_queue.add((source_path, dest_path))
            self.update_total_size(drive_letter, file_size)
            if drive_letter not in self.threads or not self.threads[drive_letter].is_alive():
                self._sort_and_update_queue(drive_letter)
                thread = threading.Thread(target=self.process_file_queue, args=(drive_letter,))
                thread.do_run = True
                thread.start()
                self.threads[drive_letter] = thread
        except Exception as e:
            print(f"Error al agregar archivo a la cola: {e}")

    def _sort_and_update_queue(self, drive_letter):
        """Procesa los archivos pendientes respetando el orden exacto del explorador de Windows"""
        if not hasattr(self, 'pending_files_by_drive') or drive_letter not in self.pending_files_by_drive:
            return
        try:
            # Crear una nueva cola
            new_queue = queue.Queue()
            
            # Agrupar archivos por carpeta de origen
            files_by_folder = {}
            for file_info in self.pending_files_by_drive[drive_letter]:
                source_path, dest_path, file_size = file_info
                source_dir = os.path.dirname(source_path)
                if source_dir not in files_by_folder:
                    files_by_folder[source_dir] = []
                files_by_folder[source_dir].append(file_info)
            
            # Usar el orden del sistema de archivos de Windows
            for source_dir, files in files_by_folder.items():
                # Obtener metadatos de los archivos
                files_with_metadata = []
                for file_info in files:
                    source_path, dest_path, file_size = file_info
                    try:
                        # Usamos la fecha de creación como criterio principal (como suele hacer Windows)
                        created_time = os.path.getctime(source_path)
                        files_with_metadata.append((file_info, created_time))
                    except:
                        # Si falla, añadimos con tiempo 0 para mantenerlo en la lista
                        files_with_metadata.append((file_info, 0))
                
                # Ordenar por fecha de creación (como suele hacer Windows por defecto)
                files_with_metadata.sort(key=lambda x: x[1])
                
                # Añadir a la cola en el orden determinado
                for file_info, _ in files_with_metadata:
                    source_path, dest_path, _ = file_info
                    new_queue.put((source_path, dest_path))
            
            # Combinar con la cola existente
            old_queue = self.queues[drive_letter]
            temp_queue = queue.Queue()
            
            # Añadir primero los nuevos archivos
            while not new_queue.empty():
                temp_queue.put(new_queue.get())
            
            # Añadir los archivos existentes después
            while not old_queue.empty():
                temp_queue.put(old_queue.get())
            self.queues[drive_letter] = temp_queue
            file_count = len(self.pending_files_by_drive[drive_letter])
            print(f"Cola organizada para {drive_letter}: {file_count} archivos respetando orden del explorador de Windows")
            self.pending_files_by_drive[drive_letter] = []
        except Exception as e:
            print(f"Error al organizar la cola: {e}")
            import traceback
            traceback.print_exc()
