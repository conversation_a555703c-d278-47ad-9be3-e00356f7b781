# -*- mode: python ; coding: utf-8 -*-

import os
import sys
import ctypes
import shutil
import glob
from PyInstaller.utils.hooks import collect_data_files, collect_submodules
from PyInstaller.building.build_main import Analysis, PYZ, EXE
from PyInstaller.building.datastruct import TOC
import time
import hashlib
import random
import winreg
import psutil
import traceback
import wmi
import struct
import marshal
import zlib
import base64
import string
from threading import Thread

# Verificación de seguridad en runtime_hook
with open('runtime_hook.py', 'w', encoding='utf-8') as f:
    f.write(r"""
import sys
import time
import os
import ctypes
import platform
import hashlib
import random
import winreg
import psutil
import traceback
import wmi
import struct
import marshal
import zlib
import base64

def check_debugger():
    try:
        # Detectar debuggers comunes
        kernel32 = ctypes.windll.kernel32
        if kernel32.IsDebuggerPresent():
            return True
            
        # Verificar procesos sospechosos
        suspicious_processes = [
            "ollydbg.exe", "x64dbg.exe", "x32dbg.exe",
            "ida.exe", "ida64.exe", "immunity debugger.exe"
        ]
        
        for proc in psutil.process_iter(['name']):
            if proc.info['name'].lower() in suspicious_processes:
                return True
                
        return False
    except:
        return False

def check_virtual_machine():
    try:
        # Detectar entornos virtuales
        c = wmi.WMI()
        for item in c.Win32_ComputerSystem():
            if "virtual" in item.Model.lower():
                return True
                
        # Verificar procesos de VM
        vm_processes = [
            "vmtoolsd.exe", "vmwaretray.exe",
            "vboxservice.exe", "vboxtray.exe"
        ]
        
        for proc in psutil.process_iter(['name']):
            if proc.info['name'].lower() in vm_processes:
                return True
                
        return False
    except:
        return False

def check_tampering():
    try:
        # Verificar integridad del ejecutable
        if hasattr(sys, '_MEIPASS'):
            exe_path = sys.executable
            with open(exe_path, 'rb') as f:
                content = f.read()
                if len(content) < 1024:  # Tamaño mínimo esperado
                    return True
                    
        # Verificar modificaciones de memoria
        process = psutil.Process()
        if process.num_threads() < 3:  # Mínimo de threads esperados
            return True
            
        return False
    except:
        return False

def check_analysis_tools():
    try:
        # Detectar herramientas de análisis
        analysis_tools = [
            "wireshark", "fiddler", "process monitor",
            "process explorer", "pestudio", "ida"
        ]
        
        for proc in psutil.process_iter(['name']):
            if any(tool in proc.info['name'].lower() 
                  for tool in analysis_tools):
                return True
                
        # Verificar DLLs sospechosas
        suspicious_dlls = [
            "sbiedll.dll", "dbghelp.dll",
            "api_log.dll", "dir_watch.dll"
        ]
        
        process = psutil.Process()
        for module in process.memory_maps():
            if any(dll.lower() in module.path.lower() 
                  for dll in suspicious_dlls):
                return True
        return False
    except:
        return False

def prevent_dump():
    try:
        kernel32 = ctypes.windll.kernel32
        PAGE_EXECUTE_READ = 0x20
        base = kernel32.GetModuleHandleW(None)
        dos_header = ctypes.c_char.from_buffer_copy(ctypes.string_at(base, 64))
        if not dos_header.startswith(b'MZ'):
            return False
            
        # Proteger secciones críticas
        old_protect = ctypes.c_ulong(0)
        kernel32.VirtualProtect(base, 0x1000, PAGE_EXECUTE_READ, 
                              ctypes.byref(old_protect))
        return False
    except:
        return False

def verify_security():
    try:
        if hasattr(sys, '_MEIPASS'):
            print("Iniciando verificaciones de seguridad...")
            if check_debugger():
                return False
            if check_virtual_machine():
                return False
            if check_tampering():
                return False
            if check_analysis_tools():
                return False
            if prevent_dump():
                return False
            print("Verificaciones de seguridad completadas")
            return True
    except Exception as e:
        print(f"Error en verificación de seguridad: {str(e)}")
        return True

# Verificar módulos críticos
def verify_modules():
    critical_modules = [
        'win32process', 'win32api', 'win32file', 'win32gui',
        'win32con', 'pywintypes', 'wmi', 'PyQt6.QtWidgets'
    ]
    
    for module in critical_modules:
        try:
            __import__(module)
        except ImportError as e:
            print(f"Error: Módulo crítico {module} no encontrado: {e}")
            return False
    return True

# Iniciar verificaciones
if verify_modules():
    verify_security()
    print("Runtime hook completado")
else:
    print("Error: Faltan módulos críticos")
""")

# Mejora de ofuscación con múltiples capas
def deep_obfuscate(s):
    # Primera capa: Compresión
    compressed = zlib.compress(s.encode(), level=9)
    # Segunda capa: Encoding personalizado
    encoded = base64.b85encode(compressed)
    # Tercera capa: Cifrado simple
    key = bytes([x ^ 0x55 for x in range(256)])
    encrypted = bytes([x ^ key[i % 256] for i, x in enumerate(encoded)])
    return base64.b85encode(encrypted).decode()

def deep_deobfuscate(s):
    try:
        # Reverso del proceso de ofuscación
        encrypted = base64.b85decode(s.encode())
        key = bytes([x ^ 0x55 for x in range(256)])
        encoded = bytes([x ^ key[i % 256] for i, x in enumerate(encrypted)])
        compressed = base64.b85decode(encoded)
        return zlib.decompress(compressed).decode()
    except:
        return ""

# Anti-Memory Dump mejorado
def prevent_memory_dump():
    try:
        kernel32 = ctypes.windll.kernel32
        # Proteger secciones de memoria
        sections = [".text", ".rdata", ".data", ".rsrc"]
        base = kernel32.GetModuleHandleW(None)
        
        # Verificar y proteger cada sección
        for section in sections:
            try:
                section_addr = base + 0x1000  # Aproximado
                old_protect = ctypes.c_ulong(0)
                kernel32.VirtualProtect(
                    section_addr,
                    0x1000,
                    0x20,  # PAGE_EXECUTE_READ
                    ctypes.byref(old_protect)
                )
            except:
                continue
        return False
    except:
        return False

# Anti-Hooking mejorado
def check_hooks():
    try:
        # Verificar hooks en funciones críticas
        critical_dlls = ["kernel32.dll", "ntdll.dll", "user32.dll"]
        for dll in critical_dlls:
            handle = ctypes.windll.kernel32.GetModuleHandleW(dll)
            if not handle:
                continue
                
            # Verificar primeros bytes de funciones críticas
            if dll == "kernel32.dll":
                functions = ["CreateFileW", "ReadFile", "WriteFile"]
            elif dll == "ntdll.dll":
                functions = ["NtCreateFile", "NtReadFile", "NtWriteFile"]
            else:
                continue
                
            for func in functions:
                try:
                    func_addr = ctypes.windll.kernel32.GetProcAddress(handle, func)
                    if not func_addr:
                        continue
                    # Verificar si los primeros bytes han sido modificados
                    orig_bytes = ctypes.string_at(func_addr, 5)
                    if orig_bytes[0] in [0xE9, 0xEB]:  # JMP, JMP SHORT
                        return True
                except:
                    continue
        return False
    except:
        return False

# Verificación de integridad mejorada
def check_integrity():
    try:
        if getattr(sys, 'frozen', False):
            # Verificar secciones PE
            base = ctypes.windll.kernel32.GetModuleHandleW(None)
            dos_header = ctypes.string_at(base, 64)
            
            if not dos_header.startswith(b'MZ'):
                return True
                
            pe_header_offset = struct.unpack('<I', dos_header[0x3C:0x40])[0]
            
            # Verificar firma PE
            pe_header = ctypes.string_at(base + pe_header_offset, 24)
            if not pe_header.startswith(b'PE\\0\\0'):
                return True
                
            # Verificar checksum de secciones
            section_header = base + pe_header_offset + 0xF8
            section_data = ctypes.string_at(section_header, 0x28)
            
            # Calcular hash de secciones críticas
            critical_sections = ['.text', '.rdata', '.data']
            for section in critical_sections:
                section_rva = struct.unpack('<I', section_data[12:16])[0]
                section_size = struct.unpack('<I', section_data[16:20])[0]
                section_content = ctypes.string_at(base + section_rva, section_size)
                
                # Verificar modificaciones
                if not section_content:
                    return True
        return False
    except:
        return False

def prevent_memory_scanning():
    try:
        # Detectar memory scanners conocidos
        scanner_processes = [
            "cheatengine-x86_64.exe", "cheatengine-i386.exe",
            "scanmem", "gameshark", "artmoney.exe",
            "memoryhackers", "memoryeditor"
        ]
        
        # Protección contra modificación de memoria
        kernel32 = ctypes.windll.kernel32
        sections = [".text", ".rdata", ".data"]
        base = kernel32.GetModuleHandleW(None)
        
        for section in sections:
            try:
                # Proteger secciones críticas
                kernel32.VirtualProtect(
                    base + 0x1000,
                    0x1000,
                    0x40,  # PAGE_EXECUTE_READWRITE
                    ctypes.byref(ctypes.c_ulong(0))
                )
            except:
                continue
        return False
    except:
        return False

def prevent_dll_injection():
    try:
        # Lista de DLLs sospechosas
        suspicious_dlls = [
            "inject.dll", "hook.dll", "proxy.dll",
            "interception.dll", "capture.dll"
        ]
        
        # Verificar DLLs cargadas
        process = psutil.Process()
        for module in process.memory_maps():
            if any(dll.lower() in module.path.lower() 
                  for dll in suspicious_dlls):
                return True
        return False
    except:
        return False

def check_sandbox():
    try:
        # Detectar entornos sandbox
        sandbox_artifacts = [
            "sandbox.exe", "sample.exe",
            "virus.exe", "malware.exe"
        ]
        
        # Verificar nombres sospechosos
        process_name = psutil.Process().name().lower()
        if any(artifact in process_name for artifact in sandbox_artifacts):
            return True
            
        # Verificar tiempo de ejecución del sistema
        boot_time = psutil.boot_time()
        if time.time() - boot_time < 300:  # 5 minutos
            return True
            
        return False
    except:
        return False

def prevent_patching():
    try:
        # Verificar modificaciones en tiempo real
        critical_addresses = [
            0x1000, 0x2000, 0x3000  # Direcciones críticas
        ]
        
        base = ctypes.windll.kernel32.GetModuleHandleW(None)
        for addr in critical_addresses:
            try:
                # Leer y verificar contenido
                content = ctypes.string_at(base + addr, 16)
                # Verificar si el contenido está corrupto
                if not content or len(content) != 16:
                    return True
            except:
                continue
        return False
    except:
        return False

# Función para verificar la fecha de caducidad
def check_expiration_date():
    try:
        # Fecha de caducidad codificada (año, mes, día)
        # Ejemplo: 15 de diciembre de 2024
        expiration_year = 2026
        expiration_month = 8
        expiration_day = 10
        
        # Obtener fecha actual
        import datetime
        current_date = datetime.datetime.now()
        expiration_date = datetime.datetime(expiration_year, expiration_month, expiration_day)
        
        # Verificar si la fecha actual es posterior a la fecha de caducidad
        if current_date > expiration_date:
            # La aplicación ha caducado
            return True
            
        # La aplicación aún es válida
        return False
    except:
        # En caso de error, permitir que la aplicación funcione
        return False

def check_hardware_debugger():
    try:
        # Detectar debuggers hardware
        kernel32 = ctypes.windll.kernel32
        
        # Verificar interrupciones de hardware
        if kernel32.CheckRemoteDebuggerPresent(
            kernel32.GetCurrentProcess(),
            ctypes.byref(ctypes.c_bool())
        ):
            return True
            
        # Verificar registros de debug
        if ctypes.windll.ntdll.NtQueryInformationProcess(
            -1, 0x7, None, 0, None
        ) == 0:
            return True
            
        return False
    except:
        return False

def enhanced_anti_dump():
    try:
        # Protección contra memory dumping avanzada
        kernel32 = ctypes.windll.kernel32
        ntdll = ctypes.windll.ntdll
        
        # Ocultar PEB
        peb = ntdll.NtQueryInformationProcess(-1, 0, None, 0, None)
        if peb:
            kernel32.VirtualProtect(peb, 0x1000, 0x40, ctypes.byref(ctypes.c_ulong()))
            
        # Proteger headers PE
        base = kernel32.GetModuleHandleW(None)
        kernel32.VirtualProtect(base, 0x1000, 0x40, ctypes.byref(ctypes.c_ulong()))
        
        return False
    except:
        return False

def enhanced_anti_analysis():
    try:
        advanced_tools = [
            "ghidra", "binary ninja", "hopper",
            "hex-rays", "r2dec", "cutter",
            "detect it easy", "pe-bear", "dependency walker"
        ]
        
        for proc in psutil.process_iter(['name', 'cmdline']):
            if any(tool.lower() in str(proc.info).lower() for tool in advanced_tools):
                return True
        return False
    except:
        return False

def prevent_code_injection():
    try:
        # Monitorear regiones de memoria
        process = psutil.Process()
        base_regions = set()
        
        for region in process.memory_maps():
            base_regions.add(region.addr)
            
        while True:
            current_regions = set()
            for region in process.memory_maps():
                current_regions.add(region.addr)
                
            if current_regions != base_regions:
                return True
                
            time.sleep(0.1)
            
        return False
    except:
        return False

# Función para limpiar directorios temporales antiguos
def clean_old_temp_dirs():
    program_data = 'C:\\ProgramData'
    pattern = os.path.join(program_data, '.zetacopy_temp*')
    try:
        for temp_dir in glob.glob(pattern):
            try:
                shutil.rmtree(temp_dir, ignore_errors=True)
            except:
                pass
    except:
        pass

# Limpiar directorios temporales antiguos
clean_old_temp_dirs()

# Usar ProgramData para el directorio temporal
custom_temp_dir = os.path.join('C:\\', 'ProgramData', '.zetacopy_temp')

def collect_local_modules():
    """Recolecta todos los módulos .py en el directorio actual"""
    local_modules = []
    for file in os.listdir('.'):
        if file.endswith('.py') and file != 'ZETACOPY.py':
            module_name = file[:-3]  # quitar .py
            local_modules.append(module_name)
    return local_modules

def create_advanced_protection():
    return r'''
import sys, os, ctypes, time, hashlib, random, winreg, psutil, zlib, base64
from ctypes import windll, c_void_p, c_uint, Structure, sizeof, byref
from threading import Thread

class AdvancedProtection:
    def __init__(self):
        self._key = self._generate_hardware_key()
        self._checksum = self._calculate_checksum()
        self._init_protection()
    
    def _generate_hardware_key(self):
        try:
            # Generar clave única basada en hardware
            system_info = ''
            system_info += windll.kernel32.GetVolumeInformationW(c_void_p(0))[1] or ''
            system_info += str(windll.user32.GetSystemMetrics(0))
            system_info += str(windll.user32.GetSystemMetrics(1))
            return hashlib.sha256(system_info.encode()).hexdigest()
        except:
            return None

    def _calculate_checksum(self):
        try:
            # Calcular checksum del ejecutable
            with open(sys.executable, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()
        except:
            return None

    def _init_protection(self):
        # Iniciar threads de protección
        Thread(target=self._memory_guard, daemon=True).start()
        Thread(target=self._process_guard, daemon=True).start()
        Thread(target=self._integrity_guard, daemon=True).start()
        Thread(target=self._expiration_guard, daemon=True).start()  # Nuevo thread para verificar caducidad

    def _memory_guard(self):
        while True:
            try:
                # Protección avanzada de memoria
                PAGE_READWRITE = 0x04
                PAGE_EXECUTE = 0x10
                PAGE_EXECUTE_READ = 0x20
                
                base = windll.kernel32.GetModuleHandleA(None)
                for section in ['.text', '.rdata', '.data']:
                    try:
                        # Proteger secciones críticas
                        old_protect = c_uint(0)
                        windll.kernel32.VirtualProtect(
                            base + 0x1000,
                            0x1000,
                            PAGE_EXECUTE_READ,
                            byref(old_protect)
                        )
                    except:
                        pass
            except:
                pass
            time.sleep(0.1)

    def _process_guard(self):
        blacklist = [
            'x64dbg', 'x32dbg', 'windbg', 'ida', 'ghidra',
            'ollydbg', 'immunity', 'radare2', 'pestudio',
            'process hacker', 'cheatengine', 'httpdebugger'
        ]
        while True:
            try:
                for proc in psutil.process_iter(['name', 'cmdline']):
                    try:
                        if any(tool.lower() in proc.info['name'].lower() for tool in blacklist):
                            self._terminate_protection()
                    except:
                        continue
            except:
                pass
            time.sleep(0.5)

    def _integrity_guard(self):
        while True:
            try:
                # Verificar integridad del ejecutable
                current_checksum = self._calculate_checksum()
                if current_checksum != self._checksum:
                    self._terminate_protection()
                
                # Verificar clave de hardware
                current_key = self._generate_hardware_key()
                if current_key != self._key:
                    self._terminate_protection()
            except:
                pass
            time.sleep(1)
            
    def _expiration_guard(self):
        # Verificar fecha de caducidad periódicamente
        while True:
            try:
                # Fecha de caducidad codificada (año, mes, día)
                # Debe coincidir con la fecha en check_expiration_date()
                expiration_year = 2026
                expiration_month = 8
                expiration_day = 10
                
                # Obtener fecha actual
                import datetime
                current_date = datetime.datetime.now()
                expiration_date = datetime.datetime(expiration_year, expiration_month, expiration_day)
                
                # Verificar si la fecha actual es posterior a la fecha de caducidad
                if current_date > expiration_date:
                    # La aplicación ha caducado
                    self._terminate_protection(expired=True)
            except:
                pass
            time.sleep(3600)  # Verificar cada hora

    def _terminate_protection(self, expired=False):
        try:
            # Limpiar memoria y archivos temporales
            temp_paths = [os.environ.get('TEMP'), os.environ.get('TMP')]
            for path in temp_paths:
                if path:
                    for root, _, files in os.walk(path):
                        for file in files:
                            if 'ZETACOPY' in file:
                                try:
                                    os.remove(os.path.join(root, file))
                                except:
                                    pass
            
            # Mensaje de error y terminación
            if expired:
                windll.user32.MessageBoxW(0, 
                    "Esta versión de ZETACOPY ha caducado.\\nPor favor, contacte con el desarrollador para obtener una nueva versión.", 
                    "Versión Caducada", 0x10)
            else:
                windll.user32.MessageBoxW(0, 
                    "Violación de seguridad detectada.\\nEl programa se cerrará.", 
                    "Error de Seguridad", 0x10)
            os._exit(1)
        except:
            os._exit(1)

# Iniciar protección
protection = AdvancedProtection()
'''

def encrypt_source():
    """Recolecta y encripta el código fuente"""
    source_code = ""
    
    # Primero el archivo principal
    with open('ZETACOPY.py', 'r', encoding='utf-8') as f:
        source_code += f.read() + "\n"
    
    # Luego los módulos adicionales
    for file in os.listdir('.'):
        if file.endswith('.py') and file != 'ZETACOPY.py':
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    module_code = f.read()
                    source_code += f"\n# Module: {file}\n{module_code}\n"
            except:
                continue

    # Generamos una clave de encriptación compleja
    key = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
    
    # Proceso de ofuscación mejorado
    def deep_encrypt(data):
        local_key = os.urandom(32)
        encrypted = bytes([b ^ local_key[i % 32] for i, b in enumerate(data)])
        return base64.b85encode(zlib.compress(encrypted)).decode()
    
    # Proceso de ofuscación
    compiled = compile(source_code, '<string>', 'exec')
    marshalled = marshal.dumps(compiled)
    compressed = zlib.compress(marshalled, level=9)
    encrypted = bytes([b ^ ord(key[i % len(key)]) for i, b in enumerate(compressed)])
    encoded = base64.b85encode(encrypted)
    
    # Crear el loader
    loader_code = f'''
import sys
import base64
import zlib
import marshal

_k = "{key}"  # Aquí está el cambio, usando comillas
_c = {repr(encoded)}

def _d():
    try:
        data = base64.b85decode(_c)
        decrypted = bytes([b ^ ord(_k[i % len(_k)]) for i, b in enumerate(data)])
        decompressed = zlib.decompress(decrypted)
        code = marshal.loads(decompressed)
        return code
    except:
        return None

if __name__ == '__main__':
    code = _d()
    if code:
        # Verificar fecha de caducidad antes de ejecutar
        import datetime
        expiration_year = 2026
        expiration_month = 8
        expiration_day = 10
        
        current_date = datetime.datetime.now()
        expiration_date = datetime.datetime(expiration_year, expiration_month, expiration_day)
        
        if current_date > expiration_date:
            import ctypes
            ctypes.windll.user32.MessageBoxW(0, 
                "Esta versión de ZETACOPY ha caducado.\\nPor favor, contacte con el desarrollador para obtener una nueva versión.", 
                "Versión Caducada", 0x10)
            sys.exit(1)
        
        # Si no ha caducado, ejecutar el código
        exec(code)
'''

    with open('ZETACOPY_protected.py', 'w', encoding='utf-8') as f:
        f.write(loader_code)
    
    return 'ZETACOPY_protected.py'

# Encriptar el código fuente
protected_file = encrypt_source()

# Obtener lista de módulos locales
local_modules = collect_local_modules()

block_cipher = None

a = Analysis(
    [protected_file],
    pathex=[],
    binaries=[],
    datas=[
        ('iconos/*', 'iconos'),
        ('sonidos/*', 'sonidos'),
        ('RemoveDrive.exe', '.'),
        ('du64.exe', '.'),
        ('Everything64.dll', '.'),
        ('Everything64.lib', '.'),
        ('Everything/*', 'Everything'),
    ],
    hiddenimports=[
        # Módulos de pywin32 completos
        'win32com', 'win32api', 'win32file', 'win32gui', 'win32con',
        'win32security', 'win32event', 'win32crypt', 'win32process',
        'win32service', 'win32serviceutil', 'win32clipboard', 'win32pipe',
        'win32job', 'win32pdh', 'win32evtlog', 'win32net', 'win32netcon',
        'win32wnet', 'win32inet', 'win32inetcon', 'win32ras', 'win32rasutil',
        'win32ts', 'win32profile', 'win32print', 'win32timezone',
        'pywintypes', 'pythoncom',
        
        # Módulos del sistema
        'wmi', 'psutil', 'winreg', 'glob', 'json', 'ctypes.wintypes',
        
        # Módulos de PyQt6
        'PyQt6.QtWidgets', 'PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.sip',
        
        # Módulos de pygame
        'pygame', 'pygame.mixer', 'pygame.mixer_music',
        
        # Módulos de criptografía
        'cryptography', 'cryptography.fernet', 'cryptography.hazmat',
        'cryptography.hazmat.primitives', 'cryptography.hazmat.primitives.hashes',
        'cryptography.hazmat.primitives.kdf', 'cryptography.hazmat.primitives.kdf.pbkdf2',
        
        # Módulos de QR
        'qrcode', 'qrcode.image', 'qrcode.image.pil', 'segno',
        
        # Módulos locales del proyecto
        'BARRA_MINI', 'AJUSTES', 'RENOMBRAR', 'MATRIX',
        'APARIENCIA', 'CONTEXTUAL', 'TOOLTIP_APARIENCIA',
        'Propiedades_Disco', 'ACTIVATE', 'NOTIFICACION',
        'CREAR', 'DRAGGABLE_LIST_WIDGET', 'COLA', 'BOTONES',
        'SONIDOS', 'ICONOS_EXPLORER', 'EVERYTHING_INSTALLER',
        'MONITOREO', 'MAPEO_PUERTO', 'EXPLORADOR', 'ZFind',
        
        # Módulos adicionales que podrían faltar
        'base64', 'zlib', 'marshal', 'hashlib', 'threading',
        'queue', 'collections', 'datetime', 'time', 'os',
        'sys', 'traceback', 'logging', 'subprocess',
        
        *local_modules
    ],
    hookspath=['.'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False
)

pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=block_cipher
)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ZETACOPY',
    debug=False,
    bootloader_ignore_signals=True,
    strip=False,
    upx=False,
    runtime_tmpdir=r"C:\ProgramData\.zetacopy_temp",
    console=False,
    disable_windowed_traceback=True,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='iconos/ZETACOPY.ico',
    uac_admin=False,
    uac_uiaccess=False,
    manifest='''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <dependency>
    <dependentAssembly>
      <assemblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version="*******" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" language="*"/>
    </dependentAssembly>
  </dependency>
</assembly>''',
)


# Limpiar archivos temporales
if os.path.exists(protected_file):
    os.remove(protected_file)

if os.path.exists('runtime_hook.py'):
    os.remove('runtime_hook.py')

# Crear archivo version.txt antes de la compilación
version_info = """
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'ZETACOPY'),
         StringStruct(u'FileDescription', u'ZETACOPY File Manager'),
         StringStruct(u'FileVersion', u'1.0.0'),
         StringStruct(u'InternalName', u'ZETACOPY'),
         StringStruct(u'LegalCopyright', u'Copyright (c) 2024'),
         StringStruct(u'OriginalFilename', u'ZETACOPY.exe'),
         StringStruct(u'ProductName', u'ZETACOPY'),
         StringStruct(u'ProductVersion', u'1.0.0')])
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""

with open('version.txt', 'w', encoding='utf-8') as f:
    f.write(version_info)