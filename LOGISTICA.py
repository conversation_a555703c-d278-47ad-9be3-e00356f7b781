import os
from datetime import datetime
import json, win32api
import traceback
from datetime import timedelta
import sys
import time
import shutil
from AJUSTES import load_config
def registrar_copia_finalizada(main_window, drive_letter):
    """
    Registra el estado de la copia finalizada en un archivo de texto en el disco de destino.
    Incluye el precio de la copia, la cantidad de archivos y el tamaño total.
    """
    try:
        # Obtener el nombre del volumen
        volume_name = "Desconocido"
        try:
            # Obtener el nombre del volumen usando el método de MainWindow
            volume_name = main_window.get_volume_name(drive_letter)
            if not volume_name: # Fallback si get_volume_name no devuelve nada
                volume_name = win32api.GetVolumeInformation(f"{drive_letter}\\")[0]
        except Exception:
            pass
        
        # Ruta del archivo de registro en el disco de destino
        current_date = datetime.now().strftime('%Y-%m-%d')
        log_file_name = f"Registro_{volume_name}_{current_date}.txt"
        log_file_path = os.path.join(f"{drive_letter}\\", log_file_name)

        # Cargar precios y modo de pago
        config = load_config()
        modo_pago = config.get('modo_pago', 'dispositivo')
        # Modo de pago para registro (optimizado)

        # Obtener información de la cola finalizada
        total_size_copied = main_window.total_sizes.get(drive_letter, 0)
        
        # Obtener lista de archivos procesados
        archivos_procesados = []
        
        # Método 1: Obtener de archivos_procesados (estructura principal)
        if hasattr(main_window, 'archivos_procesados') and drive_letter in main_window.archivos_procesados:
            archivos_procesados = list(main_window.archivos_procesados[drive_letter])
            print(f"Archivos procesados encontrados en main_window.archivos_procesados: {len(archivos_procesados)}")
        
        # Método 2: Si no hay archivos, intentar recuperarlos de la cola completada
        if not archivos_procesados and hasattr(main_window, 'completed_files') and drive_letter in main_window.completed_files:
            archivos_procesados = [file for file, _ in main_window.completed_files[drive_letter]]
            print(f"Archivos recuperados de completed_files: {len(archivos_procesados)}")
        
        # Método 3: Si aún no hay archivos, buscar en la estructura de cálculo de precio por ficheros
        if not archivos_procesados and modo_pago != "ficheros":  # Solo si NO estamos en modo ficheros
            # Intentar recuperar los archivos que se procesaron durante la copia
            if hasattr(main_window, 'copy_queue') and drive_letter in main_window.copy_queue:
                for src_path, dst_path in list(main_window.copy_queue[drive_letter].queue):
                    if os.path.exists(dst_path):  # Si el archivo existe en el destino, se copió
                        archivos_procesados.append(src_path)
                print(f"Archivos recuperados de copy_queue: {len(archivos_procesados)}")
        
        # Método 4: Buscar archivos recientes en el disco como último recurso
        if not archivos_procesados:
            try:
                # Buscar archivos copiados recientemente en el disco
                recent_time = time.time() - 3600  # Archivos de la última hora
                for root, dirs, files in os.walk(f"{drive_letter}\\"):
                    for file in files:
                        if file.startswith("Registro_"):  # Excluir archivos de registro
                            continue
                        file_path = os.path.join(root, file)
                        try:
                            if os.path.getmtime(file_path) > recent_time:
                                archivos_procesados.append(file_path)
                        except:
                            pass
                print(f"Archivos recientes encontrados en el disco: {len(archivos_procesados)}")
            except Exception as e:
                print(f"Error buscando archivos en disco: {e}")
        
        total_files_processed = len(archivos_procesados)
        print(f"Total de archivos procesados: {total_files_processed}")
        
        # Inicializar el contenido del registro
        content = []
        content.append(f"REGISTRO DE COPIA FINALIZADA - {volume_name} ({drive_letter})")
        content.append("=" * 50)
        content.append(f"Fecha y hora:                  {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append(f"Tamaño total copiado:          {main_window.format_size(total_size_copied)}")
        content.append(f"FICHEROS COPIADOS:                {total_files_processed}")
        
        # Verificar si el archivo ya existe para acumular los archivos copiados
        existing_files = []
        existing_total_size = 0
        existing_total_files = 0
        existing_total_pago = 0.0  # Para acumular el pago anterior
        
        if os.path.exists(log_file_path):
            try:
                with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content_lines = f.readlines()
                
                # Buscar la línea que contiene "FICHEROS COPIADOS:" y "PAGO:"
                for i, line in enumerate(content_lines):
                    if "FICHEROS COPIADOS:" in line:
                        try:
                            existing_total_files = int(line.split("FICHEROS COPIADOS:")[1].strip())
                            print(f"Ficheros existentes encontrados en el registro: {existing_total_files}")
                        except Exception as e:
                            print(f"Error al leer ficheros existentes: {e}")
                            pass
                    
                    if "TOTAL COPIADO:" in line:
                        try:
                            size_str = line.split("TOTAL COPIADO:")[1].strip()
                            # Convertir a bytes
                            if "GB" in size_str:
                                existing_total_size = float(size_str.split(" GB")[0]) * 1024 * 1024 * 1024
                            elif "MB" in size_str:
                                existing_total_size = float(size_str.split(" MB")[0]) * 1024 * 1024
                            elif "KB" in size_str:
                                existing_total_size = float(size_str.split(" KB")[0]) * 1024
                            print(f"Tamaño existente encontrado: {main_window.format_size(existing_total_size)}")
                        except Exception as e:
                            print(f"Error al leer tamaño existente: {e}")
                            pass
                    
                    # Buscar el pago anterior
                    if "PAGO:" in line:
                        try:
                            pago_str = line.split("PAGO:")[1].strip()
                            if pago_str.startswith("$"):
                                existing_total_pago = float(pago_str[1:])
                                print(f"Pago existente encontrado: ${existing_total_pago:.2f}")
                        except Exception as e:
                            print(f"Error al leer pago existente: {e}")
                            pass
                    
                    # También buscar el TOTAL A PAGAR (que podría ser diferente del PAGO)
                    if "TOTAL A PAGAR:" in line:
                        try:
                            pago_str = line.split("TOTAL A PAGAR:")[1].strip()
                            if pago_str.startswith("$"):
                                existing_total_pago = float(pago_str[1:])
                                print(f"Total a pagar existente encontrado: ${existing_total_pago:.2f}")
                        except Exception as e:
                            print(f"Error al leer total a pagar existente: {e}")
                            pass
                
                # Buscar la sección de archivos (después de la última línea de guiones)
                last_separator_index = -1
                for i, line in enumerate(content_lines):
                    if "-" * 50 in line:  # Buscar líneas con al menos 50 guiones
                        last_separator_index = i
                
                if last_separator_index >= 0 and last_separator_index < len(content_lines) - 1:
                    # Leer los archivos existentes
                    for i in range(last_separator_index + 1, len(content_lines)):
                        line = content_lines[i].strip()
                        if line and not line.startswith("=") and not "TOTAL A PAGAR:" in line:  # Ignorar líneas vacías, separadores y total
                            parts = line.split()
                            if len(parts) >= 4:  # Asegurarse de que tiene el formato esperado
                                try:
                                    # Extraer tamaño del archivo (si existe)
                                    file_size_str = parts[1]
                                    file_size = 0
                                    if file_size_str != "?????":
                                        try:
                                            file_size = float(file_size_str) * 1024 * 1024  # Convertir MB a bytes
                                        except:
                                            pass
                                    
                                    # Extraer nombre del archivo (todo después del tamaño)
                                    file_name = " ".join(parts[3:])
                                    
                                    # Guardar tanto el nombre como el tamaño
                                    existing_files.append((file_size, file_name))
                                except:
                                    pass
            except Exception as e:
                print(f"Error al leer el archivo existente: {e}")
        
        # Crear una lista combinada de archivos (existentes + nuevos)
        combined_files = []
        file_names_set = set()  # Conjunto para rastrear nombres de archivo únicos
        
        # Primero añadir los archivos existentes
        for file_size, file_name in existing_files:
            if file_name not in file_names_set:
                combined_files.append((file_size, file_name))
                file_names_set.add(file_name)
        
        # Luego añadir los archivos nuevos
        for file_path in archivos_procesados:
            file_name = os.path.basename(file_path)
            if file_name not in file_names_set:
                try:
                    file_size = os.path.getsize(file_path)
                except:
                    file_size = 0  # Si no se puede obtener el tamaño
                combined_files.append((file_size, file_name))
                file_names_set.add(file_name)
        
        # Calcular el número real de archivos y tamaño total
        total_files = len(combined_files)
        total_size = existing_total_size + total_size_copied
        print(f"Total de archivos calculado: {total_files}")
        print(f"Tamaño total calculado: {main_window.format_size(total_size)}")
        
        # Calcular el precio final de la copia según el modo de pago
        precio_final = None
        if modo_pago == "duracion":
            # Calcular duración total de los archivos procesados
            duracion_total = 0
            for file_path in archivos_procesados:
                duracion = main_window.get_video_duration(file_path)
                if duracion is not None:
                    duracion_total += duracion
            precio_final = main_window.calcular_y_mostrar_precio(drive_letter, None, duracion_total)
        elif modo_pago == "ficheros":
            precio_final = main_window.calcular_y_mostrar_precio(drive_letter, None, None, total_files=total_files_processed)
        else:  # modo dispositivo (por GB)
            # Usar el tamaño total acumulado para calcular el precio
            total_size_gb = total_size / (1024 ** 3)
            print(f"Calculando precio por dispositivo para {total_size_gb:.2f} GB")
            precio_final = main_window.calcular_y_mostrar_precio(drive_letter, total_size_gb)
        
        print(f"Precio calculado para esta operación: ${precio_final if precio_final else 0:.2f}")
        
        # Sumar el precio actual al precio existente
        total_pago = existing_total_pago
        if precio_final:
            total_pago += precio_final
        
        print(f"Precio total acumulado: ${total_pago:.2f}")
        
        # Preparar el contenido a escribir en formato organizado
        content = []
        
        # Crear un título con el nombre del archivo
        title = f"Registro_{volume_name}_{current_date}"
        
        # Crear un cuadro de asteriscos alrededor del título
        width = len(title) + 10  # Ancho basado en el título más un margen
        border = "*" * width
        
        # Añadir el cuadro con el título centrado
        content.append(border)
        padding = (width - len(title)) // 2
        content.append("*" + " " * (padding - 1) + title + " " * (width - padding - len(title) - 1) + "*")
        content.append(border)
        
        # Añadir una línea en blanco para separación
        content.append("")
        
        # Ancho para el resto del contenido
        content_width = 100  # Mantener el ancho de 100 para el resto del contenido
        
        # Encabezado con solo el nombre del dispositivo
        content.append(f"NOMBRE:                           {volume_name}")
        
        # Información de pago
        content.append(f"COBRADO:                          {'SI' if total_pago > 0 else 'NO'}")

        if modo_pago == "duracion":
            tipo_pago = "Pago por Duración"
            # Añadir información de duración total
            duracion_total = 0
            for file_path in archivos_procesados:
                duracion = main_window.get_video_duration(file_path)
                if duracion is not None:
                    duracion_total += duracion
            content.append(f"DURACIÓN TOTAL:                    {int(duracion_total)} minutos")
        elif modo_pago == "ficheros":
            tipo_pago = "Pago por Ficheros"  # Corregido: debe ser "Pago por Ficheros"
            # Ya tenemos la información de archivos más abajo en el TXT
        elif modo_pago == "dispositivo":
            tipo_pago = "Pago por Dispositivo"
            # Ya tenemos la información de tamaño más abajo en el TXT
        else:
            tipo_pago = "Pago por Dispositivo"
            
        content.append(f"TIPO DE PAGO:                     {tipo_pago}")
        content.append(f"PAGO:                             ${total_pago:.2f}" if total_pago > 0 else "PAGO:                             $0.00")
        
        # Separador
        content.append("-" * content_width)
        
        # Centrar el título "COPIADO"
        copiado_title = "COPIADO"
        padding = (content_width - len(copiado_title)) // 2
        content.append(" " * padding + copiado_title)
        
        content.append("-" * content_width)
        
        # Información de archivos (usando el conteo real)
        content.append(f"FICHEROS COPIADOS:                {total_files}")
        
        # Tamaño total (acumulado)
        total_size_str = main_window.format_size(total_size)
        content.append(f"TOTAL COPIADO:                    {total_size_str}")
        
        # Obtener información de tiempos
        now = datetime.now()
        start_time = main_window.copy_start_time.get(drive_letter)
        if start_time is None or isinstance(start_time, float):
            # Si no existe o es un float, crear un tiempo de inicio estimado
            if isinstance(start_time, float):
                # Convertir el timestamp float a datetime
                start_time = datetime.fromtimestamp(start_time)
            else:
                # Usar un tiempo estimado (1 minuto antes)
                start_time = now - timedelta(minutes=1)

        elapsed_time = now - start_time
        hours, remainder = divmod(elapsed_time.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        
        # Calcular velocidad promedio
        velocidad = 0
        if elapsed_time.total_seconds() > 0:
            velocidad = total_size_copied / elapsed_time.total_seconds() / (1024 * 1024)  # MB/s
        
        # Información de tiempo
        content.append(f"EMPEZÓ:                           {start_time.strftime('%m/%d/%Y %I:%M:%S %p')}")
        content.append(f"FINALIZÓ:                         {now.strftime('%m/%d/%Y %I:%M:%S %p')}")
        
        # Duración
        duration_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        content.append(f"TARDÓ:                            {duration_str}")
        
        # Tiempo de trabajo (igual a la duración en este caso)
        content.append(f"TIEMPO DE TRABAJO:                {duration_str}")
        
        # Velocidad
        content.append(f"VELICIDAD:                        {velocidad:.1f} MB/s")
        
        # Separador
        content.append("-" * content_width)
        
        # Ahora añadir todos los archivos a la salida - Comentamos esta sección que genera la primera lista menos organizada
        '''
        for idx, (file_size, file_name) in enumerate(combined_files, 1):
            # Si es un archivo existente con tamaño 0, usar un marcador
            if file_size == 0:
                content.append(f"{idx:03d}- {'?' * 5} MB{' ' * 20}{file_name}")
            else:
                # Formatear tamaño en MB
                file_size_mb = file_size / (1024 * 1024)
                content.append(f"{idx:03d}- {file_size_mb:.1f} MB{' ' * 20}{file_name}")
        '''

        # Después de la sección de información general y antes del TOTAL A PAGAR
        # Añadir la lista detallada de archivos copiados con sus tamaños
        print(f"Preparando lista de {len(archivos_procesados)} archivos para el registro")
        if archivos_procesados:
            content.append("-" * 100)
            content.append("LISTA DE ARCHIVOS COPIADOS:")
            content.append("")
            
            # Procesar y ordenar los archivos por tamaño
            archivos_con_tamano = []
            for file_path in archivos_procesados:
                try:
                    # Intentar obtener el tamaño real del archivo
                    file_size = 0
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"Archivo original encontrado: {file_path}, tamaño: {file_size}")
                    else:
                        # Si el archivo original ya no existe, buscar en el destino
                        dest_name = os.path.basename(file_path)
                        dest_path = os.path.join(f"{drive_letter}\\", dest_name)
                        if os.path.exists(dest_path):
                            file_size = os.path.getsize(dest_path)
                            print(f"Archivo destino encontrado: {dest_path}, tamaño: {file_size}")
                        else:
                            print(f"No se pudo encontrar el archivo: {file_path} ni {dest_path}")
                    
                    file_name = os.path.basename(file_path)
                    archivos_con_tamano.append((file_path, file_size, file_name))
                except Exception as e:
                    print(f"Error al obtener tamaño de {file_path}: {e}")
                    file_name = os.path.basename(file_path)
                    archivos_con_tamano.append((file_path, 0, file_name))
            
            # Ordenar por tamaño (mayor a menor)
            archivos_con_tamano.sort(key=lambda x: x[1], reverse=True)
            
            # Añadir cada archivo a la lista
            for file_path, file_size, file_name in archivos_con_tamano:
                # Formatear el tamaño en MB para mejor legibilidad
                size_mb = file_size / (1024 * 1024)
                # Añadir el archivo con su tamaño
                content.append(f"{file_name[:70].ljust(70)} {f'{size_mb:.2f} MB'.rjust(15)}")
            
            content.append("-" * 100)
        else:
            print("No hay archivos procesados para mostrar en el registro")

        # Finalmente añadir el TOTAL A PAGAR
        content.append(f"TOTAL A PAGAR:                    ${total_pago:.2f}")
        
        # Unir todo el contenido
        full_content = "\n".join(content)
        
        # Escribir el nuevo contenido (sobreescribir el archivo)
        with open(log_file_path, 'w', encoding='utf-8') as f:
            f.write(full_content)
        
        print(f"Registro de copia guardado en: {log_file_path}")
        
        # Crear copia en carpeta Estadísticas organizada por fecha
        try:
            # Determinar la ruta base (ejecutable o script)
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            # Crear estructura de carpetas
            stats_folder = os.path.join(base_path, "Estadísticas")
            date_folder = os.path.join(stats_folder, datetime.now().strftime('%d.%m.%Y'))
            
            # Crear carpetas si no existen
            os.makedirs(date_folder, exist_ok=True)
            
            # Copiar el archivo de registro a la carpeta de estadísticas
            stats_file_path = os.path.join(date_folder, log_file_name)
            shutil.copy2(log_file_path, stats_file_path)
            
            print(f"Copia de estadísticas guardada en: {stats_file_path}")
        except Exception as e:
            print(f"Error al guardar estadísticas: {e}")
            traceback.print_exc()
        
    except Exception as e:
        print(f"Error al registrar la copia finalizada: {e}")
        traceback.print_exc()
