from PyQt6.QtWidgets import (
    QWidget, QHB<PERSON>Layout, QProgressBar, QLabel, 
    QVBoxLayout, QGraphicsDropShadowEffect, QToolButton, QMainWindow,
    QGraphicsOpacityEffect, QApplication
)
from PyQt6.QtCore import Qt, QTimer, QRectF, QEvent, QRect, QPointF, QPropertyAnimation, QPoint, QSize, QEasingCurve, QObject, pyqtProperty
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QPen, QFont, QCursor, QPixmap, QIcon, QLinearGradient
from APARIENCIA import (
    ACCENT_POLICY, 
    WIND<PERSON>COMPOSITIONATTRIBDATA, 
    ACCENT_ENABLE_ACRYLICBLURBEHIND, 
    WCA_ACCENT_POLICY,
    DWMWA_WINDOW_CORNER_PREFERENCE,
    DWMWCP_ROUND,
    ACCENT_ENABLE_FLUENT,
    is_windows_11_or_greater
)
import psutil
import os
import time
import ctypes
from ctypes import wintypes
import win32gui
import win32con
import win32api
import json
from CREAR import create_unpin_icon, create_pin_icon

class CustomToolTip(QLabel):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.setStyleSheet("""
            QLabel {
                color: white;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(150)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def paintEvent(self, event):
        if self.isVisible():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            path = QPainterPath()
            rect = QRectF(self.rect()).adjusted(1, 1, -1, -1)
            radius = min(rect.height(), rect.width()) * 0.47
            path.addRoundedRect(rect, radius, radius)
            painter.setPen(Qt.PenStyle.NoPen)
            for i in range(2):
                shadow_path = QPainterPath()
                shadow_rect = rect.adjusted(-i, -i, i, i)
                shadow_path.addRoundedRect(shadow_rect, radius, radius)
                painter.setBrush(QColor(0, 0, 0, 15 - i * 5))
                painter.drawPath(shadow_path)
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0.0, QColor(65, 65, 65, 250))
            gradient.setColorAt(0.5, QColor(55, 55, 55, 250))
            gradient.setColorAt(1.0, QColor(50, 50, 50, 250))
            painter.setBrush(gradient)
            painter.drawPath(path)
            pen = QPen()
            pen.setColor(QColor(255, 255, 255, 30))
            pen.setWidth(1)
            pen.setStyle(Qt.PenStyle.SolidLine)
            painter.setPen(pen)
            painter.drawPath(path)
            super().paintEvent(event)
        
    def hideEvent(self, event):
        self.opacity_animation.stop()
        self.scale_animation.stop()
        super().hideEvent(event)

    def show_tooltip(self, pos):
        self.adjustSize()
        final_pos = pos + QPoint(-self.width() // 2, 35)
        self.move(final_pos)
        self.show()

class AnimatedToolButton(QToolButton):
    def __init__(self, icon_path=None, parent=None):
        super().__init__(parent)
        
        # Configurar tamaños base (igual que BOTONES.py)
        self._icon_size = 25
        self._hover_size = 30
        
        # Configurar apariencia inicial
        self.setStyleSheet("QToolButton { border: none; background: transparent; }")
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setFixedSize(28, 28)  # Tamaño fijo del botón
        
        # Configurar el tamaño inicial del icono
        self.setIconSize(QSize(self._icon_size, self._icon_size))
        
        # Crear efecto de sombra
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(8)
        self.shadow.setXOffset(0)
        self.shadow.setYOffset(0)
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.setGraphicsEffect(self.shadow)
        
        # Cargar icono si se proporciona
        if icon_path:
            self.setIcon(QIcon(icon_path))
        
        # Tooltip personalizado
        self._tooltip = None
        self._tooltip_text = ""
    
    def setToolTip(self, text):
        self._tooltip_text = text
        if hasattr(self, '_tooltip') and self._tooltip:
            self._tooltip.hide()
            self._tooltip.deleteLater()
            self._tooltip = None
    
    def enterEvent(self, event):
        # Mostrar tooltip si existe
        if self._tooltip_text:
            if not self._tooltip:
                self._tooltip = CustomToolTip(self._tooltip_text)
            self._tooltip.show_tooltip(self.mapToGlobal(QPoint(self.width()//2, 0)))
        
        # Cambiar a efecto de luz al hacer hover
        self.shadow.setColor(QColor(255, 255, 255, 100))
        self.shadow.setBlurRadius(15)
        
        # Animar a un tamaño más grande
        self.animate_icon_size(self._hover_size)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        # Ocultar tooltip
        if self._tooltip:
            self._tooltip.hide()
        
        # Restaurar efecto de sombra normal
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.shadow.setBlurRadius(8)
        
        # Volver al tamaño original
        self.animate_icon_size(self._icon_size)
        super().leaveEvent(event)
    
    def animate_icon_size(self, target_size):
        # Crear una nueva animación cada vez (exactamente como en BOTONES.py)
        self.animation = QPropertyAnimation(self, b"iconSize")
        self.animation.setDuration(100)
        self.animation.setStartValue(self.iconSize)
        self.animation.setEndValue(QSize(target_size, target_size))
        self.animation.start()
    
    # Uso de la propiedad iconSize en lugar de icon_size (como en BOTONES.py)
    @pyqtProperty(QSize)
    def iconSize(self):
        return super().iconSize()
    
    @iconSize.setter
    def iconSize(self, size):
        super().setIconSize(size)

class RAMPropertiesDialog(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent, Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint | Qt.WindowType.NoDropShadowWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.parent = parent
        
        # Usar los nuevos iconos generados
        from CREAR import create_ram_icon, create_ram_warning_icon
        self.normal_icon = create_ram_icon(size=24)
        self.warning_icon = create_ram_warning_icon(size=24)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        title_layout = QHBoxLayout()
        title_layout.setSpacing(5)
        self.icon_label = QLabel()
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 160))
        self.icon_label.setGraphicsEffect(shadow)
        title_layout.addWidget(self.icon_label)
        self.title_label = QLabel("<b>RAM</b>")
        self.title_label.setStyleSheet("color: white;")
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        self.text_label = QLabel()
        self.text_label.setStyleSheet("color: white; background: transparent;")
        layout.addLayout(title_layout)
        layout.addWidget(self.text_label)
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_ram_info)
        self.update_timer.setInterval(2000)
        
        # Cachear los valores de RAM para evitar lecturas frecuentes
        self._last_ram_check = 0
        self._ram_cache = None
        self._cache_duration = 2  # segundos
        
        # Preparar animaciones
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(200)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.pos_animation = QPropertyAnimation(self, b"pos")
        self.pos_animation.setDuration(300)
        self.pos_animation.setEasingCurve(QEasingCurve.Type.OutBack)

    def show_properties(self, pos):
        self.update_ram_info()
        
        # Configurar posición inicial y final para la animación
        start_pos = QPoint(pos.x(), pos.y() - 20)  # Empezar un poco más arriba
        self.move(start_pos)
        
        # Aplicar el efecto según la versión de Windows
        hwnd = int(self.winId())
        
        accent = ACCENT_POLICY()
        if is_windows_11_or_greater():
            accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
            accent.GradientColor = 0x01000000
        else:
            accent.AccentState = ACCENT_ENABLE_FLUENT
            accent.GradientColor = 0x33000000  # 20% opacidad para máxima transparencia
        accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
        
        data = WINDOWCOMPOSITIONATTRIBDATA()
        data.Attribute = WCA_ACCENT_POLICY
        data.SizeOfData = ctypes.sizeof(accent)
        data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))
        ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))
        
        # Aplicar bordes redondeados
        ctypes.windll.dwmapi.DwmSetWindowAttribute(
            hwnd,
            DWMWA_WINDOW_CORNER_PREFERENCE,
            ctypes.byref(ctypes.c_int(DWMWCP_ROUND)),
            ctypes.sizeof(ctypes.c_int)
        )
        
        # Mostrar el widget con opacidad 0
        self.setWindowOpacity(0.0)
        self.show()
        
        # Configurar y iniciar animación de opacidad
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        
        # Configurar y iniciar animación de posición
        self.pos_animation.setStartValue(start_pos)
        self.pos_animation.setEndValue(pos)
        
        # Iniciar animaciones
        self.opacity_animation.start()
        self.pos_animation.start()
        
        self.timer_id = self.startTimer(100)
        self.update_timer.start()

    def hideEvent(self, event):
        if hasattr(self, 'timer_id'):
            self.killTimer(self.timer_id)
        self.update_timer.stop()
        self.opacity_animation.stop()
        self.pos_animation.stop()
        super().hideEvent(event)

    def update_ram_info(self):
        current_time = time.time()
        # Usar caché si está disponible y es reciente
        if self._ram_cache and (current_time - self._last_ram_check) < self._cache_duration:
            ram = self._ram_cache
        else:
            ram = psutil.virtual_memory()
            self._ram_cache = ram
            self._last_ram_check = current_time

        # Calcular valores una sola vez
        total_gb = ram.total / (1024**3)
        used_gb = ram.used / (1024**3)
        free_gb = ram.free / (1024**3)
        percent = ram.percent

        # Actualizar icono solo si cambia el estado
        # Convertir QIcon a QPixmap antes de asignarlo
        if percent >= 70:
            new_icon_pixmap = self.warning_icon.pixmap(24, 24)
        else:
            new_icon_pixmap = self.normal_icon.pixmap(24, 24)
        
        # Verificar si el pixmap actual es diferente
        current_pixmap = self.icon_label.pixmap()
        if not current_pixmap or current_pixmap.cacheKey() != new_icon_pixmap.cacheKey():
            self.icon_label.setPixmap(new_icon_pixmap)

        # Optimizar la generación del texto HTML
        if not hasattr(self, '_last_percent') or self._last_percent != percent:
            info_text = (
                f"<div style='padding: 5px;'>"
                f"<hr style='border: 1px solid rgba(255,255,255,0.1); margin: 5px 0;'>"
                f"<p style='color: white; margin: 5px 0;'>Total: {total_gb:.1f} GB</p>"
                f"<p style='color: white; margin: 5px 0;'>En uso: {used_gb:.1f} GB ({percent}%)</p>"
                f"<p style='color: white; margin: 5px 0;'>Libre: {free_gb:.1f} GB</p>"
                f"</div>"
            )
            self.text_label.setText(info_text)
            self._last_percent = percent

        self.adjustSize()

    def timerEvent(self, event):
        self.check_cursor_position()

    def check_cursor_position(self):
        if not self.parent:
            self.hide()
            return
        current_pos = self.parent.mapFromGlobal(QCursor.pos())
        progress_rect = self.parent.progress_bar.geometry()
        if not progress_rect.contains(current_pos):
            self.hide()

class CircularProgressBar(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(32, 32)
        self.value = 0
        self.progress_color = QColor("#0078d7")
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # Usar los nuevos iconos generados
        from CREAR import create_ram_icon, create_ram_warning_icon
        self.normal_icon = create_ram_icon(size=24)
        self.warning_icon = create_ram_warning_icon(size=24)
        
        # Convertir QIcon a QPixmap para su uso en paintEvent
        self.icon = self.normal_icon.pixmap(24, 24)
        
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 160))
        self.setGraphicsEffect(shadow)
        
        # Pre-calcular valores comunes
        self._center_x = self.width() / 2
        self._center_y = self.height() / 2
        self._last_value = None
        
    def setValue(self, value):
        # Actualizar solo si el valor cambia
        if self._last_value != value:
            self.value = value
            # Actualizar el icono según el valor
            if value >= 70:
                self.icon = self.warning_icon.pixmap(24, 24)
            else:
                self.icon = self.normal_icon.pixmap(24, 24)
            self._last_value = value
            self.update()
    
    # Eliminar los métodos enterEvent y leaveEvent que muestran el tooltip
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Calcular el rectángulo para el círculo
        rect = QRectF(3, 3, self.width() - 6, self.height() - 6)
        int_rect = rect.toRect()  # Convertir a QRect para drawArc
        
        # Dibujar la sombra del círculo (negro semitransparente)
        shadow_rect = QRectF(3.5, 3.5, self.width() - 7, self.height() - 7)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QColor(0, 0, 0, 25))  # Sombra negra semitransparente
        painter.drawEllipse(shadow_rect)
        
        # Dibujar el círculo de fondo (gris)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QColor(255, 255, 255, 15))  # Color gris más semitransparente
        painter.drawEllipse(rect)
        
        # Dibujar el círculo de progreso
        pen = QPen(self.progress_color)
        pen.setWidth(3)
        painter.setPen(pen)
        
        # Dibujar el arco de progreso
        start_angle = 90 * 16  # Ángulo de inicio en grados * 16 (formato QPainter)
        span_angle = int(-self.value * 3.6 * 16)  # Convertir porcentaje a grados * 16 y convertir a int
        
        painter.drawArc(int_rect, start_angle, span_angle)
        
        # Dibujar el icono en el centro
        icon_rect = QRectF(
            self.width() / 2 - 12,
            self.height() / 2 - 12,
            24,
            24
        )
        painter.drawPixmap(icon_rect.toRect(), self.icon)

class RAMUsageWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(32)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        self.progress_bar = CircularProgressBar(self)
        layout.addWidget(self.progress_bar)
        
        self.pin_button = AnimatedToolButton(parent=self)
        
        # Crear iconos más grandes (36px en lugar de 38px)
        self.pin_icon = create_pin_icon(size=30)  # Reducir tamaño de generación
        self.unpin_icon = create_unpin_icon(size=30)  # Reducir tamaño de generación
        
        self.pin_button.setIcon(self.pin_icon)
        self.pin_button.setIconSize(QSize(25, 25))  # Reducir tamaño de visualización
        
        # Modificar los tamaños base y de hover para la animación
        self.pin_button._icon_size = 25 # Tamaño base más pequeño
        self.pin_button._hover_size = 30  # Tamaño de hover más pequeño
        
        self.pin_button.setMinimumSize(30, 30)  # Reducir tamaño del botón
        self.pin_button.setMaximumSize(30, 30)  # Mantener consistencia
        
        self.pin_button.setToolTip("Fijar Ventana")
        self.pin_button.clicked.connect(self.toggle_window_pin)
        layout.addWidget(self.pin_button)
        
        layout.addStretch()
        self.properties_dialog = RAMPropertiesDialog(self)
        self.progress_bar.installEventFilter(self)
        
        # Cargar el estado inicial desde config.json
        config = self._load_config()
        self.is_pinned = config.get('window_topmost', False)
        
        # Aplicar el estado inicial
        if self.is_pinned:
            self.pin_button.setIcon(self.unpin_icon)
            self.pin_button.setToolTip("Desfijar Ventana")
            # Buscar la ventana principal
            main_window = self.parent()
            while main_window and not isinstance(main_window, QMainWindow):
                main_window = main_window.parent()
            
            if main_window:
                # Obtener el handle de la ventana
                hwnd = int(main_window.winId())
                
                # Establecer la ventana como TOPMOST
                win32gui.SetWindowPos(
                    hwnd, 
                    win32con.HWND_TOPMOST, 
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
                )
                
                # Iniciar el temporizador para mantener la ventana en primer plano
                if not hasattr(self, '_topmost_timer'):
                    self._topmost_timer = QTimer(self)
                    self._topmost_timer.timeout.connect(lambda: self._check_and_restore_topmost(hwnd))
                    self._topmost_timer.setInterval(5000)  # Verificar cada 5 segundos
                
                self._topmost_timer.start()
        else:
            self.pin_button.setIcon(self.pin_icon)
            self.pin_button.setToolTip("Fijar Ventana")
        
        # Reducir frecuencia de actualización a 2 segundos
        self._update_timer = QTimer(self)
        self._update_timer.timeout.connect(self.update_ram_usage)
        self._update_timer.setInterval(2000)
        self._update_timer.start()
        
        # Cachear valores de RAM
        self._last_ram_check = 0
        self._ram_cache = None
        self._cache_duration = 2  # segundos
        
        # Definir constantes de Windows
        self.HWND_TOPMOST = -1
        self.HWND_NOTOPMOST = -2
        self.SWP_NOMOVE = 0x0002
        self.SWP_NOSIZE = 0x0001
        self.SWP_NOACTIVATE = 0x0010
        
        # Crear una ventana fantasma para manejar el estado TOPMOST
        # Esta ventana nunca se muestra pero mantiene el estado TOPMOST
        self.ghost_window = None

    def toggle_window_pin(self):
        self.is_pinned = not self.is_pinned
        
        # Cambiar el icono según el estado
        if self.is_pinned:
            self.pin_button.setIcon(self.unpin_icon)
            # Limpiar tooltip existente y establecer uno nuevo
            self.pin_button._tooltip_text = "Desfijar Ventana"
            self.pin_button.setToolTip("Desfijar Ventana")
            if hasattr(self.pin_button, '_tooltip') and self.pin_button._tooltip:
                self.pin_button._tooltip.hide()
                self.pin_button._tooltip = None
        else:
            self.pin_button.setIcon(self.pin_icon)
            # Limpiar tooltip existente y establecer uno nuevo
            self.pin_button._tooltip_text = "Fijar Ventana"
            self.pin_button.setToolTip("Fijar Ventana")
            if hasattr(self.pin_button, '_tooltip') and self.pin_button._tooltip:
                self.pin_button._tooltip.hide()
                self.pin_button._tooltip = None
        
        # Buscar la ventana principal
        main_window = self.parent()
        while main_window and not isinstance(main_window, QMainWindow):
            main_window = main_window.parent()
        
        if not main_window:
            return
        
        # Obtener el handle de la ventana
        hwnd = int(main_window.winId())
        
        if self.is_pinned:
            # Establecer la ventana como TOPMOST usando HWND_TOPMOST
            # pero con SWP_NOACTIVATE para no interferir con el foco
            win32gui.SetWindowPos(
                hwnd, 
                win32con.HWND_TOPMOST, 
                0, 0, 0, 0,
                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
            )
            
            # Registrar el evento de activación de ventana a nivel de aplicación
            app = QApplication.instance()
            if app:
                # Desconectar el filtro de eventos anterior si existe
                try:
                    if hasattr(self, '_event_filter'):
                        app.removeEventFilter(self._event_filter)
                except Exception:
                    pass
                
                # Crear nuevo filtro de eventos para detectar cuando se abren diálogos/menús
                self._event_filter = _TopMostEventFilter(hwnd, main_window)
                app.installEventFilter(self._event_filter)
            
            # Ya no necesitamos el temporizador agresivo que interfiere con los menús
            if hasattr(self, '_topmost_timer') and self._topmost_timer.isActive():
                self._topmost_timer.stop()
                
            # En su lugar, usamos un timer con intervalo más largo solo para casos extremos
            if not hasattr(self, '_topmost_timer'):
                self._topmost_timer = QTimer(self)
                self._topmost_timer.timeout.connect(lambda: self._check_and_restore_topmost(hwnd))
                self._topmost_timer.setInterval(5000)  # Verificar cada 5 segundos
            
            self._topmost_timer.start()
        else:
            # Detener el temporizador
            if hasattr(self, '_topmost_timer') and self._topmost_timer.isActive():
                self._topmost_timer.stop()
            
            # Eliminar el filtro de eventos
            app = QApplication.instance()
            if app and hasattr(self, '_event_filter'):
                try:
                    app.removeEventFilter(self._event_filter)
                except Exception:
                    pass
                del self._event_filter
            
            # Establecer la ventana como NO TOPMOST
            win32gui.SetWindowPos(
                hwnd, 
                win32con.HWND_NOTOPMOST, 
                0, 0, 0, 0,
                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
            )
        
        # Guardar el estado en config.json
        self._save_topmost_state(self.is_pinned)

    def _check_and_restore_topmost(self, hwnd):
        """Verifica si la ventana debería ser TOPMOST y la restaura si es necesario,
        pero solo si no hay diálogos o menús activos."""
        if self.is_pinned:
            # Solo restauramos TOPMOST si no hay diálogos o menús visibles
            app = QApplication.instance()
            active_popup = None
            if app:
                active_popup = app.activePopupWidget()
                modal_widget = app.activeModalWidget()
                if not active_popup and not modal_widget:
                    # No hay popups activos, podemos restaurar TOPMOST
                    win32gui.SetWindowPos(
                        hwnd, 
                        win32con.HWND_TOPMOST, 
                        0, 0, 0, 0,
                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
                    )

    def _save_topmost_state(self, is_pinned):
        """Guarda el estado de TOPMOST en config.json"""
        try:
            # Cargar la configuración actual
            config = self._load_config()
            
            # Actualizar el estado de TOPMOST
            config['window_topmost'] = is_pinned
            
            # Guardar la configuración
            with open('config.json', 'w') as f:
                json.dump(config, f, indent=4)
            
            print(f"Estado de ventana fijada guardado: {is_pinned}")
        except Exception as e:
            print(f"Error al guardar el estado de ventana fijada: {e}")

    def _load_config(self):
        """Carga la configuración desde config.json"""
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            return {'mode': 'dark', 'window_topmost': False}
        except json.JSONDecodeError:
            return {'mode': 'dark', 'window_topmost': False}

    def update_ram_usage(self):
        current_time = time.time()
        # Usar caché si está disponible
        if self._ram_cache and (current_time - self._last_ram_check) < self._cache_duration:
            ram = self._ram_cache
        else:
            ram = psutil.virtual_memory()
            self._ram_cache = ram
            self._last_ram_check = current_time
        self.progress_bar.setValue(int(ram.percent))

    def eventFilter(self, obj, event):
        if obj == self.progress_bar and event.type() == QEvent.Type.Enter:
            pos = self.mapToGlobal(self.progress_bar.geometry().topLeft())
            pos.setY(pos.y() - 100)
            self.properties_dialog.show_properties(pos)
        return super().eventFilter(obj, event)

class _TopMostEventFilter(QObject):
    """Filtro de eventos que maneja la interacción entre ventanas TOPMOST y diálogos/menús."""
    
    def __init__(self, main_hwnd, main_window):
        super().__init__()
        self.main_hwnd = main_hwnd
        self.main_window = main_window
        self.popup_active = False
        self.popup_hwnd = None
        
    def eventFilter(self, obj, event):
        # Detectar cuando se crea un diálogo o menú
        if event.type() == QEvent.Type.WindowActivate:
            # Si se activa un widget que no es la ventana principal, probablemente es un diálogo o menú
            if obj is not self.main_window and (obj.windowFlags() & (Qt.WindowType.Dialog | Qt.WindowType.Popup)):
                self.popup_active = True
                self.popup_hwnd = int(obj.winId())
                
                # Hacer que el popup sea TOPMOST (por encima de la ventana principal)
                try:
                    win32gui.SetWindowPos(
                        self.popup_hwnd,
                        win32con.HWND_TOPMOST,
                        0, 0, 0, 0,
                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
                    )
                except Exception:
                    pass
                
        # Detectar cuando se cierra un diálogo o menú
        elif event.type() == QEvent.Type.WindowDeactivate:
            if self.popup_active and obj is not self.main_window:
                # Restaurar la ventana principal como TOPMOST después de un pequeño retraso
                QTimer.singleShot(100, self._restore_main_topmost)
        
        return False  # Siempre dejar que el evento se procese normalmente
    
    def _restore_main_topmost(self):
        """Restaura la ventana principal como TOPMOST cuando se cierra un diálogo."""
        self.popup_active = False
        try:
            # Verificar si la ventana principal aún existe
            if win32gui.IsWindow(self.main_hwnd):
                win32gui.SetWindowPos(
                    self.main_hwnd,
                    win32con.HWND_TOPMOST,
                    0, 0, 0, 0,
                    win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_NOACTIVATE
                )
        except Exception:
            pass















